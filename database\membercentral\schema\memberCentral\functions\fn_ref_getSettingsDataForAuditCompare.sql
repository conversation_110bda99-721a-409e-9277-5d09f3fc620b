ALTER FUNCTION dbo.fn_ref_getSettingsDataForAuditCompare(@siteID int, @referralID int)
RETURNS @tblSettings TABLE (
	/* EmailNotifications */
	title varchar(255), referralSearchFS varchar(200), referralResultFS varchar(200), clientFeeResultFS varchar(200), clientEmailFS varchar(200),
	panelMemberChecklistFS varchar(200), defaultState varchar(100), defaultCity varchar(100), clientReferralType varchar(100), defaultClientSurvey bit,
	defRefRevAcct varchar(500), defAdminClientFeesRevAcct varchar(500), clientFeeMember varchar(300), paymentProfilesList varchar(max),
	counselorAssgnGrp varchar(500), rotationByPanel bit, isPanelGroupDepend bit, dspImportClientReferralID bit, deductFilingFee bit,
	dspLegalDescription bit, collectClientFeeFE bit, allowFeeDiscrepancy bit, statusesToAssessForFeeDescrp varchar(max),
	feeDiscrepancyAmt decimal(12,2), collectClientFeeFEOverrideTxt varchar(255),
	/* EmailNotifications */
	emailRecipient varchar(200), emailTypeList varchar(max),
	refNotificationClientET varchar(200), clientMailTopTxt varchar(max), clientMailBottomTxt varchar(max),
	refNotificationMemberET varchar(200), memberMailTopTxt varchar(max), memberMailBottomTxt varchar(max),
	dspNewsletterLink bit, clientReceiptET varchar(200),
	systemMailCaseActivityTxt varchar(max), LawyerOptOutRefUpdateNoticeGroup varchar(500),
	emailAgencyInfoToClient bit, emailAgencyInfoToClientTopTxt varchar(max), emailAgencyInfoToClientBottomTxt varchar(max),
	/* PanelSettings */
	dspPanelList bit, rcPanelInstructionsTxt varchar(max), allowPanelMgmt bit, dspEmailPanelList varchar(50), maxNumberOfPanels int,
	panelStatus varchar(255), panelApplicationStatus varchar(255), recalcDueFromTotalFees bit, refFeeStructure varchar(max),
	/* ReferralCenter */
	dspLessFilingFeeCosts bit, feeDeclarationTxt varchar(255), feeInformationTopTxt varchar(max), dspForwardingFeeAmount bit,
	referralStatementInstructions varchar(max), allowFeeTypeMgmt bit, monthlyReportTopTxt varchar(max),  monthlyReportBottomTxt varchar(max),
	dspMonthReportLink bit,
	/* FrontEndReferrals */
	searchFilterFS varchar(200), resultsScreenFS varchar(200), feFormInstr varchar(max), feLegalDescInstr varchar(max), fePanelInstr varchar(max),
	feNoResultsInstr varchar(max), feCounselor varchar(300), feMaxMemberRefNum int, fePendingStatus varchar(255), feReferredStatus varchar(255),
	fePayProfile varchar(100), feFeesRevenueAcct varchar(500), feDspSurveyOption bit, feSurveyOptionDefaultYes bit, feDspBlogOption bit,
	hideRepFieldsFE bit, feDspQuestionTree bit, feDspClientInfoFormFirst bit,
	fePanelSelectionInstr varchar(max), feRefInfoMatchInstr varchar(max), feContactInfoInstr varchar(max), feRefResultsInstr varchar(max),
	feLegalIssueDescTitle varchar(max), feAdditionalFiltersTitle varchar(max), feReferralInforMatchTitle varchar(max), feAdditionalInfoTitle varchar(max),
	feSuccessfulResultsInstr varchar(max),
	/* IntakeManagement */
	feDspLimitNumOfReferrals bit, feMaxRefPerClient int, feFreqRefPerClient varchar(10), referralFormFieldName varchar(max), feDuplicateReferralTxt varchar(max),
	feLegalDescLimitWords bit, feLegalDescLimitWordCount int, feLegalDescLimitExceedMsg varchar(300)
)
AS
BEGIN

	DECLARE @orgID int, @siteResourceID int, @applicationInstanceID int, @feSiteResourceID int, @crlf varchar(10);

	DECLARE @tblOrgGLs TABLE (GLAccountID int, thePathExpanded varchar(max));
	DECLARE @tblMFUsages TABLE (area varchar(20), fieldsetName varchar(200));

	SET @crlf = char(13) + char(10);

	SELECT @applicationInstanceID = r.applicationInstanceID, @siteResourceID = ai.siteResourceID,
		@feSiteResourceID = aiFE.siteResourceID
	FROM dbo.ref_referrals AS r
	INNER JOIN dbo.cms_applicationInstances AS ai ON ai.siteID = @siteID
		AND ai.applicationInstanceID = r.applicationInstanceID
	INNER JOIN dbo.cms_applicationInstances AS aiFE ON aiFE.siteID = @siteID
		AND aiFE.applicationInstanceID = r.feApplicationInstanceID
	WHERE r.referralID = @referralID;

	SELECT @orgID = dbo.fn_getOrgIDFromSiteID(@siteID);

	INSERT INTO @tblOrgGLs (GLAccountID, thePathExpanded)
	SELECT GLAccountID, thePathExpanded
	FROM dbo.fn_getRecursiveGLAccounts(@orgID);

	/* field usages */
	INSERT INTO @tblMFUsages (area, fieldsetName)
	SELECT TOP 1 fsu1.area, fs1.fieldsetName
	FROM dbo.ams_memberFieldUsage AS fsu1
	INNER JOIN dbo.ams_memberFieldSets AS fs1 ON fs1.fieldsetID = fsu1.fieldsetID
		AND fs1.siteID = @siteID
	WHERE fsu1.siteResourceID = @siteResourceID
	AND fsu1.area = 'referralsearch'
	ORDER BY fsu1.fieldsetorder, fsu1.fieldsetID;
	
	INSERT INTO @tblMFUsages (area, fieldsetName)
	SELECT TOP 1 fu.area, fs.fieldsetName
	FROM dbo.ams_memberFieldUsage AS fu
	INNER JOIN dbo.ams_memberFieldSets AS fs ON fs.fieldsetID = fu.fieldsetID
		AND fs.siteID = @siteID
	WHERE fu.siteResourceID = @siteResourceID
	AND fu.area = 'referralresult'
	ORDER BY fu.fieldsetorder, fu.fieldsetID;

	INSERT INTO @tblMFUsages (area, fieldsetName)
	SELECT TOP 1 fu.area, fs.fieldsetName
	FROM dbo.ams_memberFieldUsage AS fu
	INNER JOIN dbo.ams_memberFieldSets AS fs ON fs.fieldsetID = fu.fieldsetID
		AND fs.siteID = @siteID
	WHERE fu.siteResourceID = @siteResourceID
	AND fu.area = 'clientfeeresult'
	ORDER BY fu.fieldsetorder, fu.fieldsetID;

	INSERT INTO @tblMFUsages (area, fieldsetName)
	SELECT TOP 1 fu.area, fs.fieldsetName
	FROM dbo.ams_memberFieldUsage AS fu
	INNER JOIN dbo.ams_memberFieldSets AS fs ON fs.fieldsetID = fu.fieldsetID
		AND fs.siteID = @siteID
	WHERE fu.siteResourceID = @siteResourceID
	AND fu.area = 'clientemail'
	ORDER BY fu.fieldsetorder, fu.fieldsetID;

	INSERT INTO @tblMFUsages (area, fieldsetName)
	SELECT TOP 1 fu.area, fs.fieldsetName
	FROM dbo.ams_memberFieldUsage AS fu
	INNER JOIN dbo.ams_memberFieldSets AS fs ON fs.fieldsetID = fu.fieldsetID
		AND fs.siteID = @siteID
	WHERE fu.siteResourceID = @siteResourceID
	AND fu.area = 'panelmemberchecklist'
	ORDER BY fu.fieldsetorder, fu.fieldsetID;

	INSERT INTO @tblMFUsages (area, fieldsetName)
	SELECT TOP 1 fu.area, fs.fieldsetName
	FROM dbo.ams_memberFieldUsage AS fu
	INNER JOIN dbo.ams_memberFieldSets AS fs ON fs.fieldsetID = fu.fieldsetID
		AND fs.siteID = @siteID
	WHERE fu.siteResourceID = @feSiteResourceID
	AND fu.area = 'clientreferralsearch'
	ORDER BY fu.fieldsetorder, fu.fieldsetID;

	INSERT INTO @tblMFUsages (area, fieldsetName)
	SELECT TOP 1 fu.area, fs.fieldsetName
	FROM dbo.ams_memberFieldUsage AS fu
	INNER JOIN dbo.ams_memberFieldSets AS fs ON fs.fieldsetID = fu.fieldsetID
		AND fs.siteID = @siteID
	WHERE fu.siteResourceID = @feSiteResourceID
	AND fu.area = 'clientreferralresult'
	ORDER BY fu.fieldsetorder, fu.fieldsetID;

	INSERT INTO @tblSettings
	SELECT TOP 1 r.title, fs1.fieldsetName AS referralSearchFS, fs2.fieldsetName AS referralResultFS,
		fs3.fieldsetName AS clientFeeResultFS, fs4.fieldsetName AS clientEmailFS, fs5.fieldsetName AS panelMemberChecklistFS,
		s.[Name] as defaultState, r.defaultCity, crt.clientReferralType, r.defaultClientSurvey,
		gl1.thePathExpanded AS defRefRevAcct, gl2.thePathExpanded AS defAdminClientFeesRevAcct,
		mActive.firstName + ' ' + mActive.lastName + ' (' + mActive.memberNumber + ')' AS clientFeeMember,
		paymentProfilesList = STUFF((
				SELECT ', ' + p.profileName
				FROM dbo.ref_merchantProfiles AS rmp
				INNER JOIN mp_profiles AS p ON p.profileID = rmp.merchantProfileID
				WHERE rmp.applicationInstanceID = @applicationInstanceID
				ORDER BY p.frontEndOrderBy
				FOR XML PATH ('')
			),1,2,''),
		g1.groupPathExpanded AS [CounselorAssgnGrp],
		r.rotationByPanel, r.isPanelGroupDepend, r.dspImportClientReferralID, r.deductFilingFee,
		r.dspLegalDescription, r.collectClientFeeFE, r.allowFeeDiscrepancy,
		statusesToAssessForFeeDescrp = STUFF((
				SELECT ', ' + statusName
				FROM dbo.ref_clientReferralStatus 
				WHERE referralID = @referralID
				AND isFeeDiscrepancyAssess = 1
				ORDER BY statusName
				FOR XML PATH ('')
			),1,2,''),
		r.feeDiscrepancyAmt, r.collectClientFeeFEOverrideTxt,
		/* EmailNotifications */
		r.emailRecipient,
		emailTypeList = STUFF((
				SELECT ', ' + et.emailType
				FROM dbo.ref_memberEmailTypes AS ret
				INNER JOIN dbo.ams_memberEmailTypes AS et ON et.emailTypeID = ret.emailTypeID
				WHERE ret.referralID = @referralID
				ORDER BY et.emailType
				FOR XML PATH ('')
			),1,2,''),
		et1.templateName AS refNotificationClientET, r.clientMailTopTxt, r.clientMailBottomTxt,
		et2.templateName AS refNotificationMemberET, r.memberMailTopTxt, r.memberMailBottomTxt,
		r.dspNewsletterLink,
		et3.templateName AS clientReceiptET,
		r.systemMailCaseActivityTxt, g2.groupPathExpanded AS [LawyerOptOutRefUpdateNoticeGroup],
		r.emailAgencyInfoToClient, r.emailAgencyInfoToClientTopTxt, r.emailAgencyInfoToClientBottomTxt,
		/* PanelSettings */
		r.dspPanelList, r.rcPanelInstructionsTxt, r.allowPanelMgmt,
		CASE r.dspEmailPanelList WHEN 'A' THEN 'Show all panels' WHEN 'S' THEN 'Only show selected panels' WHEN 'N' THEN 'Show no panels' ELSE '' END AS dspEmailPanelList,
		r.maxNumberOfPanels, rpms.statusName AS panelStatus, rpmas.statusName AS panelApplicationStatus,
		CASE WHEN rfst.feeStructureTypeID IS NOT NULL THEN 1 ELSE 0 END AS recalcDueFromTotalFees,
		refFeeStructure = REPLACE(STUFF((
				SELECT '|' + CAST(ROW_NUMBER() OVER (ORDER BY levelID) AS VARCHAR(10)) + ') Amt:' + CAST(l.cumulativeChargedAmount AS VARCHAR(20)) + ', Fee: ' + CAST(l.cumulativeFeePercent AS VARCHAR(20)) + '%, Range Initial Charge Fee: ' + CAST(l.rangeInitialChargeFeePercent AS VARCHAR(20)) + '%'
				FROM dbo.ref_feeStructureLevels AS l 
				INNER JOIN dbo.ref_feeStructures AS fs ON fs.feeStructureID = l.feeStructureID
				AND fs.[status] = 'A'
				AND fs.referralID = @referralID
				AND fs.panelID IS NULL
				ORDER BY l.levelID
				FOR XML PATH ('')
			),1,1,''),'|',@crlf),
		/* ReferralCenter */
		r.dspLessFilingFeeCosts, r.feeDeclarationTxt, r.feeInformationTopTxt, r.dspForwardingFeeAmount,
		r.referralStatementInstructions, r.allowFeeTypeMgmt, r.monthlyReportTopTxt,  r.monthlyReportBottomTxt,
		r.dspMonthReportLink,
		/* FrontEndReferrals */
		fs6.fieldsetName AS searchFilterFS, fs7.fieldsetName AS resultsScreenFS, feFormInstrContent.rawContent AS feFormInstr,
		feLegalDescInstrContent.rawContent AS feLegalDescInstr, fePanelInstrContent.rawContent AS fePanelInstr,
		feNoResultsInstrContent.rawContent AS feNoResultsInstr,
		m2Active.firstName + ' ' + m2Active.lastName + ' (' + m2Active.memberNumber + ')' AS feCounselor,
		r.feMaxMemberRefNum, feps.statusName AS fePendingStatuss, fers.statusName AS feReferredStatus,
		pr.profileName as fePayProfile, gl3.thePathExpanded AS feFeesRevenueAcct,
		ISNULL(r.feDspSurveyOption,0), ISNULL(r.feSurveyOptionDefaultYes,0), ISNULL(r.feDspBlogOption,0),
		r.hideRepFieldsFE, r.feDspQuestionTree, r.feDspClientInfoFormFirst,
		feFormInstrStep1Content.rawContent AS fePanelSelectionInstr, feFormInstrStep2Content.rawContent AS feRefInfoMatchInstr,
		feFormInstrStep3Content.rawContent AS feContactInfoInstr, feFormInstrStep4Content.rawContent AS feRefResultsInstr,
		r.feLegalIssueDescTitle, r.feAdditionalFiltersTitle, r.feReferralInforMatchTitle, r.feAdditionalInfoTitle,
		feSuccessfulResultsInstrContent.rawContent AS feSuccessfulResultsInstr,
		/* IntakeManagement */
		ISNULL(r.feDspLimitNumOfReferrals,0), r.feMaxRefPerClient,
		CASE r.feFreqRefPerClient WHEN 'D' THEN 'Day' WHEN 'W' THEN 'Week' WHEN 'M' THEN 'Month' ELSE '' END AS feFreqRefPerClient,
		rff.referralFormFieldName, r.feDuplicateReferralTxt,
		r.feLegalDescLimitWords, r.feLegalDescLimitWordCount, r.feLegalDescLimitExceedMsg
	FROM dbo.ref_referrals AS r
	LEFT OUTER JOIN dbo.ams_states AS s ON s.stateID = r.defaultStateID
	LEFT OUTER JOIN dbo.ref_clientReferralTypes AS crt ON crt.referralID = @referralID
		AND crt.clientReferralTypeID = r.defaultCallTypeId
	LEFT OUTER JOIN @tblOrgGLs AS gl1 ON gl1.GLAccountID = r.GLAccountID
	LEFT OUTER JOIN @tblOrgGLs AS gl2 ON gl2.GLAccountID = r.clientGLAccountID
	LEFT OUTER JOIN @tblOrgGLs AS gl3 ON gl3.GLAccountID = r.feGLAccountID
	LEFT OUTER JOIN dbo.ams_members AS m
		INNER JOIN dbo.ams_members AS mActive ON mActive.memberID = m.activeMemberID
		ON m.orgID = @orgID
		AND m.memberID =  r.clientFeeMemberID
	LEFT OUTER JOIN dbo.ams_members AS m2
		INNER JOIN dbo.ams_members AS m2Active ON m2Active.memberID = m2.activeMemberID
		ON m2.orgID = @orgID
		AND m2.memberID =  r.feCounselorID
	LEFT OUTER JOIN dbo.ams_groups AS g1 ON g1.orgID = @orgID
		AND g1.groupID = r.counselorGroupID
	LEFT OUTER JOIN dbo.et_emailTemplates AS et1 ON et1.templateID = r.clientEmailTemplateId
	LEFT OUTER JOIN dbo.et_emailTemplates AS et2 ON et2.templateID = r.memberEmailTemplateId
	LEFT OUTER JOIN dbo.et_emailTemplates AS et3 ON et3.templateID = r.receiptEmailTemplateId
	LEFT OUTER JOIN dbo.ams_groups AS g2 ON g2.orgID = @orgID
		AND g2.groupID = r.memEmailOptOutGroupID
	LEFT OUTER JOIN dbo.ref_panelMemberStatus AS rpms ON rpms.referralID = @referralID
		AND rpms.panelMemberStatusID = r.panelMemberStatusID
	LEFT OUTER JOIN dbo.ref_panelMemberAppStatus AS rpmas ON rpmas.referralID = @referralID
		AND rpmas.panelMemberAppStatusID = r.panelMemberApplicationStatusID
	LEFT OUTER JOIN dbo.ref_feeStructureTypes AS rfst ON rfst.feeStructureTypeID = r.feeStructureTypeID
		AND rfst.feeStructureTypeName = 'totals'
	LEFT OUTER JOIN ref_clientReferralStatus AS feps ON feps.referralID = @referralID
		AND feps.clientReferralStatusID = r.fePendingStatusID
	LEFT OUTER JOIN ref_clientReferralStatus AS fers ON fers.referralID = @referralID
		AND fers.clientReferralStatusID = r.feReferredStatusID
	LEFT OUTER JOIN dbo.ref_merchantProfiles mp 
		INNER JOIN dbo.mp_profiles AS pr ON pr.profileID = mp.merchantProfileID
		ON mp.applicationInstanceID = r.feApplicationInstanceID
	LEFT OUTER JOIN (
		SELECT 
			r.referralID,
			rff1.referralFormFieldName
		FROM 
			ref_referrals r
		CROSS APPLY 
			dbo.fn_intListToTable(r.feIntakeFieldID, ',') AS s
		LEFT JOIN 
			ref_referralFormFields rff1 ON CAST(s.listItem AS INT) = rff1.referralFormFieldID
	) rff ON rff.referralID = r.referralID
	CROSS APPLY dbo.fn_getContent(r.feFormInstructionsContentID,1) AS feFormInstrContent
	CROSS APPLY dbo.fn_getContent(r.feLegalDescInstructContentID,1) AS feLegalDescInstrContent
	CROSS APPLY dbo.fn_getContent(r.fePanelInfoContentID,1) AS fePanelInstrContent
	CROSS APPLY dbo.fn_getContent(r.feNoResultsInfoContentID,1) AS feNoResultsInstrContent
	CROSS APPLY dbo.fn_getContent(r.feFormInstructionsStep1ContentID,1) AS feFormInstrStep1Content
	CROSS APPLY dbo.fn_getContent(r.feFormInstructionsStep2ContentID,1) AS feFormInstrStep2Content
	CROSS APPLY dbo.fn_getContent(r.feFormInstructionsStep3ContentID,1) AS feFormInstrStep3Content
	CROSS APPLY dbo.fn_getContent(r.feFormInstructionsStep4ContentID,1) AS feFormInstrStep4Content
	CROSS APPLY dbo.fn_getContent(r.feSuccessfulResultsInstructionsContentID,1) as feSuccessfulResultsInstrContent
	LEFT OUTER JOIN @tblMFUsages AS fs1 ON fs1.area = 'referralsearch'
	LEFT OUTER JOIN @tblMFUsages AS fs2 ON fs2.area = 'referralresult'
	LEFT OUTER JOIN @tblMFUsages AS fs3 ON fs3.area = 'clientfeeresult'
	LEFT OUTER JOIN @tblMFUsages AS fs4 ON fs4.area = 'clientemail'
	LEFT OUTER JOIN @tblMFUsages AS fs5 ON fs5.area = 'panelmemberchecklist'
	LEFT OUTER JOIN @tblMFUsages AS fs6 ON fs6.area = 'clientreferralsearch'
	LEFT OUTER JOIN @tblMFUsages AS fs7 ON fs7.area = 'clientreferralresult'
	WHERE r.referralID = @referralID;

	RETURN;
END
GO

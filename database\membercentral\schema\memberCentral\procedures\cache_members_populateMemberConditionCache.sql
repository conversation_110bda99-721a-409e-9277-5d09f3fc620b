ALTER PROC dbo.cache_members_populateMemberConditionCache
@triggerLogID bigint,
@allMembers bit,
@testing bit = 0

as

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @runID bigint, @conditionCount int, @memberCount int, @numChangesMade int,
		@conditionKey_afRunDate int, @tasksCount int, @acctCount int, @subCount int, @listCount int, @rtCount int, 
		@mhCount int, @subProc varchar(30), @subProc2 varchar(200), @start datetime, @totalMS int, 
		@subprocConditionCount int, @annivCount int, @relCount int, @mnCount int, @fundCount int,
		@evCount int, @suppListCount int, @swCount int, @clCount int, @madCount int, @refCount int,
		@orgID int, @dateTriggered datetime;

	declare @dateStarted DATETIME = getdate();

	-- temp tables need to be there
	IF OBJECT_ID('tempdb..#tblMCQCondCacheMem') IS NULL
		RAISERROR('holding table #tblMCQCondCacheMem does not exist.',16,1);
	IF OBJECT_ID('tempdb..#tblMCQCondCacheCond') IS NULL
		RAISERROR('holding table #tblMCQCondCacheCond does not exist.',16,1);
	IF @testing = 0 AND OBJECT_ID('tempdb..#tmpMCQCondCacheChanges') IS NULL
		RAISERROR('holding table #tmpMCQCondCacheChanges does not exist.',16,1);
	IF @testing = 1 AND OBJECT_ID('tempdb..#tmpMCQTestCondMembers') IS NULL
		RAISERROR('holding table #tmpMCQTestCondMembers does not exist.',16,1);

	select @conditionCount = count(*) from #tblMCQCondCacheCond;
	select @memberCount = count(*) from #tblMCQCondCacheMem;
	SELECT TOP 1 @orgID = orgID from #tblMCQCondCacheCond;

	IF @testing = 0
		SELECT @dateTriggered = dateTriggered from platformstatsMC.dbo.cache_conditionsTriggerLog WHERE logID = @triggerLogID

	-- if no rows, no need to continue
	IF @conditionCount = 0 or @memberCount = 0 GOTO on_done;

	/* ************************************* */
	/* Log the run in the various log tables */ 
	/* ************************************* */
	IF @testing = 0 BEGIN
		-- log the conditions run
		INSERT INTO platformStatsMC.dbo.cache_conditionsLogRun (triggerLogID, orgID, runDate, msQueued, conditionCount, memberCount) VALUES (@triggerLogID, @orgID, @dateStarted, datediff(MILLISECOND,@dateTriggered,@dateStarted), @conditionCount, @memberCount);
		select @runID = SCOPE_IDENTITY();

		-- what members were we asked to run
		SET @start = getdate();
		IF @allMembers = 1
			INSERT INTO platformStatsMC.dbo.cache_conditionsLogRunMembers (runID, memberID)
			VALUES (@runID, 0);
		ELSE
			INSERT INTO platformStatsMC.dbo.cache_conditionsLogRunMembers (runID, memberID)
			SELECT @runID, memberID FROM #tblMCQCondCacheMem;

		SET @totalMS = datediff(ms,@start,getdate());
		IF @testing = 0
			INSERT INTO platformStatsMC.dbo.cache_conditionsLogRunProcs (procname, timeMS, runID, numConditions) VALUES ('getMembers', @totalMS, @runID, 0);



		-- what conditions were we asked to run
		SET @start = getdate();

		INSERT INTO platformStatsMC.dbo.cache_conditionsLogRunConditions (runID, conditionID)
		SELECT @runID, conditionID FROM #tblMCQCondCacheCond;


		SET @totalMS = datediff(ms,@start,getdate());
		IF @testing = 0
			INSERT INTO platformStatsMC.dbo.cache_conditionsLogRunProcs (procname, timeMS, runID, numConditions) VALUES ('getConditions', @totalMS, @runID, @conditionCount);
	END

	-- get all conditions to calculate
	IF OBJECT_ID('tempdb..#tblCondALL') IS NOT NULL
		DROP TABLE #tblCondALL;
	CREATE TABLE #tblCondALL (conditionID int PRIMARY KEY, orgID int INDEX IX_tblCondALL_orgID, expression varchar(20), fieldCode varchar(40), 
		displayTypeCode varchar(20), dataTypeCode varchar(20), fieldCodeAreaID int INDEX IX_tblCondALL_fieldCodeAreaID, 
		fieldCodeAreaPartA varchar(20) INDEX IX_tblCondALL_fieldCodeAreaPartA, subProc varchar(30), 
		processArea varchar(25), processValuesSection tinyint,
		INDEX IX_tblCondALL_processArea_expression_dataTypeCode NONCLUSTERED (processArea ASC, expression ASC, dataTypeCode ASC)
	);

	SET @start = getdate();

	INSERT INTO #tblCondALL (conditionID, orgID, expression, fieldCode, displayTypeCode, dataTypeCode, fieldCodeAreaID, 
		fieldCodeAreaPartA, subProc, processArea, processValuesSection)
	select c.conditionID, c.orgID, e.expression, c.fieldCode, dit.displayTypeCode, dat.dataTypeCode,
		fieldCodeAreaID = case 
		when left(c.fieldCode,3) = 'md_' then cast(replace(c.fieldcode,'md_','') as int)
		when left(c.fieldcode,4) = 'mpl_' then cast(parsename(replace(c.fieldcode,'_','.'),2) as int)
		when left(c.fieldCode,3) = 'ma_' OR left(c.fieldCode,4) = 'mat_' then cast(parsename(replace(c.fieldcode,'_','.'),2) as int)
		when left(c.fieldCode,3) = 'mp_' OR left(c.fieldCode,4) = 'mpt_' then cast(parsename(replace(c.fieldcode,'_','.'),1) as int)
		when left(c.fieldCode,4) = 'mad_' OR left(c.fieldCode,5) = 'madt_' then cast(parsename(replace(c.fieldcode,'_','.'),1) as int)
		when left(c.fieldCode,3) = 'me_' OR left(c.fieldCode,4) = 'met_' then cast(parsename(replace(c.fieldcode,'_','.'),2) as int)
		when left(c.fieldCode,3) = 'mw_' then cast(parsename(replace(c.fieldcode,'_','.'),2) as int)
		else null end,
		fieldCodeAreaPartA = case
		when left(c.fieldcode,4) = 'mpl_' then cast(parsename(replace(c.fieldcode,'_','.'),1) as varchar(20))
		when left(c.fieldCode,3) = 'ma_' OR left(c.fieldCode,4) = 'mat_' then cast(parsename(replace(c.fieldcode,'_','.'),1) as varchar(20))
		when left(c.fieldCode,3) = 'mp_' OR left(c.fieldCode,4) = 'mpt_' then cast(parsename(replace(c.fieldcode,'_','.'),2) as varchar(10))
		when left(c.fieldCode,4) = 'mad_' OR left(c.fieldCode,5) = 'madt_' then cast(parsename(replace(c.fieldcode,'_','.'),2) as varchar(10))
		else null end,
		c.subProc, c.processArea, c.processValuesSection
	from dbo.ams_virtualGroupConditions as c
	inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.expressionID
	inner join dbo.ams_memberDataColumnDataTypes as dat on dat.dataTypeID = c.dataTypeID
	inner join dbo.ams_memberDataColumnDisplayTypes as dit on dit.displayTypeID = c.displayTypeID
	inner join #tblMCQCondCacheCond as tblC on tblc.conditionID = c.conditionID
	where c.subProc <> '';


	SET @totalMS = datediff(ms,@start,getdate());
	IF @testing = 0
		INSERT INTO platformStatsMC.dbo.cache_conditionsLogRunProcs (procname, timeMS, runID, numConditions) VALUES ('populate_tblCondALL', @totalMS, @runID, @conditionCount);

	-- for the final processing at the end
	IF OBJECT_ID('tempdb..#tblCondALLFinal') IS NOT NULL
		DROP TABLE #tblCondALLFinal;
	CREATE TABLE #tblCondALLFinal (conditionID int PRIMARY KEY);

	SET @start = getdate();

	insert into #tblCondALLFinal
	select conditionID from #tblCondALL;

	SET @totalMS = datediff(ms,@start,getdate());
	IF @testing = 0
		INSERT INTO platformStatsMC.dbo.cache_conditionsLogRunProcs (procname, timeMS, runID, numConditions) VALUES ('populate_tblCondALLFinal', @totalMS, @runID, @conditionCount);

	-- exclude afRunDate from the table of conditions to run
	select @conditionKey_afRunDate = conditionKeyID from dbo.ams_virtualGroupConditionKeys where conditionKey = 'afRunDate';

	-- put condition values into temp table by datatype so we don't need to cast the values in individual processing queries
	IF OBJECT_ID('tempdb..#tblCondValues') IS NOT NULL
		DROP TABLE #tblCondValues;
	CREATE TABLE #tblCondValues (
		conditionID int INDEX IX_tblCondValues_conditionID, conditionKeyID int INDEX IX_tblCondValues_conditionKeyID, 
		conditionKey varchar(50), conditionValueString varchar(100), 
		conditionValueInteger int, conditionValueBit bit, conditionValueDecimal2 decimal(14,2), conditionValueDate datetime,
		INDEX IX_tblCondValues__conditionValueString NONCLUSTERED (conditionValueString) INCLUDE (conditionID),
		INDEX IX_tblCondValues__conditionValueBit__conditionID NONCLUSTERED (conditionValueBit,conditionID),
		INDEX IX_tblCondValues__conditionKey__conditionID__conditionValueString NONCLUSTERED (conditionKey,conditionID,conditionValueString)
	);


	SET @start = getdate();

	-- get all conditions to calculate
	IF OBJECT_ID('tempdb..#tblCondValuesRaw') IS NOT NULL
		DROP TABLE #tblCondValuesRaw;
	CREATE TABLE #tblCondValuesRaw (conditionID int INDEX IX_tblCondValuesRaw_conditionID , conditionKeyID int  INDEX IX_tblCondValuesRaw_conditionKeyID, conditionValue varchar(100), conditionKey varchar(50))


	insert into #tblCondValuesRaw (conditionID, conditionKeyID, conditionValue,conditionKey)
	select cv.conditionID, cv.conditionKeyID, cv.conditionValue, ck.conditionKey
	from dbo.ams_virtualGroupConditionValues as cv
	inner join #tblCondALL as tblC on tblC.conditionID = cv.conditionID
	inner join dbo.ams_virtualGroupConditionKeys as ck on ck.conditionKeyID = cv.conditionKeyID
	where cv.conditionKeyID <> @conditionKey_afRunDate;


	insert into #tblCondValues (conditionID, conditionKeyID, conditionKey, conditionValueString, conditionValueInteger, conditionValueBit, conditionValueDecimal2, conditionValueDate)
	select cv.conditionID, cv.conditionKeyID,cv.conditionKey, null, cv.conditionValue, null, null, null
	from #tblCondValuesRaw as cv
	inner join #tblCondALL as tblC on tblC.conditionID = cv.conditionID
	where tblC.processValuesSection = 1
		union all
	select cv.conditionID, cv.conditionKeyID,cv.conditionKey, cv.conditionValue, null, null, null, null
	from #tblCondValuesRaw as cv
	inner join #tblCondALL as tblC on tblC.conditionID = cv.conditionID
	where tblC.processValuesSection = 2
		union all
	select cv.conditionID, cv.conditionKeyID,cv.conditionKey, null, null, cv.conditionValue, null, null
	from #tblCondValuesRaw as cv
	inner join #tblCondALL as tblC on tblC.conditionID = cv.conditionID
	where tblC.processValuesSection = 3
		union all
	select cv.conditionID, cv.conditionKeyID,cv.conditionKey, null, null, null, cv.conditionValue, null
	from #tblCondValuesRaw as cv
	inner join #tblCondALL as tblC on tblC.conditionID = cv.conditionID
	where tblC.processValuesSection = 4
		union all
	select cv.conditionID, cv.conditionKeyID,cv.conditionKey, null, null, null, null, cv.conditionValue
	from #tblCondValuesRaw as cv
	inner join #tblCondALL as tblC on tblC.conditionID = cv.conditionID
	where tblC.processValuesSection = 5
		union all
	select cv.conditionID, cv.conditionKeyID,cv.conditionKey, cv.conditionValue, null, null, null, null
	from #tblCondValuesRaw as cv
	inner join #tblCondALL as tblC on tblC.conditionID = cv.conditionID
	where tblC.subProc = 'SUB_SUBSCRIBED';

	IF OBJECT_ID('tempdb..#tblCondValuesRaw') IS NOT NULL
		DROP TABLE #tblCondValuesRaw;

	SET @totalMS = datediff(ms,@start,getdate());
	IF @testing = 0
		INSERT INTO platformStatsMC.dbo.cache_conditionsLogRunProcs (procname, timeMS, runID, numConditions) VALUES ('populate_tblCondValues', @totalMS, @runID, @conditionCount);

	/* ****** */
	/* SPLITS */
	/* ****** */
	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

	-- split ACCT data used by several subProcs
	select @acctCount = count(*) from #tblCondALL where processArea = 'Accounting';
	IF @acctCount > 0 BEGIN
		IF OBJECT_ID('tempdb..#tblAccSplitSubProcTypes') IS NOT NULL
			DROP TABLE #tblAccSplitSubProcTypes;
		IF OBJECT_ID('tempdb..#tblAccSplit') IS NOT NULL
			DROP TABLE #tblAccSplit;
		IF OBJECT_ID('tempdb..#tblAccSplitGL') IS NOT NULL
			DROP TABLE #tblAccSplitGL;
		IF OBJECT_ID('tempdb..#tblAccSplitMP') IS NOT NULL
			DROP TABLE #tblAccSplitMP;
		IF OBJECT_ID('tempdb..#tblAcctSplitAllocSum') IS NOT NULL 
			DROP TABLE #tblAcctSplitAllocSum;
		IF OBJECT_ID('tempdb..#tblAcctSplitAllocSum3') IS NOT NULL 
			DROP TABLE #tblAcctSplitAllocSum3;
		

		CREATE TABLE #tblAccSplitSubProcTypes (subproc varchar(30), subProcType varchar(30));
		CREATE TABLE #tblAccSplit (conditionID int INDEX IX_tblACCSplit_conditionID, revenueGLs varchar(max), 
			batchDateLower datetime, batchDateUpper datetime, revOrCash varchar(7), conditionValue decimal(18,2), 
			conditionValueLower decimal(18,2), conditionValueUpper decimal(18,2), accountBalMPs varchar(max));
		CREATE TABLE #tblAccSplitGL (conditionID int, revenueGL int, INDEX tblAcctSplitGL_split (conditionID, revenueGL));
		CREATE TABLE #tblAccSplitMP (conditionID int, profileID int, INDEX tblAcctSplitMP_split (conditionID, profileID));
		CREATE TABLE #tblAcctSplitAllocSum (conditionID int , orgID int , batchID int , revenueGL int , 
			conditionValue decimal(18,2), conditionValueLower decimal(18,2), conditionValueUpper decimal(18,2),
			revOrCash varchar(7), subProc varchar(30), subProcType varchar(30),
			INDEX tblAcctSplitAllocSum_split (subProcType, revOrCash, batchID, revenueGL));
		CREATE TABLE #tblAcctSplitAllocSum3 (memberID int, conditionID int, allocAmt decimal(18,2), 
			conditionValue decimal(18,2), conditionValueLower decimal(18,2), conditionValueUpper decimal(18,2), 
			subproc varchar(30),INDEX tblAcctSplitAllocSum3_split (conditionID, memberID));
		
		SET @start = getdate();
		EXEC dbo.cache_members_populateMemberConditionCache_splitAcct;
		SET @totalMS = datediff(ms,@start,getdate());
		IF @testing = 0
			INSERT INTO platformStatsMC.dbo.cache_conditionsLogRunProcs (procname, timeMS, runID, numConditions) VALUES ('splitAcct', @totalMS, @runID, @acctCount);
	END

	-- split SUB data used by subProc
	select @subCount = count(*) from #tblCondALL where processArea = 'Subscriptions';
	IF @subCount > 0 BEGIN
		IF OBJECT_ID('tempdb..#tblSubFinalSplit') IS NOT NULL
			DROP TABLE #tblSubFinalSplit;
		CREATE TABLE #tblSubFinalSplit (conditionID int INDEX IX_tblSubFinalSplit_conditionID, 
			subscriptionID int, rfid int, statusID int, paymentStatusID int, hasCard char(1),
			subStartDateLower datetime, subStartDateUpper datetime, subEndDateLower datetime,
			subEndDateUpper datetime, subGraceDateLower datetime, subGraceDateUpper datetime,
			INDEX IX_tblSubFinalSplit_2 (subscriptionID,rfid,statusID,paymentStatusID,conditionID,hasCard,subStartDateLower,subStartDateUpper,subEndDateLower,subEndDateUpper,subGraceDateLower,subGraceDateUpper)
			);

		SET @start = getdate();
		EXEC dbo.cache_members_populateMemberConditionCache_splitSub;
		SET @totalMS = datediff(ms,@start,getdate());
		IF @testing = 0
			INSERT INTO platformStatsMC.dbo.cache_conditionsLogRunProcs (procname, timeMS, runID, numConditions) VALUES ('splitSub', @totalMS, @runID, @subCount);
	END
	
	-- split LIST data used by subProc
	select @listCount = count(*) from #tblCondALL where processArea = 'Listserver Memberships';
	IF @listCount > 0 BEGIN
		IF OBJECT_ID('tempdb..#tblLyrisListSplit') IS NOT NULL
			DROP TABLE #tblLyrisListSplit;
		IF OBJECT_ID('tempdb..#tblLyrisMemTypeSplit') IS NOT NULL
			DROP TABLE #tblLyrisMemTypeSplit;
		IF OBJECT_ID('tempdb..#tblLyrisSubTypeSplit') IS NOT NULL
			DROP TABLE #tblLyrisSubTypeSplit;
		IF OBJECT_ID('tempdb..#tblLyrisOptionalSplit') IS NOT NULL
			DROP TABLE #tblLyrisOptionalSplit;
		IF OBJECT_ID('tempdb..#tblLyrisFinalSplit') IS NOT NULL
			DROP TABLE #tblLyrisFinalSplit;
		CREATE TABLE #tblLyrisListSplit (conditionID int INDEX IX_tblLyrisListSplit_conditionID, 
			list_ varchar(60) INDEX IX_tblLyrisListSplit_listname);
		CREATE TABLE #tblLyrisMemTypeSplit (conditionID int INDEX IX_tblLyrisMemTypeSplit_conditionID, 
			membertype_ varchar(60) INDEX IX_tblLyrisMemTypeSplit_memberType);
		CREATE TABLE #tblLyrisSubTypeSplit (conditionID int INDEX IX_tblLyrisSubTypeSplit_conditionID, 
			subtype_ varchar(60) INDEX IX_tblLyrisSubTypeSplit_subType);
		CREATE TABLE #tblLyrisOptionalSplit (conditionID int, listLockAddress bit, listKeepActive bit, listJoinDateLower datetime, 
			listJoinDateUpper datetime, listExpireDateLower datetime, listExpireDateUpper datetime);
		CREATE TABLE #tblLyrisFinalSplit (conditionID int, list_ varchar(60), membertype_ varchar(60), 
			subtype_ varchar(60), listLockAddress bit, listKeepActive bit, listJoinDateLower datetime, 
			listJoinDateUpper datetime, listExpireDateLower datetime, listExpireDateUpper datetime);

		SET @start = getdate();
		EXEC dbo.cache_members_populateMemberConditionCache_splitList;
		SET @totalMS = datediff(ms,@start,getdate());
		IF @testing = 0
			INSERT INTO platformStatsMC.dbo.cache_conditionsLogRunProcs (procname, timeMS, runID, numConditions) VALUES ('splitList', @totalMS, @runID, @listCount);
	END

	-- split RT data used by subProc
	select @rtCount = count(*) from #tblCondALL where processArea = 'Record Types';
	IF @rtCount > 0 BEGIN
		IF OBJECT_ID('tempdb..#tblRecSplit') IS NOT NULL
			DROP TABLE #tblRecSplit;
		IF OBJECT_ID('tempdb..#tblRecSplitRoles') IS NOT NULL
			DROP TABLE #tblRecSplitRoles;
		IF OBJECT_ID('tempdb..#tblMemRecTypes') IS NOT NULL
			DROP TABLE #tblMemRecTypes;
		CREATE TABLE #tblRecSplit (conditionID int INDEX IX_tblRecSplit_conditionID, recordTypeID int, roles varchar(max));
		CREATE TABLE #tblRecSplitRoles (conditionID int INDEX IX_tblRecSplitRoles_conditionID, [role] int);
		CREATE TABLE #tblMemRecTypes (memberID int PRIMARY KEY, recordTypeID int INDEX IX_tblMemRecTypes_recordTypeID);

		SET @start = getdate();
		EXEC dbo.cache_members_populateMemberConditionCache_splitRT;
		SET @totalMS = datediff(ms,@start,getdate());
		IF @testing = 0
			INSERT INTO platformStatsMC.dbo.cache_conditionsLogRunProcs (procname, timeMS, runID, numConditions) VALUES ('splitRT', @totalMS, @runID, @rtCount);
	END

	-- split history data used by subProc
	select @mhCount = count(*) from #tblCondALL where processArea = 'Member History';
	IF @mhCount > 0 BEGIN
		IF OBJECT_ID('tempdb..#tblMHSubCategoriesSplit') IS NOT NULL
			DROP TABLE #tblMHSubCategoriesSplit;
		IF OBJECT_ID('tempdb..#tblMHSplit') IS NOT NULL
			DROP TABLE #tblMHSplit;
		IF OBJECT_ID('tempdb..#tblMHEntries') IS NOT NULL 
			DROP TABLE #tblMHEntries;
		CREATE TABLE #tblMHSubCategoriesSplit (conditionID int INDEX IX_tblMHSubCategoriesSplit_conditionID, 
			historyCategory int INDEX IX_tblMHSubCategoriesSplit_historyCategory, 
			historySubCategory int INDEX IX_tblMHSubCategoriesSplit_historySubCategory);
		CREATE TABLE #tblMHSplit (conditionID int INDEX IX_tblMHSplit_conditionID, historyCategory int INDEX IX_tblMHSplit_historyCategory, 
			historyDateLower datetime, historyDateUpper datetime, historyEndDateLower datetime, historyEndDateUpper datetime,
			historyEnteredDateLower datetime, historyEnteredDateUpper datetime, historyQuantityLower int, historyQuantityUpper int, 
			historyAmountLower decimal(18,2), historyAmountUpper decimal(18,2), historyDescriptionContains varchar(max));
		CREATE TABLE #tblMHEntries (activeMemberID int NOT NULL, categoryID int NOT NULL,
			subCategoryID int NULL, userDate datetime NULL, quantity int NULL, dollarAmt decimal(18, 2) NULL, 
			[description] varchar(max) NULL, dateEntered [datetime] NOT NULL, userEndDate [datetime] NULL,
			INDEX IX_tmpCondHistoryEntries (categoryID) INCLUDE (activeMemberID,userDate,quantity,dollarAmt,[description],dateEntered,userEndDate));

		SET @start = getdate();
		EXEC dbo.cache_members_populateMemberConditionCache_splitMH;
		SET @totalMS = datediff(ms,@start,getdate());
		IF @testing = 0
			INSERT INTO platformStatsMC.dbo.cache_conditionsLogRunProcs (procname, timeMS, runID, numConditions) VALUES ('splitMH', @totalMS, @runID, @mhCount);
	END

	-- split ANNIV data used by several subProcs
	select @annivCount = count(*) from #tblCondALL where expression = 'isanniversary';
	IF @annivCount > 0 BEGIN
		IF OBJECT_ID('tempdb..#tblAnnivSplit') IS NOT NULL
			DROP TABLE #tblAnnivSplit;
		CREATE TABLE #tblAnnivSplit (conditionID int INDEX IX_tblAnnivSplit_conditionID, conditionValue datetime, 
			conditionValueLower datetime, conditionValueUpper datetime);

		SET @start = getdate();
		EXEC dbo.cache_members_populateMemberConditionCache_splitAnniv;
		SET @totalMS = datediff(ms,@start,getdate());
		IF @testing = 0
			INSERT INTO platformStatsMC.dbo.cache_conditionsLogRunProcs (procname, timeMS, runID, numConditions) VALUES ('splitAnniv', @totalMS, @runID, @annivCount);
	END

	-- split relationship data used by subProc
	select @relCount = count(*) from #tblCondALL where processArea = 'Relationships';
	IF @relCount > 0 BEGIN
		IF OBJECT_ID('tempdb..#tblRelSubCategoriesSplit') IS NOT NULL
			DROP TABLE #tblRelSubCategoriesSplit;
		IF OBJECT_ID('tempdb..#tblRelSplit') IS NOT NULL
			DROP TABLE #tblRelSplit;
		CREATE TABLE #tblRelSubCategoriesSplit (conditionID int INDEX IX_tblRelSubCategoriesSplit_conditionID, 
			relationshipCategory int INDEX IX_tblRelSubCategoriesSplit_relationshipCategory, 
			relationshipSubCategory int INDEX IX_tblRelSubCategoriesSplit_relationshipSubCategory);
		CREATE TABLE #tblRelSplit (conditionID int INDEX IX_tblRelSplit_conditionID, relationshipCategory int INDEX IX_tblRelSplit_relationshipCategory, 
			relationshipDateLower datetime, relationshipDateUpper datetime, relationshipEndDateLower datetime, relationshipEndDateUpper datetime,
			relationshipEnteredDateLower datetime, relationshipEnteredDateUpper datetime, relationshipDescriptionContains varchar(max));

		SET @start = getdate();
		EXEC dbo.cache_members_populateMemberConditionCache_splitRel;
		SET @totalMS = datediff(ms,@start,getdate());
		IF @testing = 0
			INSERT INTO platformStatsMC.dbo.cache_conditionsLogRunProcs (procname, timeMS, runID, numConditions) VALUES ('splitRel', @totalMS, @runID, @relCount);
	END

	-- split notes data used by subProc
	select @mnCount = count(*) from #tblCondALL where processArea = 'Notes';
	IF @mnCount > 0 BEGIN
		IF OBJECT_ID('tempdb..#tblMNSubCategoriesSplit') IS NOT NULL
			DROP TABLE #tblMNSubCategoriesSplit;
		IF OBJECT_ID('tempdb..#tblMNSplit') IS NOT NULL
			DROP TABLE #tblMNSplit;
		CREATE TABLE #tblMNSubCategoriesSplit (conditionID int INDEX IX_tblMNSubCategoriesSplit_conditionID, 
			noteCategory int INDEX IX_tblMNSubCategoriesSplit_noteCategory, 
			noteSubCategory int INDEX IX_tblMNSubCategoriesSplit_noteSubCategory);
		CREATE TABLE #tblMNSplit (conditionID int INDEX IX_tblMNSplit_conditionID, noteCategory int INDEX IX_tblMNSplit_noteCategory, 
			noteDateLower datetime, noteDateUpper datetime, noteEndDateLower datetime, noteEndDateUpper datetime,
			noteEnteredDateLower datetime, noteEnteredDateUpper datetime, noteDescriptionContains varchar(max));

		SET @start = getdate();
		EXEC dbo.cache_members_populateMemberConditionCache_splitMN;
		SET @totalMS = datediff(ms,@start,getdate());
		IF @testing = 0
			INSERT INTO platformStatsMC.dbo.cache_conditionsLogRunProcs (procname, timeMS, runID, numConditions) VALUES ('splitMN', @totalMS, @runID, @mnCount);
	END

	-- split FUND data used by subProcs
	select @fundCount = count(*) from #tblCondALL where processArea = 'Contributions';
	IF @fundCount > 0 BEGIN
		IF OBJECT_ID('tempdb..#tblCPProgramList') IS NOT NULL
			DROP TABLE #tblCPProgramList;
		IF OBJECT_ID('tempdb..#tblCPCampaignList') IS NOT NULL
			DROP TABLE #tblCPCampaignList;
		IF OBJECT_ID('tempdb..#tblCPRateSplit') IS NOT NULL
			DROP TABLE #tblCPRateSplit;
		IF OBJECT_ID('tempdb..#tblCPEntrySplit') IS NOT NULL
			DROP TABLE #tblCPEntrySplit;
		IF OBJECT_ID('tempdb..#tblDistribSplit') IS NOT NULL
			DROP TABLE #tblDistribSplit;
		IF OBJECT_ID('tempdb..#tblMonetaryFieldSplit') IS NOT NULL
			DROP TABLE #tblMonetaryFieldSplit;
		IF OBJECT_ID('tempdb..#tblNonMonetaryFieldValueSplit') IS NOT NULL
			DROP TABLE #tblNonMonetaryFieldValueSplit;
		IF OBJECT_ID('tempdb..#tblCPStatusSplit') IS NOT NULL
			DROP TABLE #tblCPStatusSplit;
		IF OBJECT_ID('tempdb..#tblCPFreqSplit') IS NOT NULL
			DROP TABLE #tblCPFreqSplit;
		IF OBJECT_ID('tempdb..#tblQualInstallments') IS NOT NULL
			DROP TABLE #tblQualInstallments;
		CREATE TABLE #tblCPProgramList (conditionID int, programID int);
		CREATE TABLE #tblCPCampaignList (conditionID int, campaignID int);
		CREATE TABLE #tblCPRateSplit (conditionID int, rateID int);
		CREATE TABLE #tblCPEntrySplit (conditionID int, programListOption char(2), nonMonetaryFieldID int, isPerpetual bit, isAnonymous bit, hasCard char(1),
			firstInstallAmtLower decimal(18,2), firstInstallAmtUpper decimal(18,2), recurringInstallAmtLower decimal(18,2), recurringInstallAmtUpper decimal(18,2), 
			contribStartDateLower datetime, contribStartDateUpper datetime, contribEndDateLower datetime, contribEndDateUpper datetime, contribEnteredDateLower datetime, 
			contribEnteredDateUpper datetime, programInstallDateLower datetime, programInstallDateUpper datetime, conditionValue decimal(18,2), 
			conditionValueLower decimal(18,2), conditionValueUpper decimal(18,2));
		CREATE TABLE #tblDistribSplit (conditionID int, distribID int);
		CREATE TABLE #tblMonetaryFieldSplit (conditionID int, fieldID int);
		CREATE TABLE #tblNonMonetaryFieldValueSplit (conditionID int, valueID int);
		CREATE TABLE #tblCPStatusSplit (conditionID int, statusID int);
		CREATE TABLE #tblCPFreqSplit (conditionID int, frequencyID int);
		CREATE TABLE #tblQualInstallments (conditionID int, contributionID int, memberID int, transactionID int, isConverted bit, isPaidOnCreate bit);
		
		SET @start = getdate();
		EXEC dbo.cache_members_populateMemberConditionCache_splitFund;
		SET @totalMS = datediff(ms,@start,getdate());
		IF @testing = 0
			INSERT INTO platformStatsMC.dbo.cache_conditionsLogRunProcs (procname, timeMS, runID, numConditions) VALUES ('splitFund', @totalMS, @runID, @fundCount);
	END

	-- split EV data used by subProcs
	select @evCount = count(*) from #tblCondALL where processArea = 'Events';
	IF @evCount > 0 BEGIN
		IF OBJECT_ID('tempdb..#tblEvSplit') IS NOT NULL
			DROP TABLE #tblEvSplit;
		IF OBJECT_ID('tempdb..#tblEvSplitCalendars') IS NOT NULL
			DROP TABLE #tblEvSplitCalendars;
		IF OBJECT_ID('tempdb..#tblEvSplitCategories') IS NOT NULL
			DROP TABLE #tblEvSplitCategories;
		IF OBJECT_ID('tempdb..#tblEvSplitRoles') IS NOT NULL
			DROP TABLE #tblEvSplitRoles;
		IF OBJECT_ID('tempdb..#tblEvSplitEvents') IS NOT NULL
			DROP TABLE #tblEvSplitEvents;
		CREATE TABLE #tblEvSplit (conditionID int INDEX IX_tblEvSplit_conditionID, evCalendarIDs varchar(max), 
			evCategoryIDs varchar(max), evRoleIDs varchar(max), evEventIDs varchar(max), evEventTitle varchar(400),
			evEventType varchar(4), evFilterOption varchar(6), evReportCode varchar(30), evStartLower datetime, 
			evStartUpper datetime, evSiteID int);
		CREATE TABLE #tblEvSplitCalendars (conditionID int, calendarID int);
		CREATE TABLE #tblEvSplitCategories (conditionID int, categoryID int);
		CREATE TABLE #tblEvSplitRoles (conditionID int, categoryID int, hasNoRoleFilter bit, hasRolesFilter bit);
		CREATE TABLE #tblEvSplitEvents (conditionID int, eventID int);
								
		SET @start = getdate();
		EXEC dbo.cache_members_populateMemberConditionCache_splitEv;
		SET @totalMS = datediff(ms,@start,getdate());
		IF @testing = 0
			INSERT INTO platformStatsMC.dbo.cache_conditionsLogRunProcs (procname, timeMS, runID, numConditions) VALUES ('splitEv', @totalMS, @runID, @evCount);
	END

	-- split SuppressionList data used by subProcs
	select @suppListCount = count(*) from #tblCondALL where processArea = 'Suppression Lists';
	IF @suppListCount > 0 BEGIN
		IF OBJECT_ID('tempdb..#tblSuppListSplit') IS NOT NULL
			DROP TABLE #tblSuppListSplit;
		IF OBJECT_ID('tempdb..#tblSuppListSplitSubUsers') IS NOT NULL
			DROP TABLE #tblSuppListSplitSubUsers;
		IF OBJECT_ID('tempdb..#tblSuppListSplitEmailTypes') IS NOT NULL
			DROP TABLE #tblSuppListSplitEmailTypes;
		IF OBJECT_ID('tempdb..#tblSuppListSplitEmailTagTypes') IS NOT NULL
			DROP TABLE #tblSuppListSplitEmailTagTypes;
		IF OBJECT_ID('tempdb..#tblSuppListSplitTypes') IS NOT NULL
			DROP TABLE #tblSuppListSplitTypes;
		CREATE TABLE #tblSuppListSplit (conditionID int INDEX IX_tblSuppListSplit_conditionID, subuserIDs varchar(max), 
			emailTypeIDs varchar(max), emailTagTypeIDs varchar(max), suppressionListTypeIDs varchar(max), suppListDateAddedLower datetime, 
			suppListDateAddedUpper datetime, suppListSiteID int);
		CREATE TABLE #tblSuppListSplitSubUsers (conditionID int, subuserID int);
		CREATE TABLE #tblSuppListSplitEmailTypes (conditionID int, emailTypeID int);
		CREATE TABLE #tblSuppListSplitEmailTagTypes (conditionID int, emailTagTypeID int);
		CREATE TABLE #tblSuppListSplitTypes (conditionID int, typeID int);
		
		SET @start = getdate();
		EXEC dbo.cache_members_populateMemberConditionCache_splitSuppList;
		SET @totalMS = datediff(ms,@start,getdate());
		IF @testing = 0
			INSERT INTO platformStatsMC.dbo.cache_conditionsLogRunProcs (procname, timeMS, runID, numConditions) VALUES ('splitSuppList', @totalMS, @runID, @suppListCount);
	END

	-- split SW data used by subProcs
	select @swCount = count(*) from #tblCondALL where processArea = 'SeminarWeb';
	IF @swCount > 0 BEGIN
		IF OBJECT_ID('tempdb..#tblSWSplit') IS NOT NULL
			DROP TABLE #tblSWSplit;
		IF OBJECT_ID('tempdb..#tblSWSplitSeminars') IS NOT NULL
			DROP TABLE #tblSWSplitSeminars;
		IF OBJECT_ID('tempdb..#tblSWSplitAwardedCredit') IS NOT NULL
			DROP TABLE #tblSWSplitAwardedCredit;
		IF OBJECT_ID('tempdb..#tblSWSplitDeniedCredits') IS NOT NULL
			DROP TABLE #tblSWSplitDeniedCredits;
		CREATE TABLE #tblSWSplit (conditionID int INDEX IX_tblSWSplit_conditionID, swOrgCode varchar(10), swParticipantID int, swSeminarIDs varchar(max), 
			swCreditAwardLinkIDs varchar(max), swCreditDeniedLinkIDs varchar(max), swProgramName varchar(400), swType varchar(4), swFilterOption varchar(8), 
			swPubType varchar(2), swStartDateLower datetime, swStartDateUpper datetime, enrollmentDateLower datetime, enrollmentDateUpper datetime, 
			completedDateLower datetime, completedDateUpper datetime);
		CREATE TABLE #tblSWSplitSeminars (conditionID int, seminarID int);
		CREATE TABLE #tblSWSplitAwardedCredit (conditionID int, CSALinkID int);
		CREATE TABLE #tblSWSplitDeniedCredits (conditionID int, CSALinkID int);
								
		SET @start = getdate();
		EXEC dbo.cache_members_populateMemberConditionCache_splitSW;
		SET @totalMS = datediff(ms,@start,getdate());
		IF @testing = 0
			INSERT INTO platformStatsMC.dbo.cache_conditionsLogRunProcs (procname, timeMS, runID, numConditions) VALUES ('splitSW', @totalMS, @runID, @swCount);
	END

	-- split ConsentList data used by subProcs
	select @clCount = count(*) from #tblCondALL where processArea = 'Consent Lists';
	IF @clCount > 0 BEGIN
		IF OBJECT_ID('tempdb..#tblConsentListSplit') IS NOT NULL
			DROP TABLE #tblConsentListSplit;
		IF OBJECT_ID('tempdb..#tblConsentListSplitEmailTypes') IS NOT NULL
			DROP TABLE #tblConsentListSplitEmailTypes;
		IF OBJECT_ID('tempdb..#tblConsentListSplitEmailTagTypes') IS NOT NULL
			DROP TABLE #tblConsentListSplitEmailTagTypes;
		IF OBJECT_ID('tempdb..#tblConsentListSplitListTypes') IS NOT NULL
			DROP TABLE #tblConsentListSplitListTypes;
		IF OBJECT_ID('tempdb..#tblConsentListSplitLists') IS NOT NULL
			DROP TABLE #tblConsentListSplitLists;
		CREATE TABLE #tblConsentListSplit (conditionID int INDEX IX_tblConsentListSplit_conditionID, consentListModeID int,
			emailTypeIDs varchar(max), emailTagTypeIDs varchar(max), consentListTypeIDs varchar(max), consentListIDs varchar(max),
			clDateAddedLower datetime, clDateAddedUpper datetime, orgID int);
		CREATE TABLE #tblConsentListSplitEmailTypes (conditionID int INDEX IX_tblConsentListSplitEmailTypes_conditionID, emailTypeID int);
		CREATE TABLE #tblConsentListSplitEmailTagTypes (conditionID int INDEX IX_tblConsentListSplitEmailTagTypes_conditionID, emailTagTypeID int);
		CREATE TABLE #tblConsentListSplitListTypes (conditionID int INDEX IX_tblConsentListSplitListTypes_conditionID, consentListTypeID int);
		CREATE TABLE #tblConsentListSplitLists (conditionID int INDEX IX_tblConsentListSplitLists_conditionID, consentListID int);
		
		SET @start = getdate();
		EXEC dbo.cache_members_populateMemberConditionCache_splitCL;
		SET @totalMS = datediff(ms,@start,getdate());
		IF @testing = 0
			INSERT INTO platformStatsMC.dbo.cache_conditionsLogRunProcs (procname, timeMS, runID, numConditions) VALUES ('splitCL', @totalMS, @runID, @clCount);
	END

	-- split Districting data used by subProcs
	select @madCount = count(*) from #tblCondALL where processArea = 'Districting';
	IF @madCount > 0 BEGIN
		IF OBJECT_ID('tempdb..#tblDistrictingSplit') IS NOT NULL
			DROP TABLE #tblDistrictingSplit;
		IF OBJECT_ID('tempdb..#tblDistrictingSplitDistrictValues') IS NOT NULL
			DROP TABLE #tblDistrictingSplitDistrictValues;
		IF OBJECT_ID('tempdb..#tblDistrictingSplitDistrictMatchVal') IS NOT NULL
			DROP TABLE #tblDistrictingSplitDistrictMatchVal;
		CREATE TABLE #tblDistrictingSplit (conditionID int PRIMARY KEY, subProc varchar(50) INDEX IX_tblDistrictingSplit_subProc, validAsOfDate date);
    	CREATE TABLE #tblDistrictingSplitDistrictValues (conditionID int, districtValueID int, INDEX IX_tblDistrictingSplitDistrictValues_conditionID_districtValueID(conditionID, districtValueID));
		CREATE TABLE #tblDistrictingSplitDistrictMatchVal (valueID int, conditionID int, INDEX IX_tblDistrictingSplitDistrictMatchVal__conditionID__valueID (conditionID, valueID));

		SET @start = getdate();
		EXEC dbo.cache_members_populateMemberConditionCache_splitMAD;
		SET @totalMS = datediff(ms,@start,getdate());
		IF @testing = 0
			INSERT INTO platformStatsMC.dbo.cache_conditionsLogRunProcs (procname, timeMS, runID, numConditions) VALUES ('splitMAD', @totalMS, @runID, @madCount);
	END

	-- split Referral data used by subProcs
	select @refCount = count(*) from #tblCondALL where processArea = 'Referrals';
	IF @refCount > 0 BEGIN
		IF OBJECT_ID('tempdb..#tblRefSplit') IS NOT NULL
			DROP TABLE #tblRefSplit;
		IF OBJECT_ID('tempdb..#tblRefSplitStatuses') IS NOT NULL
			DROP TABLE #tblRefSplitStatuses;
		IF OBJECT_ID('tempdb..#tblRefSplitPanels') IS NOT NULL
			DROP TABLE #tblRefSplitPanels;
		IF OBJECT_ID('tempdb..#tblRefSplitCustomFieldValues') IS NOT NULL
			DROP TABLE #tblRefSplitCustomFieldValues;
		IF OBJECT_ID('tempdb..#tblRefSplitFollowUpStatuses') IS NOT NULL
			DROP TABLE #tblRefSplitFollowUpStatuses;
		IF OBJECT_ID('tempdb..#tblRefSplitFeeDiscrepancyStatuses') IS NOT NULL
			DROP TABLE #tblRefSplitFeeDiscrepancyStatuses;
		CREATE TABLE #tblRefSplit (conditionID int INDEX IX_tblReferralSplit_conditionID, statusIDs varchar(max), panelIDs varchar(max), 
			customFieldID int, followUpStatuses varchar(10), feeDiscrepancyStatusIDs varchar(max),
			callDateLower datetime, callDateUpper datetime, referralDateLower datetime, referralDateUpper datetime,
			retainedDateLower datetime, retainedDateUpper datetime, closedDateLower datetime, closedDateUpper datetime,
			lastUpdatedDateLower datetime, lastUpdatedDateUpper datetime, feeDiscrepancyDateLower datetime, feeDiscrepancyDateUpper datetime,
			followUpDateLower datetime, followUpDateUpper datetime, referralSiteID int);
		CREATE TABLE #tblRefSplitStatuses (conditionID int INDEX IX_tblRefSplitStatuses_conditionID, statusID int);
		CREATE TABLE #tblRefSplitPanels (conditionID int INDEX IX_tblRefSplitPanels_conditionID, panelID int);
		CREATE TABLE #tblRefSplitCustomFieldValues (conditionID int INDEX IX_tblRefSplitCustomFieldValues_conditionID, valueID int);
		CREATE TABLE #tblRefSplitFollowUpStatuses (conditionID int INDEX IX_tblRefSplitFollowUpStatuses_conditionID, [status] char(1));
		CREATE TABLE #tblRefSplitFeeDiscrepancyStatuses (conditionID int INDEX IX_tblRefSplitFeeDiscrepancyStatuses_conditionID, statusID int);
		
		SET @start = getdate();
		EXEC dbo.cache_members_populateMemberConditionCache_splitRef;
		SET @totalMS = datediff(ms,@start,getdate());
		IF @testing = 0
			INSERT INTO platformStatsMC.dbo.cache_conditionsLogRunProcs (procname, timeMS, runID, numConditions) VALUES ('splitRef', @totalMS, @runID, @refCount);
	END

	-- split TASKS data used by several subProcs
	select @tasksCount = count(*) from #tblCondALL where processArea = 'Tasks';
	IF @tasksCount > 0 BEGIN
		IF OBJECT_ID('tempdb..#tblTaskEntrySplit') IS NOT NULL
			DROP TABLE #tblTaskEntrySplit;
		IF OBJECT_ID('tempdb..#tblTaskWorkspaceSplit') IS NOT NULL
			DROP TABLE #tblTaskWorkspaceSplit;
		IF OBJECT_ID('tempdb..#tblTaskProjectSplit') IS NOT NULL
			DROP TABLE #tblTaskProjectSplit;
		IF OBJECT_ID('tempdb..#tblTaskTagSplit') IS NOT NULL
			DROP TABLE #tblTaskTagSplit;
		IF OBJECT_ID('tempdb..#tblTaskFieldSplit') IS NOT NULL
			DROP TABLE #tblTaskFieldSplit;
		IF OBJECT_ID('tempdb..#tblTaskFieldValueSplit') IS NOT NULL
			DROP TABLE #tblTaskFieldValueSplit;
		IF OBJECT_ID('tempdb..#tblTaskStatusSplit') IS NOT NULL
			DROP TABLE #tblTaskStatusSplit;
		CREATE TABLE #tblTaskEntrySplit (conditionID int INDEX IX_tblTaskEntrySplit_conditionID, taskLinkType varchar(10), taskCreatedDateLower datetime, taskCreatedDateUpper datetime, taskDueDateLower datetime, taskDueDateUpper datetime, 
			taskReminderDateLower datetime, taskReminderDateUpper datetime, taskLastModifiedDateLower datetime, taskLastModifiedDateUpper datetime);
		CREATE TABLE #tblTaskWorkspaceSplit (conditionID int INDEX IX_tblTaskWorkspaceSplit_conditionID, workspaceID int);
		CREATE TABLE #tblTaskProjectSplit (conditionID int INDEX IX_tblTaskProjectSplit_conditionID, projectID int);
		CREATE TABLE #tblTaskTagSplit (conditionID int INDEX IX_tblTaskTagSplit_conditionID, categoryID int);
		CREATE TABLE #tblTaskFieldSplit (conditionID int INDEX IX_tblTaskFieldSplit_conditionID, fieldID int, itemType varchar(20));
		CREATE TABLE #tblTaskFieldValueSplit (conditionID int INDEX IX_tblTaskFieldValueSplit_conditionID, valueID int, fieldID int);
		CREATE TABLE #tblTaskStatusSplit (conditionID int INDEX IX_tblTaskStatusSplit_conditionID, statusID int);

		SET @start = getdate();
		EXEC dbo.cache_members_populateMemberConditionCache_splitTasks;
		SET @totalMS = datediff(ms,@start,getdate());
		IF @testing = 0
			INSERT INTO platformStatsMC.dbo.cache_conditionsLogRunProcs (procname, timeMS, runID, numConditions) VALUES ('splitTasks', @totalMS, @runID, @refCount);
	END

	/* ************* */
	/* CALL SUBPROCS */
	/* ************* */
	-- loop over subProcs to run
	IF OBJECT_ID('tempdb..#cache_members_conditions_shouldbe') IS NOT NULL
		DROP TABLE #cache_members_conditions_shouldbe;
	CREATE TABLE #cache_members_conditions_shouldbe (memberid int, conditionID int, 
		INDEX IX_cache_members_conditions_shouldbe_conditionID_memberID NONCLUSTERED (conditionID,memberid));

	select @subProc = min(subProc) from #tblCondALL;
	while @subProc is not null BEGIN
		SET @subProc2 = 'cache_members_populateMemberConditionCache_' + @subProc;

		set @subprocConditionCount = null;
		SELECT @subprocConditionCount = count(*) from #tblCondALL where subProc = @subProc;

		SET @start = getdate();
		EXEC @subProc2;
		SET @totalMS = datediff(ms,@start,getdate());
		IF @testing = 0
			INSERT INTO platformStatsMC.dbo.cache_conditionsLogRunProcs (procname, timeMS, runID, numConditions) VALUES (@subProc, @totalMS, @runID, @subprocConditionCount);

		delete from #tblCondALL where subProc = @subProc;

		select @subProc = min(subProc) from #tblCondALL where subProc > @subProc;
	END

	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	
	IF @testing = 0 BEGIN
		-- delete member/conditions that should not be there
		SET @start = getdate();
		EXEC dbo.cache_members_populateMemberConditionCache_delCache @orgID=@orgID;
		SET @totalMS = datediff(ms,@start,getdate());
		INSERT INTO platformStatsMC.dbo.cache_conditionsLogRunProcs (procname, timeMS, runID) VALUES ('delCache', @totalMS, @runID);
			
		-- insert member/conditions that should be but arent already there
		SET @start = getdate();
		EXEC dbo.cache_members_populateMemberConditionCache_addCache @orgID=@orgID;
		SET @totalMS = datediff(ms,@start,getdate());
		INSERT INTO platformStatsMC.dbo.cache_conditionsLogRunProcs (procname, timeMS, runID) VALUES ('addCache', @totalMS, @runID);

		-- log what was changed
		INSERT INTO platformStatsMC.dbo.cache_conditionsLogRunChanges (runID, memberID, conditionID, isAdded)
		SELECT @runID, memberID, conditionID, isAdded
		FROM #tmpMCQCondCacheChanges;

		set @numChangesMade = @@rowcount;

		--Update the log entry with details
		UPDATE platformStatsMC.dbo.cache_conditionsLogRun
		SET finishDate = getdate(),
		msProcessing = datediff(MILLISECOND,@dateStarted,getdate()),
		numChangesMade = @numChangesMade
		WHERE runID = @runID
		AND orgID = @orgID
	END ELSE
		INSERT INTO #tmpMCQTestCondMembers (memberID, conditionID, orgID)
		SELECT memberID, conditionID, @orgID
		FROM #cache_members_conditions_shouldbe;


	on_done:
	IF OBJECT_ID('tempdb..#cache_members_conditions_shouldbe') IS NOT NULL
		DROP TABLE #cache_members_conditions_shouldbe;
	IF OBJECT_ID('tempdb..#tblCondALL') IS NOT NULL
		DROP TABLE #tblCondALL;
	IF OBJECT_ID('tempdb..#tblCondALLFinal') IS NOT NULL
		DROP TABLE #tblCondALLFinal;
	IF OBJECT_ID('tempdb..#tblCondValues') IS NOT NULL
		DROP TABLE #tblCondValues;
	IF @acctCount > 0 BEGIN
		IF OBJECT_ID('tempdb..#tblAccSplitSubProcTypes') IS NOT NULL
			DROP TABLE #tblAccSplitSubProcTypes;
		IF OBJECT_ID('tempdb..#tblAccSplit') IS NOT NULL
			DROP TABLE #tblAccSplit;
		IF OBJECT_ID('tempdb..#tblAccSplitGL') IS NOT NULL
			DROP TABLE #tblAccSplitGL;
		IF OBJECT_ID('tempdb..#tblAccSplitMP') IS NOT NULL
			DROP TABLE #tblAccSplitMP;
		IF OBJECT_ID('tempdb..#tblAcctSplitAllocSum') IS NOT NULL 
			DROP TABLE #tblAcctSplitAllocSum;
		IF OBJECT_ID('tempdb..#tblAcctSplitAllocSum3') IS NOT NULL 
			DROP TABLE #tblAcctSplitAllocSum3;
	END
	IF @subCount > 0 BEGIN
		IF OBJECT_ID('tempdb..#tblSubFinalSplit') IS NOT NULL
			DROP TABLE #tblSubFinalSplit;
	END
	IF @listCount > 0 BEGIN
		IF OBJECT_ID('tempdb..#tblLyrisListSplit') IS NOT NULL
			DROP TABLE #tblLyrisListSplit;
		IF OBJECT_ID('tempdb..#tblLyrisMemTypeSplit') IS NOT NULL
			DROP TABLE #tblLyrisMemTypeSplit;
		IF OBJECT_ID('tempdb..#tblLyrisSubTypeSplit') IS NOT NULL
			DROP TABLE #tblLyrisSubTypeSplit;
		IF OBJECT_ID('tempdb..#tblLyrisOptionalSplit') IS NOT NULL
			DROP TABLE #tblLyrisOptionalSplit;
		IF OBJECT_ID('tempdb..#tblLyrisFinalSplit') IS NOT NULL
			DROP TABLE #tblLyrisFinalSplit;
	END
	IF @rtCount > 0 BEGIN
		IF OBJECT_ID('tempdb..#tblRecSplit') IS NOT NULL
			DROP TABLE #tblRecSplit;
		IF OBJECT_ID('tempdb..#tblRecSplitRoles') IS NOT NULL
			DROP TABLE #tblRecSplitRoles;
		IF OBJECT_ID('tempdb..#tblMemRecTypes') IS NOT NULL
			DROP TABLE #tblMemRecTypes;
	END
	IF @mhCount > 0 BEGIN
		IF OBJECT_ID('tempdb..#tblMHSubCategoriesSplit') IS NOT NULL
			DROP TABLE #tblMHSubCategoriesSplit;
		IF OBJECT_ID('tempdb..#tblMHSplit') IS NOT NULL
			DROP TABLE #tblMHSplit;
		IF OBJECT_ID('tempdb..#tblMHEntries') IS NOT NULL 
			DROP TABLE #tblMHEntries;
	END
	IF @annivCount > 0 BEGIN
		IF OBJECT_ID('tempdb..#tblAnnivSplit') IS NOT NULL
			DROP TABLE #tblAnnivSplit;
	END
	IF @relCount > 0 BEGIN
		IF OBJECT_ID('tempdb..#tblRelSubCategoriesSplit') IS NOT NULL
			DROP TABLE #tblRelSubCategoriesSplit;
		IF OBJECT_ID('tempdb..#tblRelSplit') IS NOT NULL
			DROP TABLE #tblRelSplit;
	END
	IF @mnCount > 0 BEGIN
		IF OBJECT_ID('tempdb..#tblMNSubCategoriesSplit') IS NOT NULL
			DROP TABLE #tblMNSubCategoriesSplit;
		IF OBJECT_ID('tempdb..#tblMNSplit') IS NOT NULL
			DROP TABLE #tblMNSplit;
	END
	IF @fundCount > 0 BEGIN
		IF OBJECT_ID('tempdb..#tblCPProgramList') IS NOT NULL
			DROP TABLE #tblCPProgramList;
		IF OBJECT_ID('tempdb..#tblCPCampaignList') IS NOT NULL
			DROP TABLE #tblCPCampaignList;
		IF OBJECT_ID('tempdb..#tblCPRateSplit') IS NOT NULL
			DROP TABLE #tblCPRateSplit;
		IF OBJECT_ID('tempdb..#tblCPEntrySplit') IS NOT NULL
			DROP TABLE #tblCPEntrySplit;
		IF OBJECT_ID('tempdb..#tblDistribSplit') IS NOT NULL
			DROP TABLE #tblDistribSplit;
		IF OBJECT_ID('tempdb..#tblMonetaryFieldSplit') IS NOT NULL
			DROP TABLE #tblMonetaryFieldSplit;
		IF OBJECT_ID('tempdb..#tblNonMonetaryFieldValueSplit') IS NOT NULL
			DROP TABLE #tblNonMonetaryFieldValueSplit;
		IF OBJECT_ID('tempdb..#tblCPStatusSplit') IS NOT NULL
			DROP TABLE #tblCPStatusSplit;
		IF OBJECT_ID('tempdb..#tblCPFreqSplit') IS NOT NULL
			DROP TABLE #tblCPFreqSplit;
		IF OBJECT_ID('tempdb..#tblQualInstallments') IS NOT NULL
			DROP TABLE #tblQualInstallments;
	END
	IF @evCount > 0 BEGIN
		IF OBJECT_ID('tempdb..#tblEvSplit') IS NOT NULL
			DROP TABLE #tblEvSplit;
		IF OBJECT_ID('tempdb..#tblEvSplitCalendars') IS NOT NULL
			DROP TABLE #tblEvSplitCalendars;
		IF OBJECT_ID('tempdb..#tblEvSplitCategories') IS NOT NULL
			DROP TABLE #tblEvSplitCategories;
		IF OBJECT_ID('tempdb..#tblEvSplitRoles') IS NOT NULL
			DROP TABLE #tblEvSplitRoles;
		IF OBJECT_ID('tempdb..#tblEvSplitEvents') IS NOT NULL
			DROP TABLE #tblEvSplitEvents;
	END
	IF @suppListCount > 0 BEGIN
		IF OBJECT_ID('tempdb..#tblSuppListSplit') IS NOT NULL
			DROP TABLE #tblSuppListSplit;
		IF OBJECT_ID('tempdb..#tblSuppListSplitSubUsers') IS NOT NULL
			DROP TABLE #tblSuppListSplitSubUsers;
		IF OBJECT_ID('tempdb..#tblSuppListSplitEmailTypes') IS NOT NULL
			DROP TABLE #tblSuppListSplitEmailTypes;
		IF OBJECT_ID('tempdb..#tblSuppListSplitEmailTagTypes') IS NOT NULL
			DROP TABLE #tblSuppListSplitEmailTagTypes;
		IF OBJECT_ID('tempdb..#tblSuppListSplitTypes') IS NOT NULL
			DROP TABLE #tblSuppListSplitTypes;
	END
	IF @swCount > 0 BEGIN
		IF OBJECT_ID('tempdb..#tblSWSplit') IS NOT NULL
			DROP TABLE #tblSWSplit;
		IF OBJECT_ID('tempdb..#tblSWSplitSeminars') IS NOT NULL
			DROP TABLE #tblSWSplitSeminars;
		IF OBJECT_ID('tempdb..#tblSWSplitAwardedCredit') IS NOT NULL
			DROP TABLE #tblSWSplitAwardedCredit;
		IF OBJECT_ID('tempdb..#tblSWSplitDeniedCredits') IS NOT NULL
			DROP TABLE #tblSWSplitDeniedCredits;
	END
	IF @clCount > 0 BEGIN
		IF OBJECT_ID('tempdb..#tblConsentListSplit') IS NOT NULL
			DROP TABLE #tblConsentListSplit;
		IF OBJECT_ID('tempdb..#tblConsentListSplitEmailTypes') IS NOT NULL
			DROP TABLE #tblConsentListSplitEmailTypes;
		IF OBJECT_ID('tempdb..#tblConsentListSplitEmailTagTypes') IS NOT NULL
			DROP TABLE #tblConsentListSplitEmailTagTypes;
		IF OBJECT_ID('tempdb..#tblConsentListSplitListTypes') IS NOT NULL
			DROP TABLE #tblConsentListSplitListTypes;
		IF OBJECT_ID('tempdb..#tblConsentListSplitLists') IS NOT NULL
			DROP TABLE #tblConsentListSplitLists;
	END
	IF @madCount > 0 BEGIN
		IF OBJECT_ID('tempdb..#tblDistrictingSplit') IS NOT NULL
			DROP TABLE #tblDistrictingSplit;
		IF OBJECT_ID('tempdb..#tblDistrictingSplitDistrictValues') IS NOT NULL
			DROP TABLE #tblDistrictingSplitDistrictValues;
		IF OBJECT_ID('tempdb..#tblDistrictingSplitDistrictMatchVal') IS NOT NULL
			DROP TABLE #tblDistrictingSplitDistrictMatchVal;
	END
	IF @refCount > 0 BEGIN
		IF OBJECT_ID('tempdb..#tblRefSplit') IS NOT NULL
			DROP TABLE #tblRefSplit;
		IF OBJECT_ID('tempdb..#tblRefSplitStatuses') IS NOT NULL
			DROP TABLE #tblRefSplitStatuses;
		IF OBJECT_ID('tempdb..#tblRefSplitPanels') IS NOT NULL
			DROP TABLE #tblRefSplitPanels;
		IF OBJECT_ID('tempdb..#tblRefSplitCustomFieldValues') IS NOT NULL
			DROP TABLE #tblRefSplitCustomFieldValues;
		IF OBJECT_ID('tempdb..#tblRefSplitFollowUpStatuses') IS NOT NULL
			DROP TABLE #tblRefSplitFollowUpStatuses;
		IF OBJECT_ID('tempdb..#tblRefSplitFeeDiscrepancyStatuses') IS NOT NULL
			DROP TABLE #tblRefSplitFeeDiscrepancyStatuses;
	END
	IF @tasksCount > 0 BEGIN
		IF OBJECT_ID('tempdb..#tblTaskEntrySplit') IS NOT NULL
			DROP TABLE #tblTaskEntrySplit;
		IF OBJECT_ID('tempdb..#tblTaskWorkspaceSplit') IS NOT NULL
			DROP TABLE #tblTaskWorkspaceSplit;
		IF OBJECT_ID('tempdb..#tblTaskProjectSplit') IS NOT NULL
			DROP TABLE #tblTaskProjectSplit;
		IF OBJECT_ID('tempdb..#tblTaskTagSplit') IS NOT NULL
			DROP TABLE #tblTaskTagSplit;
		IF OBJECT_ID('tempdb..#tblTaskFieldSplit') IS NOT NULL
			DROP TABLE #tblTaskFieldSplit;
		IF OBJECT_ID('tempdb..#tblTaskFieldValueSplit') IS NOT NULL
			DROP TABLE #tblTaskFieldValueSplit;
		IF OBJECT_ID('tempdb..#tblTaskStatusSplit') IS NOT NULL
			DROP TABLE #tblTaskStatusSplit;
	END

	IF membercentral.dbo.fn_getCurrentTransactionIsolationLevel() <> 'ReadCommitted'
		SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	IF membercentral.dbo.fn_getCurrentTransactionIsolationLevel() <> 'ReadCommitted'
		SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

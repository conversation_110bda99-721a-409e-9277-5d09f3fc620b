ALTER PROC dbo.cms_createCategoryTree
@siteID int,
@categoryTreeName varchar(100),
@categoryTreeDesc varchar(250),
@categoryTreeCode varchar(50),
@controllingSiteResourceID int,
@categoryTreeID int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	-- ensure @categoryTreeID is null (can be passed in)
	SELECT @categoryTreeID = null;
	
	declare @siteResourceID int, @resourceTypeID int;
	select @resourceTypeID = dbo.fn_getResourceTypeID('categoryTree');

	-- categoryTreeCode must be A-Z 0-9 only and be unique in site
	select @categoryTreeCode = dbo.fn_regExReplace(isnull(@categoryTreeCode,''),'[^A-Za-z0-9]','');
	IF EXISTS (select categoryTreeID from dbo.cms_categoryTrees ct inner join cms_siteResources sr on ct.siteResourceID = sr.siteResourceID inner join cms_siteResourceStatuses srs on srs.siteResourceStatusID = sr.siteResourceStatusID and srs.siteResourceStatusDesc <> 'Deleted' where ct.siteID = @siteID and ct.categoryTreeCode = @categoryTreeCode and ct.categoryTreeCode <> '')
		RAISERROR('Category tree code must be unique.',16,1);

	BEGIN TRAN;

		exec dbo.cms_createSiteResource
			@resourceTypeID = @resourceTypeID,
			@siteResourceStatusID = 1,
			@siteID = @siteid,
			@isVisible = 1,
			@parentSiteResourceID = null,
			@siteResourceID   = @siteResourceID OUTPUT;

		-- get next sort order
		declare @sortOrder int
		select @sortOrder = isnull(max(sortOrder),0) + 1
			from dbo.cms_categoryTrees
			where siteID = @siteID
			and controllingSiteResourceID = @controllingSiteResourceID;

		INSERT INTO dbo.cms_categoryTrees (siteID, siteResourceID, categoryTreeName, categoryTreeDesc, categoryTreeCode, controllingSiteResourceID, sortOrder)
		VALUES (@siteID, @siteResourceID, @categoryTreeName, @categoryTreeDesc, @categoryTreeCode, @controllingSiteResourceID, @sortOrder);
			select @categoryTreeID = SCOPE_IDENTITY();

		EXEC dbo.cms_reorderCategoryTrees @controllingSiteResourceID;

	COMMIT TRAN;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

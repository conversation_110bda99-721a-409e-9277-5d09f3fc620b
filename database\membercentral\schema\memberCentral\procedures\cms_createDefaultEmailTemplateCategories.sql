ALTER PROCEDURE dbo.cms_createDefaultEmailTemplateCategories
@siteID int,
@contributingMemberID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY
	
	declare @categoryTreeID int, @controllingSiteResourceID int, @templateCategoryID int;

	select @controllingSiteResourceID = dbo.fn_getSiteResourceIDForResourceType('EmailTemplateAdmin', @siteID);
	IF @controllingSiteResourceID is NULL 
		RAISERROR('EmailTemplateAdmin resource type not found.',16,1);

	select @categoryTreeID = categoryTreeID from dbo.cms_categoryTrees where controllingSiteResourceID = @controllingSiteResourceID and categoryTreeCode = 'ETHISTORY';
	IF @categoryTreeID is NULL 
		exec dbo.cms_createCategoryTree @siteID=@siteID, @categoryTreeName='MemberHistoryTemplates', 
			@categoryTreeDesc='Member History Templates', @categoryTreeCode='ETHISTORY',
			@controllingSiteResourceID=@controllingSiteResourceID, @categoryTreeID=@categoryTreeID OUTPUT;

	set @categoryTreeID = null;
	select @categoryTreeID = categoryTreeID from dbo.cms_categoryTrees where controllingSiteResourceID = @controllingSiteResourceID and categoryTreeCode = 'ETCONTRIBUTIONS';
	IF @categoryTreeID is NULL BEGIN
		exec dbo.cms_createCategoryTree @siteID=@siteID, @categoryTreeName='ContributionTemplates', 
			@categoryTreeDesc='Contribution Templates', @categoryTreeCode='ETCONTRIBUTIONS',
			@controllingSiteResourceID=@controllingSiteResourceID, @categoryTreeID=@categoryTreeID OUTPUT;
		exec dbo.cms_createCategory @categoryTreeID=@categoryTreeID, @categoryName='Contribution Templates',
			@categoryDesc='', @categoryCode='', @parentCategoryID=NULL, @contributorMemberID=@contributingMemberID,
			@categoryID=@templateCategoryID OUTPUT;
	END

	set @categoryTreeID = null;
	select @categoryTreeID = categoryTreeID from dbo.cms_categoryTrees where controllingSiteResourceID = @controllingSiteResourceID and categoryTreeCode = 'ETGROUPS';
	IF @categoryTreeID is NULL BEGIN
		exec dbo.cms_createCategoryTree @siteID=@siteID, @categoryTreeName='GroupTemplates', 
			@categoryTreeDesc='Group Templates', @categoryTreeCode='ETGROUPS',
			@controllingSiteResourceID=@controllingSiteResourceID, @categoryTreeID=@categoryTreeID OUTPUT;
		exec dbo.cms_createCategory @categoryTreeID=@categoryTreeID, @categoryName='Group Templates',
			@categoryDesc='', @categoryCode='', @parentCategoryID=NULL, @contributorMemberID=@contributingMemberID,
			@categoryID=@templateCategoryID OUTPUT;
	END

	set @categoryTreeID = null;
	select @categoryTreeID = categoryTreeID from dbo.cms_categoryTrees where controllingSiteResourceID = @controllingSiteResourceID and categoryTreeCode = 'ETSUBS';
	IF @categoryTreeID is NULL BEGIN
		exec dbo.cms_createCategoryTree @siteID=@siteID, @categoryTreeName='SubscriptionTemplates', 
			@categoryTreeDesc='Subscription Email Templates', @categoryTreeCode='ETSUBS',
			@controllingSiteResourceID=@controllingSiteResourceID, @categoryTreeID=@categoryTreeID OUTPUT;
		exec dbo.cms_createCategory @categoryTreeID=@categoryTreeID, @categoryName='Subscription Templates',
			@categoryDesc='', @categoryCode='', @parentCategoryID=NULL, @contributorMemberID=@contributingMemberID,
			@categoryID=@templateCategoryID OUTPUT;
	END

	set @categoryTreeID = null;
	select @categoryTreeID = categoryTreeID from dbo.cms_categoryTrees where controllingSiteResourceID = @controllingSiteResourceID and categoryTreeCode = 'ETINVOICES';
	IF @categoryTreeID is NULL BEGIN
		exec dbo.cms_createCategoryTree @siteID=@siteID, @categoryTreeName='InvoiceTemplates', 
			@categoryTreeDesc='Invoice Email Templates', @categoryTreeCode='ETINVOICES',
			@controllingSiteResourceID=@controllingSiteResourceID, @categoryTreeID=@categoryTreeID OUTPUT;
		exec dbo.cms_createCategory @categoryTreeID=@categoryTreeID, @categoryName='Invoice Templates',
			@categoryDesc='', @categoryCode='', @parentCategoryID=NULL, @contributorMemberID=@contributingMemberID,
			@categoryID=@templateCategoryID OUTPUT;
	END

	set @categoryTreeID = null;
	select @categoryTreeID = categoryTreeID from dbo.cms_categoryTrees where controllingSiteResourceID = @controllingSiteResourceID and categoryTreeCode = 'ETEVENTS';
	IF @categoryTreeID is NULL BEGIN
		exec dbo.cms_createCategoryTree @siteID=@siteID, @categoryTreeName='EventTemplates', 
			@categoryTreeDesc='Event Email Templates', @categoryTreeCode='ETEVENTS',
			@controllingSiteResourceID=@controllingSiteResourceID, @categoryTreeID=@categoryTreeID OUTPUT;
		exec dbo.cms_createCategory @categoryTreeID=@categoryTreeID, @categoryName='Event Templates',
			@categoryDesc='', @categoryCode='', @parentCategoryID=NULL, @contributorMemberID=@contributingMemberID,
			@categoryID=@templateCategoryID OUTPUT;
	END

	set @categoryTreeID = null;
	select @categoryTreeID = categoryTreeID from dbo.cms_categoryTrees where controllingSiteResourceID = @controllingSiteResourceID and categoryTreeCode = 'ETREFCLIENTS';
	IF @categoryTreeID is NULL BEGIN
		exec dbo.cms_createCategoryTree @siteID=@siteID, @categoryTreeName='New Referral to Client', 
			@categoryTreeDesc='This e-mail is sent to clients when a new referral is created', 
			@categoryTreeCode='ETREFCLIENTS', @controllingSiteResourceID=@controllingSiteResourceID, 
			@categoryTreeID=@categoryTreeID OUTPUT;
		exec dbo.cms_createCategory @categoryTreeID=@categoryTreeID, @categoryName='New Referral to Client',
			@categoryDesc='', @categoryCode='', @parentCategoryID=NULL, @contributorMemberID=@contributingMemberID,
			@categoryID=@templateCategoryID OUTPUT;
	END

	set @categoryTreeID = null;
	select @categoryTreeID = categoryTreeID from dbo.cms_categoryTrees where controllingSiteResourceID = @controllingSiteResourceID and categoryTreeCode = 'ETREFMEMBERS';
	IF @categoryTreeID is NULL BEGIN
		exec dbo.cms_createCategoryTree @siteID=@siteID, @categoryTreeName='New Referral to Members', 
			@categoryTreeDesc='This e-mail is sent to members when a new referral is created', 
			@categoryTreeCode='ETREFMEMBERS', @controllingSiteResourceID=@controllingSiteResourceID, 
			@categoryTreeID=@categoryTreeID OUTPUT;
		exec dbo.cms_createCategory @categoryTreeID=@categoryTreeID, @categoryName='New Referral to Members',
			@categoryDesc='', @categoryCode='', @parentCategoryID=NULL, @contributorMemberID=@contributingMemberID,
			@categoryID=@templateCategoryID OUTPUT;
	END

	set @categoryTreeID = null;
	select @categoryTreeID = categoryTreeID from dbo.cms_categoryTrees where controllingSiteResourceID = @controllingSiteResourceID and categoryTreeCode = 'ETREFCLIENTRCPT';
	IF @categoryTreeID is NULL BEGIN
		exec dbo.cms_createCategoryTree @siteID=@siteID, @categoryTreeName='Client Receipt', 
			@categoryTreeDesc='This e-mail is sent to clients after submitting referral payment', 
			@categoryTreeCode='ETREFCLIENTRCPT', @controllingSiteResourceID=@controllingSiteResourceID, 
			@categoryTreeID=@categoryTreeID OUTPUT;
		exec dbo.cms_createCategory @categoryTreeID=@categoryTreeID, @categoryName='Client Receipt',
			@categoryDesc='', @categoryCode='', @parentCategoryID=NULL, @contributorMemberID=@contributingMemberID,
			@categoryID=@templateCategoryID OUTPUT;
	END

   set @categoryTreeID = null;
	select @categoryTreeID = categoryTreeID from dbo.cms_categoryTrees where controllingSiteResourceID = @controllingSiteResourceID and categoryTreeCode = 'ETREFFEEDSCRPNCY';
	IF @categoryTreeID is NULL BEGIN
		exec dbo.cms_createCategoryTree @siteID=@siteID, @categoryTreeName='FeeDiscrepancy Templates', 
			@categoryTreeDesc='Templates to Send Fee Discrepancy Email', 
			@categoryTreeCode='ETREFFEEDSCRPNCY', @controllingSiteResourceID=@controllingSiteResourceID, 
			@categoryTreeID=@categoryTreeID OUTPUT;
		exec dbo.cms_createCategory @categoryTreeID=@categoryTreeID, @categoryName='Fee Discrepancy Templates',
			@categoryDesc='', @categoryCode='', @parentCategoryID=NULL, @contributorMemberID=@contributingMemberID,
			@categoryID=@templateCategoryID OUTPUT;
	END

	set @categoryTreeID = null;
	select @categoryTreeID = categoryTreeID from dbo.cms_categoryTrees where controllingSiteResourceID = @controllingSiteResourceID and categoryTreeCode = 'ETREFREPORT';
	IF @categoryTreeID is NULL BEGIN
		exec dbo.cms_createCategoryTree @siteID=@siteID, @categoryTreeName='Automated Referral Reports', 
			@categoryTreeDesc='This is an scheduled e-mail sent to Members', 
			@categoryTreeCode='ETREFREPORT', @controllingSiteResourceID=@controllingSiteResourceID, 
			@categoryTreeID=@categoryTreeID OUTPUT;
		exec dbo.cms_createCategory @categoryTreeID=@categoryTreeID, @categoryName='Automated Referral Reports',
			@categoryDesc='', @categoryCode='', @parentCategoryID=NULL, @contributorMemberID=@contributingMemberID,
			@categoryID=@templateCategoryID OUTPUT;
	END
	
	set @categoryTreeID = null;
	select @categoryTreeID = categoryTreeID from dbo.cms_categoryTrees where controllingSiteResourceID = @controllingSiteResourceID and categoryTreeCode = 'ETREFCSTRACKREPORT';
	IF @categoryTreeID is NULL BEGIN
		exec dbo.cms_createCategoryTree @siteID=@siteID, @categoryTreeName='Automated Internal Case Tracking Reports', 
			@categoryTreeDesc='Automated Internal Case Tracking Report Templates', 
			@categoryTreeCode='ETREFCSTRACKREPORT', @controllingSiteResourceID=@controllingSiteResourceID, 
			@categoryTreeID=@categoryTreeID OUTPUT;
		exec dbo.cms_createCategory @categoryTreeID=@categoryTreeID, @categoryName='Automated Internal Case Tracking Reports',
			@categoryDesc='', @categoryCode='', @parentCategoryID=NULL, @contributorMemberID=@contributingMemberID,
			@categoryID=@templateCategoryID OUTPUT;
	END

	set @categoryTreeID = null;
	select @categoryTreeID = categoryTreeID from dbo.cms_categoryTrees where controllingSiteResourceID = @controllingSiteResourceID and categoryTreeCode = 'ETTASKS';
	IF @categoryTreeID is NULL BEGIN
		exec dbo.cms_createCategoryTree @siteID=@siteID, @categoryTreeName='TaskTemplates', 
			@categoryTreeDesc='Task Email Templates', @categoryTreeCode='ETTASKS',
			@controllingSiteResourceID=@controllingSiteResourceID, @categoryTreeID=@categoryTreeID OUTPUT;
		exec dbo.cms_createCategory @categoryTreeID=@categoryTreeID, @categoryName='Task Templates',
			@categoryDesc='', @categoryCode='', @parentCategoryID=NULL, @contributorMemberID=@contributingMemberID,
			@categoryID=@templateCategoryID OUTPUT;
	END

	set @categoryTreeID = null;
	select @categoryTreeID = categoryTreeID from dbo.cms_categoryTrees where controllingSiteResourceID = @controllingSiteResourceID and categoryTreeCode = 'ETSEMWEB';
	IF @categoryTreeID is NULL BEGIN
		exec dbo.cms_createCategoryTree @siteID=@siteID, @categoryTreeName='SeminarWebTemplates', 
			@categoryTreeDesc='SeminarWeb Email Templates', @categoryTreeCode='ETSEMWEB',
			@controllingSiteResourceID=@controllingSiteResourceID, @categoryTreeID=@categoryTreeID OUTPUT;
		exec dbo.cms_createCategory @categoryTreeID=@categoryTreeID, @categoryName='SeminarWeb Templates',
			@categoryDesc='', @categoryCode='', @parentCategoryID=NULL, @contributorMemberID=@contributingMemberID,
			@categoryID=@templateCategoryID OUTPUT;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

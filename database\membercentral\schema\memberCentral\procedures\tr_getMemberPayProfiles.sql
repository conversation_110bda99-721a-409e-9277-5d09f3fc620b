CREATE PROC dbo.tr_getMemberPayProfiles
@siteID int,
@orgID int,
@memberID int,
@limitToPayProfileID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

	IF OBJECT_ID('tempdb..#tmpPayProfiles') IS NOT NULL 
		DROP TABLE #tmpPayProfiles;
	CREATE TABLE #tmpPayProfiles (payProfileID int, nickname varchar(30), detail varchar(50), expiration date, failedLastDate datetime, profileID int,
		profileName varchar(100), tabTitle varchar(100), enableSurcharge bit, surchargePercent decimal(5,2), surchargeEligible bit, dateAdded datetime,
		gatewayClass varchar(20), gatewayID int, cardType varchar(30), profileSiteID int, payProfileSiteID int, payProfileSiteName varchar(60),
		enableProcessingFeeDonation bit, processFeeDonationFeePercent decimal(5,2), processingFeeLabel varchar(30), processFeeDonationFETitle varchar(100),
		processFeeDonationFEMsg varchar(800), processFeeDonationDefaultSelect bit, processFeeContributionsFELabel varchar(200), 
		processFeeContributionsFEDenyLabel varchar(200), processFeeSubscriptionsFELabel varchar(200), processFeeSubscriptionsFEDenyLabel varchar(200), 
		processFeeOtherPaymentsFELabel varchar(200), processFeeOtherPaymentsFEDenyLabel varchar(200), bankAccountType varchar(10));

	INSERT INTO #tmpPayProfiles (payProfileID, nickname, detail, expiration, failedLastDate, profileID,
		profileName, tabTitle, enableSurcharge, surchargePercent, surchargeEligible, dateAdded,
		gatewayClass, gatewayID, cardType, profileSiteID, payProfileSiteID, payProfileSiteName,
		enableProcessingFeeDonation, processFeeDonationFeePercent, processingFeeLabel, processFeeDonationFETitle,
		processFeeDonationFEMsg, processFeeDonationDefaultSelect, processFeeContributionsFELabel, 
		processFeeContributionsFEDenyLabel, processFeeSubscriptionsFELabel, processFeeSubscriptionsFEDenyLabel, 
		processFeeOtherPaymentsFELabel, processFeeOtherPaymentsFEDenyLabel)
	SELECT mpp.payProfileID, mpp.nickname, mpp.detail, mpp.expiration, mpp.failedLastDate, p.profileID, p.profileName, 
		p.tabTitle, p.enableSurcharge, p.surchargePercent, mpp.surchargeEligible, mpp.dateAdded, 
		g.gatewayClass, g.gatewayID, ct.cardtype, p.siteID, p.siteID, s.siteName,
		p.enableProcessingFeeDonation, p.processFeeDonationFeePercent, p.processingFeeLabel,
		pfm.title as processFeeDonationFETitle, pfm.message as processFeeDonationFEMsg, p.processFeeDonationDefaultSelect,
		p.processFeeContributionsFELabel, p.processFeeContributionsFEDenyLabel,
		p.processFeeSubscriptionsFELabel, p.processFeeSubscriptionsFEDenyLabel,
		p.processFeeOtherPaymentsFELabel, p.processFeeOtherPaymentsFEDenyLabel
	FROM dbo.ams_memberPaymentProfiles AS mpp
	INNER JOIN dbo.mp_profiles AS p on p.profileID = mpp.profileID
		AND p.siteID = @siteID
		AND p.[status] IN ('A','I')
		AND mpp.payProfileID = ISNULL(@limitToPayProfileID,mpp.payProfileID)
	INNER JOIN dbo.mp_gateways AS g ON g.gatewayID = p.gatewayID
		AND g.isActive = 1
		AND g.gatewayClass = 'creditcard'
	INNER JOIN dbo.sites AS s ON s.siteID = p.siteID
	LEFT OUTER JOIN dbo.mp_cardTypes AS ct ON ct.cardTypeID = mpp.cardTypeID
	LEFT OUTER JOIN dbo.tr_solicitationMessages AS pfm ON pfm.siteID = @siteID
		AND pfm.messageID = p.solicitationMessageID
	WHERE mpp.[status] = 'A'
	AND mpp.memberID = @memberID;

	INSERT INTO #tmpPayProfiles (payProfileID, nickname, detail, failedLastDate, profileID, profileName, tabTitle, 
		gatewayClass, gatewayID, profileSiteID, payProfileSiteID, payProfileSiteName, dateAdded, bankAccountType)
	SELECT DISTINCT mpp.payProfileID, mpp.nickname, mpp.detail, mpp.failedLastDate, p.profileID, p.profileName, 
		p.tabTitle, g.gatewayClass, g.gatewayID, p.siteID, s.siteID, s.siteName, mpp.dateAdded, b.acctType
	FROM dbo.ams_memberPaymentProfiles AS mpp
	INNER JOIN dbo.tr_bankAccounts as b on b.MPPPayProfileID = mpp.payProfileID
	INNER JOIN dbo.mp_profiles AS p on p.profileID = mpp.profileID
		AND p.siteID = @siteID
		AND p.[status] IN ('A','I')
		AND mpp.payProfileID = ISNULL(@limitToPayProfileID,mpp.payProfileID)
	INNER JOIN dbo.mp_gateways AS g ON g.gatewayID = p.gatewayID
		AND g.isActive = 1
		AND g.gatewayClass = 'bankdraft'
	INNER JOIN dbo.sites AS s ON s.siteID = b.siteID
	WHERE mpp.[status] = 'A'
	AND mpp.memberID = @memberID;

	SELECT payProfileID, nickname, detail, expiration, failedLastDate, profileID, profileName, tabTitle, 
		ISNULL(enableSurcharge,0) AS enableSurcharge, surchargePercent, surchargeEligible, gatewayClass, gatewayID, cardType, profileSiteID, 
		payProfileSiteID, payProfileSiteName, ISNULL(enableProcessingFeeDonation,0) AS enableProcessingFeeDonation, processFeeDonationFeePercent, 
		processingFeeLabel, processFeeDonationFETitle, processFeeDonationFEMsg, processFeeDonationDefaultSelect, 
		processFeeContributionsFELabel, processFeeContributionsFEDenyLabel, processFeeSubscriptionsFELabel, 
		processFeeSubscriptionsFEDenyLabel, processFeeOtherPaymentsFELabel, processFeeOtherPaymentsFEDenyLabel, dateAdded, bankAccountType,
		CASE WHEN profileSiteID <> payProfileSiteID THEN 'Associated with ' + payProfileSiteName ELSE '' END AS payProfileAssociatedWith
	FROM #tmpPayProfiles
	ORDER BY dateAdded DESC;

	IF OBJECT_ID('tempdb..#tmpPayProfiles') IS NOT NULL 
		DROP TABLE #tmpPayProfiles;

	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

	RETURN 0;
END TRY
BEGIN CATCH
	IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
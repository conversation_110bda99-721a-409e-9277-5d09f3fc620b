html,
body {
    font-family: proxima-nova, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"
}

body {
    display: flex;
    flex-direction: column;
    min-height: 100vh
}

img {
    max-width: 100%
}

.page-template-default main {
    padding-left: .75rem;
    padding-right: .75rem
}

.page-template-default main>p {
    margin-bottom: 1rem
}

.page-template-default main>h1,
.page-template-default main>h2,
.page-template-default main>h3,
.page-template-default main>h4,
.page-template-default main>h5,
.page-template-default main>h6 {
    margin-bottom: .625rem
}

.page-template-default main>h1 {
    font-size: 1.5rem;
    line-height: 2rem
}

@media (min-width:1025px) {
    .page-template-default main>h1 {
        font-size: 2.25rem;
        line-height: 2.5rem
    }
}

.page-template-default main>h2 {
    font-size: 1.25rem;
    line-height: 1.75rem
}

@media (min-width:1025px) {
    .page-template-default main>h2 {
        font-size: 1.5rem;
        line-height: 2rem
    }
}

.page-template-default main>h3 {
    font-size: 1.25rem;
    line-height: 1.75rem
}

.page-template-default main>p {
    margin-bottom: 1rem
}

.page-template-default main>a {
    --tw-text-opacity: 1;
    color: rgba(39, 59, 156, var(--tw-text-opacity))
}

.page-template-default main>ul {
    padding-bottom: 1rem
}

.page-template-default main>ul li {
    margin-bottom: .625rem;
    padding-left: .75rem
}

.cis-separator {
    --tw-border-opacity: 1;
    border-color: rgba(247, 247, 248, var(--tw-border-opacity));
    border-width: 1px;
    margin-bottom: 1rem
}

.cis-button__link a,
.cis-button a {
    padding: .5rem 1rem !important;
    border: 1px solid #4568db !important;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600 !important;
    color: #4568db !important;
    background: 0 0 !important;
    transition: all .25s
}

.cis-button__link a:visited,
.cis-button a:visited {
    color: #4568db !important
}

.cis-button__link a:hover,
.cis-button a:hover {
    color: #fff !important;
    background: #4568db !important;
    text-decoration: none !important;
    transition: all .25s
}

.cis-button__link a:focus,
.cis-button a:focus {
    outline: 0
}

.sectionornament1:before {
    display: block;
    position: absolute;
    top: 0;
    left: -4000px;
    height: 64px;
    content: '';
    width: 4000px;
    background: #f7f7f8
}

.sectionornament1:after {
    display: block;
    position: absolute;
    top: 0;
    right: 0;
    margin-right: -4000px;
    content: '';
    width: 4000px;
    height: 64px;
    background: #f7f7f8
}

#events:before {
    display: block;
    position: absolute;
    top: 0;
    left: -4000px;
    height: 100%;
    content: '';
    width: 4000px;
    background: #f7f7f8
}

#events:after {
    display: block;
    position: absolute;
    top: 0;
    right: 0;
    margin-right: -4000px;
    content: '';
    width: 4000px;
    height: 100%;
    background: #f7f7f8
}

.upcomingEventsList li {
    margin-bottom: 25px !important
}

@media (min-width:1024px) {
    .upcomingEventsList li {
        margin-bottom: 40px !important
    }
}

.upcomingEventsList li:nth-child(1) {
    order: 2
}

.upcomingEventsList li:nth-child(2) {
    order: 1
}

.upcomingEventsList li:nth-child(3) {
    order: 0
}

.upcomingEventsList li .eventInfo {
    order: 1
}

@media (min-width:1024px) {
    .upcomingEventsList li .eventInfo {
        order: 0
    }
}

.upcomingEventsList li .eventTitle {
    order: 0
}

@media (min-width:1024px) {
    .upcomingEventsList li .eventTitle {
        order: 1
    }
}

.upcomingEventsList li .eventTitle a {
    color: #303030 !important;
    text-decoration: none !important
}

.upcomingEventsList li .eventTitle a:hover {
    color: #5077e3 !important
}

html,
body.home {
    overflow-x: hidden
}

.glass-blue {
    box-shadow: inset 10px 10px 5px 1000px rgba(80, 119, 227, .82)
}

.swiper-pagination-eventsarea {
    height: 20px;
    top: auto !important;
    bottom: 0 !important;
    right: 0 !important;
    left: initial !important;
    display: flex;
    flex-direction: row;
    justify-content: center;
    z-index: 60
}

@media (min-width:1024px) {
    .swiper-pagination-eventsarea {
        position: absolute;
        flex-direction: column;
        height: auto;
        width: 50px !important;
        top: 0 !important;
        right: 0 !important;
        left: initial !important
    }
}

.glass-blue .swiper-pagination-bullet {
    z-index: 10;
    width: 10px;
    height: 10px;
    margin-bottom: 20px !important;
    border: 1px solid #fff;
    background-color: transparent;
    opacity: 1
}

.glass-blue .swiper-pagination-bullet.swiper-pagination-bullet-active {
    background-color: #fff
}

#directory .control-group {
    margin-bottom: 1.25rem
}

#directory .control-label {
    display: none
}

.btn {
    --tw-bg-opacity: 1;
    background-color: rgba(255, 255, 255, var(--tw-bg-opacity));
    --tw-border-opacity: 1;
    border-color: rgba(80, 119, 227, var(--tw-border-opacity));
    border-radius: .375rem;
    border-width: 1px;
    cursor: pointer;
    font-weight: 600;
    font-size: .75rem;
    line-height: 1rem;
    padding-top: .25rem;
    padding-bottom: .25rem;
    padding-left: .75rem;
    padding-right: .75rem;
    --tw-text-opacity: 1;
    
}

.btn i {
    margin-right: .25rem
}

.btn.btn-primary {
    --tw-bg-opacity: 1;
    background-color: rgba(80, 119, 227, var(--tw-bg-opacity));
    --tw-text-opacity: 1;
    color: rgba(255, 255, 255, var(--tw-text-opacity))
}

.btn:focus {
    outline: 0
}

.btn2 {
    background: 0 0 !important;
    padding: .5rem 1rem !important;
    border: 1px solid #4568db !important;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600 !important;
    color: #4568db !important;
    transition: all .25s
}

.btn2:visited {
    color: #4568db !important
}

.btn2:hover {
    color: #fff !important;
    background: #4568db !important;
    text-decoration: none !important;
    transition: all .25s
}

.btn2:focus {
    outline: 0
}

input[type=text],
input[type=password] {
    min-width: 300px;
    max-width: 100%
}

input[type=text],
input[type=password] {
    margin-bottom: .25rem
}

input[type=text],
input[type=password] {
    padding: 2px 4px;
    border-bottom: 1px solid #5077f3
}

input[type=text]:focus,
input[type=password]:focus {
    border-bottom: 1px solid #303030;
    outline: 0
}

.alignnone {
    margin-left: 0;
    margin-right: 0;
    max-width: 100%;
    height: auto
}

.aligncenter {
    display: block;
    margin: (2rem/2) auto;
    height: auto
}

.alignleft,
.alignright {
    margin-bottom: (2rem/2);
    height: auto
}

@media (min-width:30rem) {
    .alignleft {
        float: left;
        margin-right: (2rem/2)
    }

    .alignright {
        float: right;
        margin-left: (2rem/2)
    }
}

.screen-reader-text {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    border: 0;
    color: #000;
    background: #fff
}

@media (max-width:1023px) {

    #search-menu-button.active,
    #search-close-button {
        min-width: 45px
    }

    #primary-nav a {
        display: block;
        font-weight: 600;
        font-size: 1.125rem;
        line-height: 1.75rem;
        padding-top: .625rem;
        padding-bottom: .625rem;
        padding-left: .75rem;
        padding-right: .75rem;
        line-height: 30px
    }

    #primary-nav>li {
        border-bottom-width: 2px;
        border-color: rgba(247, 247, 247, .7)
    }

    #primary-nav .sub-menu {
        display: none;
        background: #4568db
    }

    #primary-nav .sub-menu a {
        font-size: .75rem;
        line-height: 1rem;
        line-height: 1.5;
        padding-top: .625rem;
        padding-bottom: .625rem;
        padding-left: 2rem
    }

    #primary-nav .sub-menu a:hover {
        --tw-text-opacity: 1;
        color: rgba(232, 238, 255, var(--tw-text-opacity))
    }

    #primary-nav .sub-menu li:first-child a {
        padding-top: 1rem
    }

    #primary-nav .sub-menu li:last-child a {
        padding-bottom: 1rem
    }

    #primary-nav .menu-item-has-children>a {
        position: relative
    }

    #primary-nav .menu-item-has-children>a:after {
        content: '';
        display: block;
        position: absolute;
        top: calc(50% - 2px);
        right: 16px;
        width: 8px;
        height: 4px;
        /* background-image:url('/cis-content/themes/acba/public/images/svg/accordion-arrow-white.svg'); */
        background-size: 100%;
        background-repeat: no-repeat;
        transition: transform .1s linear
    }

    #primary-nav .menu-item-has-children>a.active:after {
        transform: rotate(180deg)
    }

    #nav-wrapper:not(.left-0) {
        left: -100%
    }

    #nav-wrapper {
        width: 270px;
        transition: left .2s linear;
        background: #547be3
    }

    #secondary-nav-mobile li {
        width: 48%
    }

    #secondary-nav-mobile a {
        display: block;
        font-weight: 400;
        font-size: .875rem;
        line-height: 1.25rem;
        margin-bottom: .25rem;
        padding-top: .25rem;
        padding-bottom: .25rem;
        padding-right: .25rem;
        padding-left: .75rem
    }

    #mobile-mask {
        -webkit-backdrop-filter: blur(4px);
        backdrop-filter: blur(4px)
    }

    #search-form-wrapper .searchform {
        display: flex;
        width: 100%
    }

    #search-form-wrapper .searchform label {
        display: flex;
        width: 100%
    }

    #search-form-wrapper .searchform div>* {
        display: contents
    }

    #search-form-wrapper #s {
        padding-left: .75rem;
        padding-right: .75rem;
        width: 100%
    }

    #search-form-wrapper #s:focus {
        outline: 0
    }

    #search-form-wrapper .screen-reader-text {
        line-height: 1
    }
}

@media (min-width:1024px) {
    #primary-nav {
        font-size: .875rem;
        line-height: 1.25rem
    }

    #primary-nav>li {
        margin-left: 1rem;
        position: relative
    }

    #primary-nav>li>a {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%
    }

    #primary-nav>li>a:hover {
        --tw-text-opacity: 1;
        color: rgba(69, 104, 219, var(--tw-text-opacity))
    }

    #primary-nav>li.current-menu-parent>a,
    #primary-nav>li.current-menu-item>a {
        --tw-text-opacity: 1;
        color: rgba(69, 104, 219, var(--tw-text-opacity))
    }

    #primary-nav>li.menu-item-has-children>a {
        padding-right: 1rem
    }

    #primary-nav>li.menu-item-has-children>a:after {
        content: '';
        display: block;
        position: absolute;
        top: calc(50% - 2px);
        right: -4px;
        width: 12px;
        height: 8px;
        /* background-image:url('/cis-content/themes/acba/public/images/svg/dropdown-desktop.svg'); */
        background-size: 100%;
        background-repeat: no-repeat;
        transition: transform .1s linear
    }

    #primary-nav .sub-menu {
        --tw-bg-opacity: 1;
        background-color: rgba(69, 104, 219, var(--tw-bg-opacity));
        display: none;
        position: absolute
    }

    #primary-nav .sub-menu a {
        display: block;
        padding-top: .625rem;
        padding-bottom: .625rem;
        padding-left: .75rem;
        padding-right: .75rem;
        --tw-text-opacity: 1;
        color: rgba(255, 255, 255, var(--tw-text-opacity));
        white-space: nowrap
    }

    #primary-nav .sub-menu a:hover {
        --tw-bg-opacity: 1;
        background-color: rgba(247, 247, 248, var(--tw-bg-opacity));
        --tw-text-opacity: 1;
        color: rgba(69, 104, 219, var(--tw-text-opacity))
    }

    #search {
        font-size: .95rem;
        line-height: 1.5rem
    }

    #search .searchform {
        display: flex;
        position: relative
    }

    #search .searchform label {
        display: block;
        width: 100%
    }

    #search #s_key_all {
        --tw-bg-opacity: 1;
        background-color: rgba(247, 247, 248, var(--tw-bg-opacity));
        border-radius: 9999px;
        padding-left: .75rem;
        padding: right 2rem;
        width: 180px;
        border: 1px solid #e7e7e7;
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none
    }

    #search #s_key_all:focus {
        outline: 0;
        border: 1px solid #5077e3
    }

    #search #searchsubmit {
        right: 0;
        width: 40px;
        height: 40px;
        font-size: 0;
        background-image: url(../images/search-icon.svg);
        background-position: center;
        background-repeat: no-repeat;
        background-size: 32%;
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
        background-color: transparent;
        position: absolute;
        padding: 5px;
    }

    #search #searchsubmit:focus {
        outline: 0;
        /* background-image:url('/cis-content/themes/acba/public/images/svg/search-icon-focus.svg') */
    }

    #user-menu {
        top: -14px
    }

    input[type=text]::-ms-clear {
        display: none;
        width: 0;
        height: 0
    }

    input[type=text]::-ms-reveal {
        display: none;
        width: 0;
        height: 0
    }

    input[type=search]::-webkit-search-decoration,
    input[type=search]::-webkit-search-cancel-button,
    input[type=search]::-webkit-search-results-button,
    input[type=search]::-webkit-search-results-decoration {
        display: none
    }
}

@media (min-width:1280px) {
    #primary-nav>li {
        position: relative
    }

    #primary-nav>li a {
        display: flex;
        align-items: center;
        justify-content: center
    }
}

#secondary-nav-desktop {
    font-size: .875rem;
    line-height: 1.25rem;
    padding-right: .75rem;
    height: 34px
}

#secondary-nav-desktop>li.current-menu-parent>a,
#secondary-nav-desktop>li.current-menu-item>a {
    --tw-text-opacity: 1;
    color: rgba(69, 104, 219, var(--tw-text-opacity))
}

#secondary-nav-desktop>li a:hover {
    --tw-text-opacity: 1;
    color: rgba(69, 104, 219, var(--tw-text-opacity))
}

#secondary-nav-desktop a {
    margin-left: 1.25rem;
    padding-right: .75rem;
    position: relative
}

#secondary-nav-desktop .menu-item-has-children {
    position: relative
}

#secondary-nav-desktop .menu-item-has-children>a:after {
    content: '';
    display: block;
    position: absolute;
    top: calc(50% - 2px);
    right: -4px;
    width: 12px;
    height: 8px;
    /* background-image:url('/cis-content/themes/acba/public/images/svg/dropdown-desktop.svg'); */
    background-size: 100%;
    background-repeat: no-repeat;
    transition: transform .1s linear
}

#secondary-nav-desktop .sub-menu {
    --tw-bg-opacity: 1;
    background-color: rgba(255, 255, 255, var(--tw-bg-opacity));
    --tw-bg-opacity: 1;
    background-color: rgba(69, 104, 219, var(--tw-bg-opacity));
    display: none;
    padding-bottom: .25rem;
    padding-top: .625rem;
    position: absolute;
    z-index: 50
}

@media (min-width:1025px) {
    #secondary-nav-desktop .sub-menu a {
        display: block;
        margin-left: 0;
        padding-top: .625rem;
        padding-bottom: .625rem;
        padding-left: .625rem;
        position: relative;
        --tw-text-opacity: 1;
        color: rgba(255, 255, 255, var(--tw-text-opacity));
        white-space: nowrap
    }

    #secondary-nav-desktop .sub-menu a:hover {
        --tw-bg-opacity: 1;
        background-color: rgba(255, 255, 255, var(--tw-bg-opacity));
        --tw-text-opacity: 1;
        color: rgba(69, 104, 219, var(--tw-text-opacity))
    }
}

#secondary-nav-mobile .menu-item-has-children>a {
    position: relative
}

#secondary-nav-mobile .menu-item-has-children>a:after {
    content: '';
    display: block;
    position: absolute;
    top: calc(50% - 2px);
    right: 16px;
    width: 8px;
    height: 4px;
    /* background-image:url('/cis-content/themes/acba/public/images/svg/accordion-arrow-white.svg'); */
    background-size: 100%;
    background-repeat: no-repeat;
    transition: transform .1s linear
}

#secondary-nav-mobile .menu-item-has-children>a.active:after {
    transform: rotate(180deg)
}

#secondary-nav-mobile .sub-menu {
    --tw-bg-opacity: 1;
    background-color: rgba(69, 104, 219, var(--tw-bg-opacity));
    display: none;
    position: absolute;
    z-index: 10
}

#secondary-nav-mobile .sub-menu a {
    font-size: .75rem;
    line-height: 1rem;
    line-height: 1.5;
    padding-top: .25rem;
    padding-bottom: .25rem;
    padding-left: .625rem
}

@media (min-width:1025px) {
    #secondary-nav-mobile .sub-menu a {
        padding-top: .625rem;
        padding-bottom: .625rem
    }
}

#secondary-nav-mobile .sub-menu a:hover {
    --tw-text-opacity: 1;
    color: rgba(232, 238, 255, var(--tw-text-opacity))
}

#secondary-nav-mobile .sub-menu li {
    width: 100%
}

#secondary-nav-mobile .sub-menu li:first-child a {
    padding-top: 1rem
}

#secondary-nav-mobile .sub-menu li:last-child a {
    padding-bottom: 1rem
}

@media (min-width:1280px) {
    #secondary-nav-desktop {
        padding-right: 0
    }
}

#search-form-wrapper input[type=text] {
    display: flex !important
}

#search-form-wrapper form>div {
    display: flex
}

footer {
    margin-top: auto
}

footer .logo {
    max-width: 104px
}

@media (max-width:767px) {
    footer .logo {
        margin-left: 34px
    }
}

@media (min-width:1024px) {
    footer .footer-block {
        height: 155px;
        padding-left: 22px;
        border-color: #979797
    }
}

footer a:hover {
    --tw-text-opacity: 1;
    color: rgba(69, 104, 219, var(--tw-text-opacity))
}

.iframe-wrapper {
    overflow: hidden;
    padding-top: 56.25%;
    position: relative
}

.iframe-wrapper iframe {
    border: 0;
    height: 100%;
    left: 0;
    position: absolute;
    top: 0;
    width: 100%
}

body#tinymce {
    margin: 12px !important
}

body#clearvantage {
    font-size: .75rem;
    line-height: 1rem
}

body#clearvantage .navbar {
    display: none !important
}

body#clearvantage h1,
body#clearvantage h2 {
    margin-bottom: .75rem;
    font-size: 24px
}

body#clearvantage h3,
body#clearvantage h4,
body#clearvantage h5,
body#clearvantage h6 {
    font-size: 18px
}

body#clearvantage .underline {
    text-decoration: none
}

body#clearvantage .form-actions {
    background-color: transparent
}

body#clearvantage .controls .btn {
    margin-right: .25rem
}

body#clearvantage .alert {
    padding: .25rem
}

body#clearvantage .alert.alert-error {
    border: 1px solid #e53935
}

body#clearvantage .alert.alert-success {
    border: 1px solid #5077e3
}

body#clearvantage .hide {
    display: none
}

#join-iframe {
    min-height: 1440px
}

@media (min-width:768px) {
    #join-iframe {
        min-height: 1054px
    }
}

#pagination-nav span,
#pagination-nav a {
    margin-left: .25rem;
    margin-right: .25rem
}

#pagination-nav .PageNavLink {
    --tw-text-opacity: 1;
    color: rgba(80, 119, 227, var(--tw-text-opacity))
}

#home-swiper {
    max-width: 100%
}

#home-swiper .swiper-slide {
    width: 100vw;
    height: calc(100vh - 220px)
}

#home-swiper .swiper-slide .swiper-pagination {
    bottom: 18px
}

#home-swiper .swiper-pagination-bullet {
    z-index: 10;
    width: 10px;
    height: 10px;
    margin: 0 8px;
    border: 1px solid #fff;
    background-color: transparent;
    opacity: 1
}

#home-swiper .swiper-pagination-bullet.swiper-pagination-bullet-active {
    background-color: #fff
}

#home-swiper .slide-triangle {
    background-color: #5077e3;
    opacity: .8;
    -webkit-clip-path: polygon(0 14%, 0% 100%, 74% 100%);
    clip-path: polygon(0 14%, 0% 100%, 74% 100%)
}

#home-swiper .slide-text {
    max-width: 270px;
    font-size: 46px;
    font-weight: 600
}

#home-swiper #below-slider-callouts .image-container {
    width: 107px;
    height: 88px
}

@media (min-width:1024px) {
    #home-swiper .swiper-slide {
        height: calc(100vh - 200px)
    }

    #home-swiper .slide-triangle {
        -webkit-clip-path: polygon(0 0, 34% 0, 71% 100%, 0% 100%);
        clip-path: polygon(0 0, 34% 0, 71% 100%, 0% 100%)
    }

    #home-swiper .slide-text {
        position: relative;
        z-index: 20;
        max-width: 780px;
        margin-bottom: 7%;
        margin-left: 5%;
        padding-left: 5%;
        border-left: 2px solid #fff
    }

    #home-swiper .swiper-pagination {
        position: relative;
        z-index: 20;
        display: flex;
        flex-direction: column;
        max-width: 1024px;
        margin-bottom: 27%
    }

    @media (min-width:1280px) {
        #home-swiper .swiper-pagination {
            max-width: 1280px
        }
    }

    #home-swiper .swiper-pagination-bullet {
        margin-bottom: 18px
    }

    #below-slider-callouts {
        margin-top: -42px
    }
}

@media (min-width:1024px) and (min-height:400px) {
    #home-swiper .slide-text {
        font-size: 86px
    }
}

@media (min-width:1024px) and (min-height:600px) {
    #home-swiper .slide-text {
        font-size: 96px
    }
}

@media (min-width:1024px) and (min-height:900px) {
    #home-swiper .slide-text {
        font-size: 106px
    }
}

body.home {
    --tw-bg-opacity: 1;
    background-color: rgba(255, 255, 255, var(--tw-bg-opacity))
}

body.home #before-event-content .floating-box {
    top: 5%;
    right: 0;
    bottom: 0;
    left: 15%;
    z-index: -1
}

body.home #before-event-content .google-ad-block {
    display: block;
    max-width: 336px
}

@media (min-width:1024px) {
    body.home #before-event-content {
        display: flex;
        margin-top: 3rem;
        margin-bottom: 3rem;
        position: relative
    }

    body.home #before-event-content .google-ad-block {
        max-width: 300px
    }



    body.home #main-content-image {
        max-width: 300px;
        margin-top: 34px;
        margin-right: 72px
    }

    body.home .other-floating-box {
        top: 0;
        right: 0;
        bottom: 20%;
        left: 5%;
        z-index: -1
    }
}

@media (min-width:1400px) {
    body.home #main-content-image {
        max-width: 394px
    }
}

body.home #below-event-callouts img {
    max-height: 155px
}

@media (min-width:768px) {
    body.home #below-event-callouts {
        max-width: 1060px
    }

    body.home #below-event-callouts .image-container {
        height: 262px
    }
}

@media (min-width:1024px) {
    body.home #below-event-callouts h2 {
        font-size: 36px;
        font-weight: 600;
        line-height: 45px
    }

    body.home #below-event-callouts .text-container {
        min-height: 235px
    }
}

#committee-index-iframe {
    min-height: 1080px
}

@media (min-width:768px) {
    #committee-index-iframe {
        min-height: 1020px
    }
}

@media (min-width:1024px) {
    #committee-index-iframe {
        min-height: 1300px
    }
}

#hero-header {
    width: 100%
}

#hero-header .swiper-slide {
    width: 100%;
    min-height: 370px
}

#hero-header .slide-triangle {
    background-color: #5077e3;
    opacity: .8;
    -webkit-clip-path: polygon(0 14%, 0% 100%, 74% 100%);
    clip-path: polygon(0 14%, 0% 100%, 74% 100%)
}

#hero-header .slide-text {
    max-width: 270px;
    font-size: 46px;
    font-weight: 600
}

#hero-header .slide-text.largetitle {
    max-width: 100%
}

@media (min-width:1024px) {
    #hero-header .swiper-slide {
        min-height: 320px
    }

    #hero-header .slide-triangle {
        -webkit-clip-path: polygon(0 0, 34% 0, 71% 100%, 0% 100%);
        clip-path: polygon(0 0, 34% 0, 71% 100%, 0% 100%)
    }

    #hero-header .slide-text {
        position: relative;
        z-index: 20;
        max-width: 883px
    }

    #hero-header .slide-text.largetitle {
        max-width: 100%
    }
}

@media (min-width:1024px) and (min-height:400px) {
    #hero-header .slide-text {
        font-size: 68px
    }
}

.wysiwyg-section p,
.wysiwyg-section ul {
    margin-bottom: 1rem
}

.wysiwyg-section p,
.wysiwyg-section ul {
    line-height: 30px
}

.wysiwyg-section ul {
    list-style-type: disc;
    margin-left: .75rem
}

.wysiwyg-section ol {
    list-style-type: decimal
}

.wysiwyg-section ol,
.wysiwyg-section ul {
    padding-left: .75rem;
    padding-bottom: 1rem
}

.wysiwyg-section ol li,
.wysiwyg-section ul li {
    margin-bottom: .625rem;
    margin-left: .75rem
}

.wysiwyg-section img {
    display: block;
    margin-bottom: .25rem
}

.wysiwyg-section h1,
.wysiwyg-section h2,
.wysiwyg-section h3,
.wysiwyg-section h4,
.wysiwyg-section h5,
.wysiwyg-section h6 {
    font-weight: 600;
    margin-bottom: .625rem
}

.wysiwyg-section h3,
.wysiwyg-section h4,
.wysiwyg-section h5 {
    line-height: 28px
}

@media (min-width:1024px) {
    .wysiwyg-section img {
        display: block;
        margin-bottom: 1.5rem
    }
}

@media (min-width:768px) {
    .multicolumns:not(.column-count-1) .cols {
        width: 48%
    }
}

@media (min-width:1024px) {
    .multicolumns.column-count-3 .cols {
        width: 30%
    }
}

@media (min-width:1400px) {
    .multicolumns.column-count-3 .cols {
        width: 394px
    }
}

.multicolumns.column-count-4 .cols {
    width: 48%
}

@media (min-width:1024px) {
    .multicolumns.column-count-4 .cols {
        width: 22%
    }
}

.narrow-pagecontent {
    width: 800px;
    max-width: 100%;
    margin: 0 auto
}

.cis-column h2 {
    font-weight: 600;
    margin-top: 1rem;
    margin-bottom: 1rem
}

.cis-column .c-accordion__title {
    padding: 10px
}

.narrow-pagecontent h1,
.narrow-pagecontent h2,
.narrow-pagecontent h3,
.narrow-pagecontent h4,
.narrow-pagecontent h5,
.narrow-pagecontent h6,
.cis-column h1,
.cis-column h2,
.cis-column h3,
.cis-column h4,
.cis-column h5,
.cis-column h6 {
    margin-bottom: .625rem
}

.narrow-pagecontent>h1,
.cis-column>h1 {
    font-size: 1.5rem;
    line-height: 2rem
}

@media (min-width:1025px) {

    .narrow-pagecontent>h1,
    .cis-column>h1 {
        font-size: 2.25rem;
        line-height: 2.5rem
    }
}

.narrow-pagecontent h2,
.cis-column h2 {
    font-size: 1.25rem;
    line-height: 1.75rem;
    margin-top: 1rem;
    margin-bottom: 1rem
}

@media (min-width:1025px) {

    .narrow-pagecontent h2,
    .cis-column h2 {
        font-size: 1.75rem;
        margin-top: 1.5rem;
        margin-bottom: 1.5rem
    }
}

.narrow-pagecontent h3,
.cis-column h3 {
    font-size: 1.25rem;
    line-height: 1.75rem
}

.narrow-pagecontent p,
.cis-column p {
    margin-bottom: 1rem
}

.narrow-pagecontent a,
.cis-column a {
    --tw-text-opacity: 1;
    color: rgba(39, 59, 156, var(--tw-text-opacity))
}

.narrow-pagecontent ol,
.cis-column ol {
    list-style-type: decimal
}

.narrow-pagecontent ul,
.cis-column ul {
    list-style-type: disc
}

.narrow-pagecontent ol,
.narrow-pagecontent ul,
.cis-column ol,
.cis-column ul {
    padding-left: .75rem;
    padding-bottom: 1rem
}

.narrow-pagecontent ol li,
.narrow-pagecontent ul li,
.cis-column ol li,
.cis-column ul li {
    margin-bottom: .625rem;
    margin-left: .75rem
}

.cis-pb-accordion-item {
    margin-bottom: .625rem
}

.cis-pb-accordion-item:focus {
    border: 0 !important;
    outline: none !important
}

.c-accordion__title {
    --tw-border-opacity: 1;
    border-color: rgba(247, 247, 248, var(--tw-border-opacity));
    border-bottom-width: 2px;
    font-size: 1.5rem;
    line-height: 2rem;
    margin-bottom: .625rem
}

.c-accordion__title:focus {
    border: 0 !important;
    outline: none !important
}

.c-accordion__title {
    --tw-border-opacity: 1;
    border-color: rgba(247, 247, 248, var(--tw-border-opacity));
    border-bottom-width: 2px;
    font-size: 1.5rem;
    line-height: 2rem;
    margin-bottom: .625rem
}

.c-accordion__title:focus {
    border: 0 !important;
    outline: none !important
}

.c-accordion__content {
    padding: 0 10px
}

.cis-column .c-accordion__title {
    padding: 10px
}

.content main ol {
    list-style-type: decimal
}

.content main ul {
    list-style-type: disc
}

.content main ol,
.content main ul {
    padding-left: .75rem;
    padding-bottom: 1rem
}

.content main ol li,
.content main ul li {
    margin-bottom: .625rem;
    margin-left: .75rem
}

.content main a {
    --tw-text-opacity: 1;
    color: rgba(69, 104, 219, var(--tw-text-opacity))
}

.content main a body#clearvantage:hover {
    text-decoration: none
}

.content main a:hover {
    --tw-text-opacity: 1;
    color: rgba(39, 59, 156, var(--tw-text-opacity));
    text-decoration: underline
}

.content main #below-event-callouts a:hover {
    text-decoration: none
}

.content main #below-event-callouts a:hover body#clearvantage h3 {
    text-decoration: none
}

.content main #below-event-callouts a:hover h3 {
    text-decoration: underline
}

.below-event-callouts a:hover {
    text-decoration: none !important
}

#calendar .fc-daygrid-event {
    flex-wrap: nowrap
}

#calendar .fc-event-title {
    white-space: normal;
    line-height: 1.2em;
    margin: .5em 0 .5em .25em
}

#calendar .fc-daygrid-event-harness {
    margin-top: 0 !important
}

@font-face {
    font-display: swap;
    font-family: 'swiper-icons';
    src: url('data:application/font-woff;charset=utf-8;base64,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') format('woff');
    font-weight: 400;
    font-style: normal
}

:root {
    --swiper-theme-color: #007aff
}

.swiper-container {
    margin-left: auto;
    margin-right: auto;
    position: relative;
    overflow: hidden;
    list-style: none;
    padding: 0;
    z-index: 1
}

.swiper-container-vertical>.swiper-wrapper {
    flex-direction: column
}

.swiper-wrapper {
    position: relative;
    width: 100%;
    height: 100%;
    z-index: 1;
    display: flex;
    transition-property: transform;
    box-sizing: content-box
}

.swiper-container-android .swiper-slide,
.swiper-wrapper {
    transform: translate3d(0px, 0, 0)
}

.swiper-container-multirow>.swiper-wrapper {
    flex-wrap: wrap
}

.swiper-container-multirow-column>.swiper-wrapper {
    flex-wrap: wrap;
    flex-direction: column
}

.swiper-container-free-mode>.swiper-wrapper {
    transition-timing-function: ease-out;
    margin: 0 auto
}

.swiper-container-pointer-events {
    touch-action: pan-y
}

.swiper-container-pointer-events.swiper-container-vertical {
    touch-action: pan-x
}

.swiper-slide {
    flex-shrink: 0;
    width: 100%;
    height: 100%;
    position: relative;
    transition-property: transform
}

.swiper-slide-invisible-blank {
    visibility: hidden
}

.swiper-container-autoheight,
.swiper-container-autoheight .swiper-slide {
    height: auto
}

.swiper-container-autoheight .swiper-wrapper {
    align-items: flex-start;
    transition-property: transform, height
}

.swiper-container-3d {
    perspective: 1200px
}

.swiper-container-3d .swiper-wrapper,
.swiper-container-3d .swiper-slide,
.swiper-container-3d .swiper-slide-shadow-left,
.swiper-container-3d .swiper-slide-shadow-right,
.swiper-container-3d .swiper-slide-shadow-top,
.swiper-container-3d .swiper-slide-shadow-bottom,
.swiper-container-3d .swiper-cube-shadow {
    transform-style: preserve-3d
}

.swiper-container-3d .swiper-slide-shadow-left,
.swiper-container-3d .swiper-slide-shadow-right,
.swiper-container-3d .swiper-slide-shadow-top,
.swiper-container-3d .swiper-slide-shadow-bottom {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 10
}

.swiper-container-3d .swiper-slide-shadow-left {
    background-image: linear-gradient(to left, rgba(0, 0, 0, .5), rgba(0, 0, 0, 0))
}

.swiper-container-3d .swiper-slide-shadow-right {
    background-image: linear-gradient(to right, rgba(0, 0, 0, .5), rgba(0, 0, 0, 0))
}

.swiper-container-3d .swiper-slide-shadow-top {
    background-image: linear-gradient(to top, rgba(0, 0, 0, .5), rgba(0, 0, 0, 0))
}

.swiper-container-3d .swiper-slide-shadow-bottom {
    background-image: linear-gradient(to bottom, rgba(0, 0, 0, .5), rgba(0, 0, 0, 0))
}

.swiper-container-css-mode>.swiper-wrapper {
    overflow: auto;
    scrollbar-width: none;
    -ms-overflow-style: none
}

.swiper-container-css-mode>.swiper-wrapper::-webkit-scrollbar {
    display: none
}

.swiper-container-css-mode>.swiper-wrapper>.swiper-slide {
    scroll-snap-align: start start
}

.swiper-container-horizontal.swiper-container-css-mode>.swiper-wrapper {
    -ms-scroll-snap-type: x mandatory;
    scroll-snap-type: x mandatory
}

.swiper-container-vertical.swiper-container-css-mode>.swiper-wrapper {
    -ms-scroll-snap-type: y mandatory;
    scroll-snap-type: y mandatory
}

:root {
    --swiper-navigation-size: 44px
}

.swiper-button-prev,
.swiper-button-next {
    position: absolute;
    top: 50%;
    width: calc(var(--swiper-navigation-size)/44*27);
    height: var(--swiper-navigation-size);
    margin-top: calc(-1*var(--swiper-navigation-size)/2);
    z-index: 10;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--swiper-navigation-color, var(--swiper-theme-color))
}

.swiper-button-prev.swiper-button-disabled,
.swiper-button-next.swiper-button-disabled {
    opacity: .35;
    cursor: auto;
    pointer-events: none
}

.swiper-button-prev:after,
.swiper-button-next:after {
    font-family: swiper-icons;
    font-size: var(--swiper-navigation-size);
    text-transform: none !important;
    letter-spacing: 0;
    text-transform: none;
    font-variant: initial;
    line-height: 1
}

.swiper-button-prev,
.swiper-container-rtl .swiper-button-next {
    left: 10px;
    right: auto
}

.swiper-button-prev:after,
.swiper-container-rtl .swiper-button-next:after {
    content: 'prev'
}

.swiper-button-next,
.swiper-container-rtl .swiper-button-prev {
    right: 10px;
    left: auto
}

.swiper-button-next:after,
.swiper-container-rtl .swiper-button-prev:after {
    content: 'next'
}

.swiper-button-prev.swiper-button-white,
.swiper-button-next.swiper-button-white {
    --swiper-navigation-color: #fff
}

.swiper-button-prev.swiper-button-black,
.swiper-button-next.swiper-button-black {
    --swiper-navigation-color: #000
}

.swiper-button-lock {
    display: none
}

.swiper-pagination {
    position: absolute;
    text-align: center;
    transition: .3s opacity;
    transform: translate3d(0, 0, 0);
    z-index: 10
}

.swiper-pagination.swiper-pagination-hidden {
    opacity: 0
}

.swiper-pagination-fraction,
.swiper-pagination-custom,
.swiper-container-horizontal>.swiper-pagination-bullets {
    bottom: 10px;
    left: 0;
    width: 100%
}

.swiper-pagination-bullets-dynamic {
    overflow: hidden;
    font-size: 0
}

.swiper-pagination-bullets-dynamic .swiper-pagination-bullet {
    transform: scale(.33);
    position: relative
}

.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active {
    transform: scale(1)
}

.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-main {
    transform: scale(1)
}

.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-prev {
    transform: scale(.66)
}

.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-prev-prev {
    transform: scale(.33)
}

.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-next {
    transform: scale(.66)
}

.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-next-next {
    transform: scale(.33)
}

.swiper-pagination-bullet {
    width: 8px;
    height: 8px;
    display: inline-block;
    border-radius: 50%;
    background: #000;
    opacity: .2
}

button.swiper-pagination-bullet {
    border: none;
    margin: 0;
    padding: 0;
    box-shadow: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none
}

.swiper-pagination-clickable .swiper-pagination-bullet {
    cursor: pointer
}

.swiper-pagination-bullet-active {
    opacity: 1;
    background: var(--swiper-pagination-color, var(--swiper-theme-color))
}

.swiper-container-vertical>.swiper-pagination-bullets {
    right: 10px;
    top: 50%;
    transform: translate3d(0px, -50%, 0)
}

.swiper-container-vertical>.swiper-pagination-bullets .swiper-pagination-bullet {
    margin: 6px 0;
    display: block
}

.swiper-container-vertical>.swiper-pagination-bullets.swiper-pagination-bullets-dynamic {
    top: 50%;
    transform: translateY(-50%);
    width: 8px
}

.swiper-container-vertical>.swiper-pagination-bullets.swiper-pagination-bullets-dynamic .swiper-pagination-bullet {
    display: inline-block;
    transition: .2s transform, .2s top
}

.swiper-container-horizontal>.swiper-pagination-bullets .swiper-pagination-bullet {
    margin: 0 4px
}

.swiper-container-horizontal>.swiper-pagination-bullets.swiper-pagination-bullets-dynamic {
    left: 50%;
    transform: translateX(-50%);
    white-space: nowrap
}

.swiper-container-horizontal>.swiper-pagination-bullets.swiper-pagination-bullets-dynamic .swiper-pagination-bullet {
    transition: .2s transform, .2s left
}

.swiper-container-horizontal.swiper-container-rtl>.swiper-pagination-bullets-dynamic .swiper-pagination-bullet {
    transition: .2s transform, .2s right
}

.swiper-pagination-progressbar {
    background: rgba(0, 0, 0, .25);
    position: absolute
}

.swiper-pagination-progressbar .swiper-pagination-progressbar-fill {
    background: var(--swiper-pagination-color, var(--swiper-theme-color));
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    transform: scale(0);
    transform-origin: left top
}

.swiper-container-rtl .swiper-pagination-progressbar .swiper-pagination-progressbar-fill {
    transform-origin: right top
}

.swiper-container-horizontal>.swiper-pagination-progressbar,
.swiper-container-vertical>.swiper-pagination-progressbar.swiper-pagination-progressbar-opposite {
    width: 100%;
    height: 4px;
    left: 0;
    top: 0
}

.swiper-container-vertical>.swiper-pagination-progressbar,
.swiper-container-horizontal>.swiper-pagination-progressbar.swiper-pagination-progressbar-opposite {
    width: 4px;
    height: 100%;
    left: 0;
    top: 0
}

.swiper-pagination-white {
    --swiper-pagination-color: #fff
}

.swiper-pagination-black {
    --swiper-pagination-color: #000
}

.swiper-pagination-lock {
    display: none
}

.swiper-scrollbar {
    border-radius: 10px;
    position: relative;
    -ms-touch-action: none;
    background: rgba(0, 0, 0, .1)
}

.swiper-container-horizontal>.swiper-scrollbar {
    position: absolute;
    left: 1%;
    bottom: 3px;
    z-index: 50;
    height: 5px;
    width: 98%
}

.swiper-container-vertical>.swiper-scrollbar {
    position: absolute;
    right: 3px;
    top: 1%;
    z-index: 50;
    width: 5px;
    height: 98%
}

.swiper-scrollbar-drag {
    height: 100%;
    width: 100%;
    position: relative;
    background: rgba(0, 0, 0, .5);
    border-radius: 10px;
    left: 0;
    top: 0
}

.swiper-scrollbar-cursor-drag {
    cursor: move
}

.swiper-scrollbar-lock {
    display: none
}

.swiper-zoom-container {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center
}

.swiper-zoom-container>img,
.swiper-zoom-container>svg,
.swiper-zoom-container>canvas {
    max-width: 100%;
    max-height: 100%;
    -o-object-fit: contain;
    object-fit: contain
}

.swiper-slide-zoomed {
    cursor: move
}

.swiper-lazy-preloader {
    width: 42px;
    height: 42px;
    position: absolute;
    left: 50%;
    top: 50%;
    margin-left: -21px;
    margin-top: -21px;
    z-index: 10;
    transform-origin: 50%;
    -webkit-animation: swiper-preloader-spin 1s infinite linear;
    animation: swiper-preloader-spin 1s infinite linear;
    box-sizing: border-box;
    border: 4px solid var(--swiper-preloader-color, var(--swiper-theme-color));
    border-radius: 50%;
    border-top-color: transparent
}

.swiper-lazy-preloader-white {
    --swiper-preloader-color: #fff
}

.swiper-lazy-preloader-black {
    --swiper-preloader-color: #000
}

@-webkit-keyframes swiper-preloader-spin {
    100% {
        transform: rotate(360deg)
    }
}

@keyframes swiper-preloader-spin {
    100% {
        transform: rotate(360deg)
    }
}

.swiper-container .swiper-notification {
    position: absolute;
    left: 0;
    top: 0;
    pointer-events: none;
    opacity: 0;
    z-index: -1000
}

.swiper-container-fade.swiper-container-free-mode .swiper-slide {
    transition-timing-function: ease-out
}

.swiper-container-fade .swiper-slide {
    pointer-events: none;
    transition-property: opacity
}

.swiper-container-fade .swiper-slide .swiper-slide {
    pointer-events: none
}

.swiper-container-fade .swiper-slide-active,
.swiper-container-fade .swiper-slide-active .swiper-slide-active {
    pointer-events: auto
}

.swiper-container-cube {
    overflow: visible
}

.swiper-container-cube .swiper-slide {
    pointer-events: none;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    z-index: 1;
    visibility: hidden;
    transform-origin: 0 0;
    width: 100%;
    height: 100%
}

.swiper-container-cube .swiper-slide .swiper-slide {
    pointer-events: none
}

.swiper-container-cube.swiper-container-rtl .swiper-slide {
    transform-origin: 100% 0
}

.swiper-container-cube .swiper-slide-active,
.swiper-container-cube .swiper-slide-active .swiper-slide-active {
    pointer-events: auto
}

.swiper-container-cube .swiper-slide-active,
.swiper-container-cube .swiper-slide-next,
.swiper-container-cube .swiper-slide-prev,
.swiper-container-cube .swiper-slide-next+.swiper-slide {
    pointer-events: auto;
    visibility: visible
}

.swiper-container-cube .swiper-slide-shadow-top,
.swiper-container-cube .swiper-slide-shadow-bottom,
.swiper-container-cube .swiper-slide-shadow-left,
.swiper-container-cube .swiper-slide-shadow-right {
    z-index: 0;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden
}

.swiper-container-cube .swiper-cube-shadow {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    opacity: .6;
    z-index: 0
}

.swiper-container-cube .swiper-cube-shadow:before {
    content: '';
    background: #000;
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    right: 0;
    filter: blur(50px)
}

.swiper-container-flip {
    overflow: visible
}

.swiper-container-flip .swiper-slide {
    pointer-events: none;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    z-index: 1
}

.swiper-container-flip .swiper-slide .swiper-slide {
    pointer-events: none
}

.swiper-container-flip .swiper-slide-active,
.swiper-container-flip .swiper-slide-active .swiper-slide-active {
    pointer-events: auto
}

.swiper-container-flip .swiper-slide-shadow-top,
.swiper-container-flip .swiper-slide-shadow-bottom,
.swiper-container-flip .swiper-slide-shadow-left,
.swiper-container-flip .swiper-slide-shadow-right {
    z-index: 0;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden
}

/*! modern-normalize v1.0.0 | MIT License | https://github.com/sindresorhus/modern-normalize */
*,
*:before,
*:after {
    box-sizing: border-box
}

:root {
    -moz-tab-size: 4;
    -o-tab-size: 4;
    tab-size: 4
}

html {
    line-height: 1.15;
    -webkit-text-size-adjust: 100%
}

body {
    margin: 0
}

hr {
    height: 0;
    color: inherit
}

abbr[title] {
    -webkit-text-decoration: underline dotted;
    text-decoration: underline dotted
}

b,
strong {
    font-weight: bolder
}

code,
kbd,
samp,
pre {
    font-family: ui-monospace, SFMono-Regular, Consolas, 'Liberation Mono', Menlo, monospace;
    font-size: 1em
}

small {
    font-size: 80%
}

sub,
sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline
}

sub {
    bottom: -.25em
}

sup {
    top: -.5em
}

table {
    text-indent: 0;
    border-color: inherit
}

button,
input,
optgroup,
select,
textarea {
    font-family: inherit;
    font-size: 100%;
    line-height: 1.15;
    margin: 0
}

button,
select {
    text-transform: none
}

button,
[type=button],
[type=reset],
[type=submit] {
    -webkit-appearance: button
}

legend {
    padding: 0
}

progress {
    vertical-align: baseline
}

[type=search] {
    -webkit-appearance: textfield;
    outline-offset: -2px
}

summary {
    display: list-item
}

button {
    background-color: transparent;
    background-image: none
}

button:focus {
    outline: 1px dotted;
    outline: 5px auto -webkit-focus-ring-color
}

fieldset {
    margin: 0;
    padding: 0
}


html {
    font-family: proxima-nova, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    line-height: 1.5
}

*,
:before,
:after {
    box-sizing: border-box;
    border-width: 0;
    border-style: solid;
    border-color: currentColor
}

hr {
    border-top-width: 1px
}

img {
    border-style: solid
}

textarea {
    resize: vertical
}

input::-moz-placeholder,
textarea::-moz-placeholder {
    opacity: 1;
    color: #a1a1aa
}

input:-ms-input-placeholder,
textarea:-ms-input-placeholder {
    opacity: 1;
    color: #a1a1aa
}

input::placeholder,
textarea::placeholder {
    opacity: 1;
    color: #a1a1aa
}

button,
[role=button] {
    cursor: pointer
}

table {
    border-collapse: collapse
}

a {
    color: inherit;
    text-decoration: inherit
}

button,
input,
optgroup,
select,
textarea {
    padding: 0;
    line-height: inherit;
    color: inherit
}

pre,
code,
kbd,
samp {
    font-family: Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace
}

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
    display: block;
    vertical-align: middle
}

img,
video {
    max-width: 100%;
    height: auto
}

.container.containerCustom {
    width: 100%;
    margin-right: auto;
    margin-left: auto
}

@media (min-width:420px) {
    .container.containerCustom {
        max-width: 420px
    }
}

@media (min-width:640px) {
    .container.containerCustom {
        max-width: 640px
    }
}

@media (min-width:768px) {
    .container.containerCustom {
        max-width: 768px
    }
}

@media (min-width:1024px) {
    .container.containerCustom {
        max-width: 1024px
    }
}

@media (min-width:1280px) {
    .container.containerCustom {
        max-width: 1280px
    }
}

body,
html {
    font-size: 16px !important;
    line-height: 28px !important
}

.appearance-none {
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    appearance: none !important
}

.bg-transparent {
    background-color: transparent !important
}

.bg-black {
    --tw-bg-opacity: 1 !important;
    background-color: rgba(0, 0, 0, var(--tw-bg-opacity)) !important
}

.bg-white {
    --tw-bg-opacity: 1 !important;
    background-color: rgba(255, 255, 255, var(--tw-bg-opacity)) !important
}

.bg-error {
    --tw-bg-opacity: 1 !important;
    background-color: rgba(244, 67, 54, var(--tw-bg-opacity)) !important
}

.bg-success {
    --tw-bg-opacity: 1 !important;
    background-color: rgba(75, 181, 67, var(--tw-bg-opacity)) !important
}

.bg-gray-300 {
    --tw-bg-opacity: 1 !important;
    background-color: rgba(247, 247, 248, var(--tw-bg-opacity)) !important
}

.bg-blue-300 {
    --tw-bg-opacity: 1 !important;
    background-color: rgba(69, 104, 219, var(--tw-bg-opacity)) !important
}

.bg-blue {
    --tw-bg-opacity: 1 !important;
    background-color: rgba(80, 119, 227, var(--tw-bg-opacity)) !important
}

.bg-center {
    background-position: center !important
}

.bg-no-repeat {
    background-repeat: no-repeat !important
}

.bg-cover {
    background-size: cover !important
}

.border-white {
    --tw-border-opacity: 1 !important;
    border-color: rgba(255, 255, 255, var(--tw-border-opacity)) !important
}

.border-gray-300 {
    --tw-border-opacity: 1 !important;
    border-color: rgba(247, 247, 248, var(--tw-border-opacity)) !important
}

.border-gray-800 {
    --tw-border-opacity: 1 !important;
    border-color: rgba(225, 225, 225, var(--tw-border-opacity)) !important
}

.border-gray {
    --tw-border-opacity: 1 !important;
    border-color: rgba(182, 182, 182, var(--tw-border-opacity)) !important
}

.border-blue-200 {
    --tw-border-opacity: 1 !important;
    border-color: rgba(232, 238, 255, var(--tw-border-opacity)) !important
}

.border-blue {
    --tw-border-opacity: 1 !important;
    border-color: rgba(80, 119, 227, var(--tw-border-opacity)) !important
}

.focus\:border-blue-300:focus {
    --tw-border-opacity: 1 !important;
    border-color: rgba(69, 104, 219, var(--tw-border-opacity)) !important
}

.rounded {
    border-radius: .25rem !important
}

.rounded-md {
    border-radius: .375rem !important
}

.rounded-lg {
    border-radius: .5rem !important
}

.rounded-r-md {
    border-top-right-radius: .375rem !important;
    border-bottom-right-radius: .375rem !important
}

.rounded-b-md {
    border-bottom-right-radius: .375rem !important;
    border-bottom-left-radius: .375rem !important
}

.rounded-l-md {
    border-top-left-radius: .375rem !important;
    border-bottom-left-radius: .375rem !important
}

.border-solid {
    border-style: solid !important
}

.border-2 {
    border-width: 2px !important
}

.border {
    border-width: 1px !important
}

.border-r-2 {
    border-right-width: 2px !important
}

.border-b-2 {
    border-bottom-width: 2px !important
}

.border-l-3 {
    border-left-width: 3px !important
}

.border-t {
    border-top-width: 1px !important
}

.border-r {
    border-right-width: 1px !important
}

.cursor {
    cursor: default !important
}

.block {
    display: block !important
}

.inline-block {
    display: inline-block !important
}

.inline {
    display: inline !important
}

.flex {
    display: flex !important
}

.inline-flex {
    display: inline-flex !important
}

.table {
    display: table !important
}

.grid {
    display: grid !important
}

.contents {
    display: contents !important
}

.hidden {
    display: none !important
}

.flex-col {
    flex-direction: column !important
}

.flex-wrap {
    flex-wrap: wrap !important
}

.items-start {
    align-items: flex-start !important
}

.items-end {
    align-items: flex-end !important
}

.items-center {
    align-items: center !important
}

.content-center {
    align-content: center !important
}

.justify-start {
    justify-content: flex-start !important
}

.justify-end {
    justify-content: flex-end !important
}

.justify-center {
    justify-content: center !important
}

.justify-between {
    justify-content: space-between !important
}

.flex-1 {
    flex: 1 1 0% !important
}

.float-right {
    float: right !important
}

.float-left {
    float: left !important
}

.font-sans {
    font-family: proxima-nova, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji" !important
}

.font-light {
    font-weight: 300 !important
}

.font-medium {
    font-weight: 500 !important
}

.font-semibold {
    font-weight: 600 !important
}

.font-bold {
    font-weight: 700 !important
}

.font-extrabold {
    font-weight: 800 !important
}

.font-black {
    font-weight: 900 !important
}

.h-1 {
    height: .25rem !important
}

.h-5 {
    height: 1.25rem !important
}

.h-8 {
    height: 2rem !important
}

.h-16 {
    height: 4rem !important
}

.h-full {
    height: 100% !important
}

.text-xs {
    font-size: .75rem !important;
    line-height: 1rem !important
}

.text-sm {
    font-size: .875rem !important;
    line-height: 1.25rem !important
}

.text-base {
    font-size: 1rem !important;
    line-height: 1.5rem !important
}

.text-lg {
    font-size: 1.125rem !important;
    line-height: 1.75rem !important
}

.text-xl {
    font-size: 1.25rem !important;
    line-height: 1.75rem !important
}

.text-2xl {
    font-size: 1.5rem !important;
    line-height: 2rem !important
}

.text-3xl {
    font-size: 1.875rem !important;
    line-height: 2.25rem !important
}

.text-5xl {
    font-size: 3rem !important;
    line-height: 1 !important
}

.leading-3 {
    line-height: .95rem !important
}

.leading-4 {
    line-height: 1rem !important
}

.leading-5 {
    line-height: 1.25rem !important
}

.leading-7 {
    line-height: 1.75rem !important
}

.leading-none {
    line-height: 1 !important
}

.leading-snug {
    line-height: 1.375 !important
}

.leading-normal {
    line-height: 1.5 !important
}

.leading-loose {
    line-height: 2 !important
}

.list-none {
    list-style-type: none !important
}

.m-1 {
    margin: .25rem !important
}

.m-8 {
    margin: 2rem !important
}

.my-1 {
    margin-top: .25rem !important;
    margin-bottom: .25rem !important
}

.mx-2 {
    margin-left: .625rem !important;
    margin-right: .625rem !important
}

.my-3 {
    margin-top: .75rem !important;
    margin-bottom: .75rem !important
}

.mx-3 {
    margin-left: .75rem !important;
    margin-right: .75rem !important
}

.my-6 {
    margin-top: 1.5rem !important;
    margin-bottom: 1.5rem !important
}

.my-8 {
    margin-top: 2rem !important;
    margin-bottom: 2rem !important
}

.mx-auto {
    margin-left: auto !important;
    margin-right: auto !important
}

.mb-0 {
    margin-bottom: 0 !important
}

.ml-0 {
    margin-left: 0 !important
}

.mr-1 {
    margin-right: .25rem !important
}

.ml-1 {
    margin-left: .25rem !important
}

.mt-2 {
    margin-top: .625rem !important
}

.mr-2 {
    margin-right: .625rem !important
}

.mb-2 {
    margin-bottom: .625rem !important
}

.ml-2 {
    margin-left: .625rem !important
}

.mt-3 {
    margin-top: .75rem !important
}

.mb-3 {
    margin-bottom: .75rem !important
}

.ml-3 {
    margin-left: .75rem !important
}

.mt-4 {
    margin-top: 1rem !important
}

.mr-4 {
    margin-right: 1rem !important
}

.mb-4 {
    margin-bottom: 1rem !important
}

.ml-4 {
    margin-left: 1rem !important
}

.mb-6 {
    margin-bottom: 1.5rem !important
}

.mt-8 {
    margin-top: 2rem !important
}

.mb-8 {
    margin-bottom: 2rem !important
}

.mb-10 {
    margin-bottom: 2.5rem !important
}

.mb-12 {
    margin-bottom: 3rem !important
}

.ml-12 {
    margin-left: 3rem !important
}

.mt-16 {
    margin-top: 4rem !important
}

.mb-16 {
    margin-bottom: 4rem !important
}

.mr-20 {
    margin-right: 5rem !important
}

.mb-20 {
    margin-bottom: 5rem !important
}

.mt-auto {
    margin-top: auto !important
}

.ml-auto {
    margin-left: auto !important
}

.-mt-px {
    margin-top: -1px !important
}

.-ml-px {
    margin-left: -1px !important
}

.max-w-sm {
    max-width: 24rem !important
}

.max-w-xl {
    max-width: 36rem !important
}

.max-w-6xl {
    max-width: 72rem !important
}

.max-w-full {
    max-width: 100% !important
}

.min-h-screen {
    min-height: 100vh !important
}

.opacity-60 {
    opacity: .6 !important
}

.focus\:outline-none:focus {
    outline: 2px solid transparent !important;
    outline-offset: 2px !important
}

.overflow-hidden {
    overflow: hidden !important
}

.overflow-y-scroll {
    overflow-y: scroll !important
}

.p-0 {
    padding: 0 !important
}

.p-2 {
    padding: .625rem !important
}

.p-3 {
    padding: .75rem !important
}

.p-4 {
    padding: 1rem !important
}

.p-6 {
    padding: 1.5rem !important
}

.px-0 {
    padding-left: 0 !important;
    padding-right: 0 !important
}

.py-2 {
    padding-top: .625rem !important;
    padding-bottom: .625rem !important
}

.px-2 {
    padding-left: .625rem !important;
    padding-right: .625rem !important
}

.py-3 {
    padding-top: .75rem !important;
    padding-bottom: .75rem !important
}

.px-3 {
    padding-left: .75rem !important;
    padding-right: .75rem !important
}

.py-4 {
    padding-top: 1rem !important;
    padding-bottom: 1rem !important
}

.px-4 {
    padding-left: 1rem !important;
    padding-right: 1rem !important
}

.py-6 {
    padding-top: 1.5rem !important;
    padding-bottom: 1.5rem !important
}

.px-6 {
    padding-left: 1.5rem !important;
    padding-right: 1.5rem !important
}

.py-12 {
    padding-top: 3rem !important;
    padding-bottom: 3rem !important
}

.pt-2 {
    padding-top: .625rem !important
}

.pb-2 {
    padding-bottom: .625rem !important
}

.pl-2 {
    padding-left: .625rem !important
}

.pt-3 {
    padding-top: .75rem !important
}

.pl-3 {
    padding-left: .75rem !important
}

.pr-4 {
    padding-right: 1rem !important
}

.pb-4 {
    padding-bottom: 1rem !important
}

.pl-4 {
    padding-left: 1rem !important
}

.pt-6 {
    padding-top: 1.5rem !important
}

.pb-6 {
    padding-bottom: 1.5rem !important
}

.pt-8 {
    padding-top: 2rem !important
}

.pb-8 {
    padding-bottom: 2rem !important
}

.pr-10 {
    padding-right: 2.5rem !important
}

.pl-10 {
    padding-left: 2.5rem !important
}

.pb-12 {
    padding-bottom: 3rem !important
}

.pb-16 {
    padding-bottom: 4rem !important
}

.static {
    position: static !important
}

.fixed {
    position: fixed !important
}

.absolute {
    position: absolute !important
}

.relative {
    position: relative !important
}

.sticky {
    position: -webkit-sticky !important;
    position: sticky !important
}

.inset-0 {
    top: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    left: 0 !important
}

.top-0 {
    top: 0 !important
}

.right-0 {
    right: 0 !important
}

.bottom-0 {
    bottom: 0 !important
}

* {
    --tw-shadow: 0 0 #0000
}

.shadow-sm {
    --tw-shadow: 0 1px 2px 0 rgba(0, 0, 0, .05) !important;
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important
}

.shadow {
    --tw-shadow: 0 1px 3px 0 rgba(0, 0, 0, .1), 0 1px 2px 0 rgba(0, 0, 0, .06) !important;
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important
}

.hover\:shadow-md:hover {
    --tw-shadow: 0 4px 6px -1px rgba(0, 0, 0, .1), 0 2px 4px -1px rgba(0, 0, 0, .06) !important;
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important
}

* {
    --tw-ring-inset: var(--tw-empty, );
    --tw-ring-offset-width: 0px;
    --tw-ring-offset-color: #fff;
    --tw-ring-color: rgba(39, 59, 156, .5);
    --tw-ring-offset-shadow: 0 0 #0000;
    --tw-ring-shadow: 0 0 #0000
}

.ring {
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color) !important;
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color) !important;
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000) !important
}

.text-left {
    text-align: left !important
}

.text-center {
    text-align: center !important
}

.text-right {
    text-align: right !important
}

.text-black {
    --tw-text-opacity: 1 !important;
    color: rgba(0, 0, 0, var(--tw-text-opacity)) !important
}

.text-white {
    --tw-text-opacity: 1 !important;
    color: rgba(255, 255, 255, var(--tw-text-opacity)) !important
}

.text-error {
    --tw-text-opacity: 1 !important;
    color: rgba(244, 67, 54, var(--tw-text-opacity)) !important
}

.text-gray-300 {
    --tw-text-opacity: 1 !important;
    color: rgba(247, 247, 248, var(--tw-text-opacity)) !important
}

.text-gray-500 {
    --tw-text-opacity: 1 !important;
    color: rgba(48, 48, 48, var(--tw-text-opacity)) !important
}

.text-gray-700 {
    --tw-text-opacity: 1 !important;
    color: rgba(151, 151, 151, var(--tw-text-opacity)) !important
}

.text-gray {
    --tw-text-opacity: 1 !important;
    color: rgba(182, 182, 182, var(--tw-text-opacity)) !important
}

.text-blue {
    --tw-text-opacity: 1 !important;
    color: rgba(80, 119, 227, var(--tw-text-opacity)) !important
}

.hover\:text-black:hover {
    --tw-text-opacity: 1 !important;
    color: rgba(0, 0, 0, var(--tw-text-opacity)) !important
}

.hover\:text-gray-500:hover {
    --tw-text-opacity: 1 !important;
    color: rgba(48, 48, 48, var(--tw-text-opacity)) !important
}

.hover\:text-blue-300:hover {
    --tw-text-opacity: 1 !important;
    color: rgba(69, 104, 219, var(--tw-text-opacity)) !important
}

.hover\:text-blue:hover {
    --tw-text-opacity: 1 !important;
    color: rgba(80, 119, 227, var(--tw-text-opacity)) !important
}

.truncate {
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    white-space: nowrap !important
}

.italic {
    font-style: italic !important
}

.uppercase {
    text-transform: uppercase !important
}

.lowercase {
    text-transform: lowercase !important
}

.capitalize {
    text-transform: capitalize !important
}

.underline {
    text-decoration: underline !important
}

.no-underline {
    text-decoration: none !important
}

.hover\:no-underline:hover {
    text-decoration: none !important
}

.antialiased {
    -webkit-font-smoothing: antialiased !important;
    -moz-osx-font-smoothing: grayscale !important
}

.ordinal {
    --tw-ordinal: var(--tw-empty, ) !important;
    --tw-slashed-zero: var(--tw-empty, ) !important;
    --tw-numeric-figure: var(--tw-empty, ) !important;
    --tw-numeric-spacing: var(--tw-empty, ) !important;
    --tw-numeric-fraction: var(--tw-empty, ) !important;
    font-variant-numeric: var(--tw-ordinal) var(--tw-slashed-zero) var(--tw-numeric-figure) var(--tw-numeric-spacing) var(--tw-numeric-fraction) !important
}

.ordinal {
    --tw-ordinal: ordinal !important
}

.tracking-wide {
    letter-spacing: .025em !important
}

.tracking-wider {
    letter-spacing: .05em !important
}

.tracking-widest {
    letter-spacing: .1em !important
}

.visible {
    visibility: visible !important
}

.invisible {
    visibility: hidden !important
}

.break-all {
    word-break: break-all !important
}

.w-5 {
    width: 1.25rem !important
}

.w-8 {
    width: 2rem !important
}

.w-16 {
    width: 4rem !important
}

.w-auto {
    width: auto !important
}

.w-1\/2 {
    width: 50% !important
}

.w-1\/3 {
    width: 33.333333% !important
}

.w-2\/3 {
    width: 66.666667% !important
}

.w-8\/12 {
    width: 66.666667% !important
}

.w-full {
    width: 100% !important
}

.z-0 {
    z-index: 0 !important
}

.z-10 {
    z-index: 10 !important
}

.z-30 {
    z-index: 30 !important
}

.z-40 {
    z-index: 40 !important
}

.z-50 {
    z-index: 50 !important
}

.focus\:z-10:focus {
    z-index: 10 !important
}

.gap-8 {
    gap: 2rem !important
}

.grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr)) !important
}

.col-span-1 {
    grid-column: span 1/span 1 !important
}

.col-span-2 {
    grid-column: span 2/span 2 !important
}

.col-span-3 {
    grid-column: span 3/span 3 !important
}

.col-span-4 {
    grid-column: span 4/span 4 !important
}

.transform {
    --tw-translate-x: 0 !important;
    --tw-translate-y: 0 !important;
    --tw-rotate: 0 !important;
    --tw-skew-x: 0 !important;
    --tw-skew-y: 0 !important;
    --tw-scale-x: 1 !important;
    --tw-scale-y: 1 !important;
    transform: translateX(var(--tw-translate-x)) translateY(var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)) !important
}

.transition {
    transition-property: background-color, border-color, color, fill, stroke, opacity, box-shadow, transform !important;
    transition-timing-function: cubic-bezier(.4, 0, .2, 1) !important;
    transition-duration: .15s !important
}

.ease-in-out {
    transition-timing-function: cubic-bezier(.4, 0, .2, 1) !important
}

.duration-150 {
    transition-duration: .15s !important
}

@-webkit-keyframes spin {
    to {
        transform: rotate(360deg)
    }
}

@keyframes spin {
    to {
        transform: rotate(360deg)
    }
}

@-webkit-keyframes ping {

    75%,
    100% {
        transform: scale(2);
        opacity: 0
    }
}

@keyframes ping {

    75%,
    100% {
        transform: scale(2);
        opacity: 0
    }
}

@-webkit-keyframes pulse {
    50% {
        opacity: .5
    }
}

@keyframes pulse {
    50% {
        opacity: .5
    }
}

@-webkit-keyframes bounce {

    0%,
    100% {
        transform: translateY(-25%);
        -webkit-animation-timing-function: cubic-bezier(.8, 0, 1, 1);
        animation-timing-function: cubic-bezier(.8, 0, 1, 1)
    }

    50% {
        transform: none;
        -webkit-animation-timing-function: cubic-bezier(0, 0, .2, 1);
        animation-timing-function: cubic-bezier(0, 0, .2, 1)
    }
}

@keyframes bounce {

    0%,
    100% {
        transform: translateY(-25%);
        -webkit-animation-timing-function: cubic-bezier(.8, 0, 1, 1);
        animation-timing-function: cubic-bezier(.8, 0, 1, 1)
    }

    50% {
        transform: none;
        -webkit-animation-timing-function: cubic-bezier(0, 0, .2, 1);
        animation-timing-function: cubic-bezier(0, 0, .2, 1)
    }
}

.filter {
    --tw-blur: var(--tw-empty, ) !important;
    --tw-brightness: var(--tw-empty, ) !important;
    --tw-contrast: var(--tw-empty, ) !important;
    --tw-grayscale: var(--tw-empty, ) !important;
    --tw-hue-rotate: var(--tw-empty, ) !important;
    --tw-invert: var(--tw-empty, ) !important;
    --tw-saturate: var(--tw-empty, ) !important;
    --tw-sepia: var(--tw-empty, ) !important;
    --tw-drop-shadow: var(--tw-empty, ) !important;
    filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow) !important
}

@media (min-width:640px) {
    .sm\:flex {
        display: flex !important
    }

    .sm\:hidden {
        display: none !important
    }

    .sm\:items-center {
        align-items: center !important
    }

    .sm\:justify-start {
        justify-content: flex-start !important
    }

    .sm\:justify-between {
        justify-content: space-between !important
    }

    .sm\:flex-1 {
        flex: 1 1 0% !important
    }

    .sm\:mt-4 {
        margin-top: 1rem !important
    }

    .sm\:px-6 {
        padding-left: 1.5rem !important;
        padding-right: 1.5rem !important
    }

    .sm\:pt-0 {
        padding-top: 0 !important
    }
}

@media (min-width:768px) {
    .md\:flex {
        display: flex !important
    }

    .md\:hidden {
        display: none !important
    }

    .md\:flex-row {
        flex-direction: row !important
    }

    .md\:flex-wrap {
        flex-wrap: wrap !important
    }

    .md\:items-start {
        align-items: flex-start !important
    }

    .md\:justify-start {
        justify-content: flex-start !important
    }

    .md\:justify-end {
        justify-content: flex-end !important
    }

    .md\:justify-between {
        justify-content: space-between !important
    }

    .md\:text-sm {
        font-size: .875rem !important;
        line-height: 1.25rem !important
    }

    .md\:text-2xl {
        font-size: 1.5rem !important;
        line-height: 2rem !important
    }

    .md\:text-3xl {
        font-size: 1.875rem !important;
        line-height: 2.25rem !important
    }

    .md\:leading-5 {
        line-height: 1.25rem !important
    }

    .md\:mx-0 {
        margin-left: 0 !important;
        margin-right: 0 !important
    }

    .md\:my-6 {
        margin-top: 1.5rem !important;
        margin-bottom: 1.5rem !important
    }

    .md\:mx-auto {
        margin-left: auto !important;
        margin-right: auto !important
    }

    .md\:mt-0 {
        margin-top: 0 !important
    }

    .md\:ml-0 {
        margin-left: 0 !important
    }

    .md\:mb-2 {
        margin-bottom: .625rem !important
    }

    .md\:mt-3 {
        margin-top: .75rem !important
    }

    .md\:mt-6 {
        margin-top: 1.5rem !important
    }

    .md\:mb-6 {
        margin-bottom: 1.5rem !important
    }

    .md\:mr-10 {
        margin-right: 2.5rem !important
    }

    .md\:mb-16 {
        margin-bottom: 4rem !important
    }

    .md\:min-h-screen {
        min-height: 100vh !important
    }

    .md\:p-0 {
        padding: 0 !important
    }

    .md\:p-3 {
        padding: .75rem !important
    }

    .md\:px-1 {
        padding-left: .25rem !important;
        padding-right: .25rem !important
    }

    .md\:px-6 {
        padding-left: 1.5rem !important;
        padding-right: 1.5rem !important
    }

    .md\:px-8 {
        padding-left: 2rem !important;
        padding-right: 2rem !important
    }

    .md\:pb-0 {
        padding-bottom: 0 !important
    }

    .md\:pr-8 {
        padding-right: 2rem !important
    }

    .md\:pt-10 {
        padding-top: 2.5rem !important
    }

    .md\:text-left {
        text-align: left !important
    }

    .md\:w-16 {
        width: 4rem !important
    }

    .md\:w-1\/2 {
        width: 50% !important
    }

    .md\:w-1\/3 {
        width: 33.333333% !important
    }

    .md\:w-3\/10 {
        width: 30% !important
    }

    .md\:w-2\/12 {
        width: 16.666667% !important
    }

    .md\:w-10\/12 {
        width: 83.333333% !important
    }

    .md\:w-12\/25 {
        width: 48% !important
    }
}

@media (min-width:1024px) {
    .lg\:bg-transparent {
        background-color: transparent !important
    }

    .lg\:bg-auto {
        background-size: auto !important
    }

    .lg\:border-solid {
        border-style: solid !important
    }

    .lg\:border-l {
        border-left-width: 1px !important
    }

    .lg\:block {
        display: block !important
    }

    .lg\:flex {
        display: flex !important
    }

    .lg\:inline-flex {
        display: inline-flex !important
    }

    .lg\:grid {
        display: grid !important
    }

    .lg\:hidden {
        display: none !important
    }

    .lg\:flex-row {
        flex-direction: row !important
    }

    .lg\:flex-col {
        flex-direction: column !important
    }

    .lg\:flex-nowrap {
        flex-wrap: nowrap !important
    }

    .lg\:items-end {
        align-items: flex-end !important
    }

    .lg\:items-center {
        align-items: center !important
    }

    .lg\:content-end {
        align-content: flex-end !important
    }

    .lg\:justify-start {
        justify-content: flex-start !important
    }

    .lg\:justify-end {
        justify-content: flex-end !important
    }

    .lg\:justify-between {
        justify-content: space-between !important
    }

    .lg\:h-16 {
        height: 4rem !important
    }

    .lg\:text-3xl {
        font-size: 1.875rem !important;
        line-height: 2.25rem !important
    }

    .lg\:text-4xl {
        font-size: 2.25rem !important;
        line-height: 2.5rem !important
    }

    .lg\:mx-1 {
        margin-left: .25rem !important;
        margin-right: .25rem !important
    }

    .lg\:mt-0 {
        margin-top: 0 !important
    }

    .lg\:mb-0 {
        margin-bottom: 0 !important
    }

    .lg\:mt-1 {
        margin-top: .25rem !important
    }

    .lg\:mt-6 {
        margin-top: 1.5rem !important
    }

    .lg\:mb-6 {
        margin-bottom: 1.5rem !important
    }

    .lg\:ml-6 {
        margin-left: 1.5rem !important
    }

    .lg\:mb-8 {
        margin-bottom: 2rem !important
    }

    .lg\:ml-8 {
        margin-left: 2rem !important
    }

    .lg\:mb-12 {
        margin-bottom: 3rem !important
    }

    .lg\:ml-20 {
        margin-left: 5rem !important
    }

    .lg\:mb-32 {
        margin-bottom: 8rem !important
    }

    .lg\:mt-auto {
        margin-top: auto !important
    }

    .lg\:mr-auto {
        margin-right: auto !important
    }

    .lg\:ml-auto {
        margin-left: auto !important
    }

    .lg\:-mt-12 {
        margin-top: -3rem !important
    }

    .lg\:overflow-y-visible {
        overflow-y: visible !important
    }

    .lg\:p-0 {
        padding: 0 !important
    }

    .lg\:px-0 {
        padding-left: 0 !important;
        padding-right: 0 !important
    }

    .lg\:px-3 {
        padding-left: .75rem !important;
        padding-right: .75rem !important
    }

    .lg\:py-8 {
        padding-top: 2rem !important;
        padding-bottom: 2rem !important
    }

    .lg\:px-8 {
        padding-left: 2rem !important;
        padding-right: 2rem !important
    }

    .lg\:py-10 {
        padding-top: 2.5rem !important;
        padding-bottom: 2.5rem !important
    }

    .lg\:py-12 {
        padding-top: 3rem !important;
        padding-bottom: 3rem !important
    }

    .lg\:pb-0 {
        padding-bottom: 0 !important
    }

    .lg\:pl-0 {
        padding-left: 0 !important
    }

    .lg\:pb-1 {
        padding-bottom: .25rem !important
    }

    .lg\:pt-5 {
        padding-top: 1.25rem !important
    }

    .lg\:pt-6 {
        padding-top: 1.5rem !important
    }

    .lg\:pr-6 {
        padding-right: 1.5rem !important
    }

    .lg\:pb-8 {
        padding-bottom: 2rem !important
    }

    .lg\:pl-8 {
        padding-left: 2rem !important
    }

    .lg\:pt-10 {
        padding-top: 2.5rem !important
    }

    .lg\:pt-12 {
        padding-top: 3rem !important
    }

    .lg\:pt-16 {
        padding-top: 4rem !important
    }

    .lg\:pb-16 {
        padding-bottom: 4rem !important
    }

    .lg\:static {
        position: static !important
    }

    .lg\:relative {
        position: relative !important
    }

    .lg\:right-0 {
        right: 0 !important
    }

    .lg\:top-32 {
        top: 8rem !important
    }

    .lg\:bottom-auto {
        bottom: auto !important
    }

    .lg\:text-center {
        text-align: center !important
    }

    .lg\:text-black {
        --tw-text-opacity: 1 !important;
        color: rgba(0, 0, 0, var(--tw-text-opacity)) !important
    }

    .lg\:text-gray-500 {
        --tw-text-opacity: 1 !important;
        color: rgba(48, 48, 48, var(--tw-text-opacity)) !important
    }

    .lg\:w-2\/3 {
        width: 66.666667% !important
    }

    .lg\:w-1\/4 {
        width: 25% !important
    }

    .lg\:w-3\/4 {
        width: 75% !important
    }

    .lg\:w-3\/10 {
        width: 30% !important
    }

    .lg\:w-2\/5 {
        width: 40% !important
    }

    .lg\:w-3\/5 {
        width: 60% !important
    }

    .lg\:w-1\/6 {
        width: 16.666667% !important
    }

    .lg\:w-7\/12 {
        width: 58.333333% !important
    }

    .lg\:w-8\/12 {
        width: 66.666667% !important
    }

    .lg\:w-10\/12 {
        width: 83.333333% !important
    }

    .lg\:w-11\/50 {
        width: 22% !important
    }

    .lg\:w-full {
        width: 100% !important
    }

    .lg\:z-30 {
        z-index: 30 !important
    }

    .lg\:grid-cols-1 {
        grid-template-columns: repeat(1, minmax(0, 1fr)) !important
    }

    .lg\:grid-cols-2 {
        grid-template-columns: repeat(2, minmax(0, 1fr)) !important
    }

    .lg\:grid-cols-3 {
        grid-template-columns: repeat(3, minmax(0, 1fr)) !important
    }

    .lg\:grid-cols-5 {
        grid-template-columns: repeat(5, minmax(0, 1fr)) !important
    }

    .lg\:grid-cols-8 {
        grid-template-columns: repeat(8, minmax(0, 1fr)) !important
    }

    .lg\:grid-cols-12 {
        grid-template-columns: repeat(12, minmax(0, 1fr)) !important
    }

    .lg\:col-span-1 {
        grid-column: span 1/span 1 !important
    }

    .lg\:col-span-2 {
        grid-column: span 2/span 2 !important
    }

    .lg\:col-span-3 {
        grid-column: span 3/span 3 !important
    }

    .lg\:col-span-4 {
        grid-column: span 4/span 4 !important
    }

    .lg\:col-span-5 {
        grid-column: span 5/span 5 !important
    }
}

@media (min-width:1280px) {
    .xl\:text-md {
        font-size: .95rem !important;
        line-height: 1.5rem !important
    }

    .xl\:leading-6 {
        line-height: 1.5rem !important
    }

    .xl\:px-0 {
        padding-left: 0 !important;
        padding-right: 0 !important
    }

    .xl\:px-2 {
        padding-left: .625rem !important;
        padding-right: .625rem !important
    }

    .xl\:pt-0 {
        padding-top: 0 !important
    }

    .xl\:pb-2 {
        padding-bottom: .625rem !important
    }

    .xl\:pl-18 {
        padding-left: 4.5rem !important
    }
}

@font-face {
    font-display: swap;
    font-family: 'Material Icons';
    font-style: normal;
    font-weight: 400;
    src: url(https://fonts.gstatic.com/s/materialicons/v140/flUhRq6tzZclQEJ-Vdg-IuiaDsNZ.ttf) format('truetype')
}

.material-icons {
    font-family: 'Material Icons';
    font-weight: 400;
    font-style: normal;
    font-size: 24px;
    line-height: 1;
    letter-spacing: normal;
    text-transform: none;
    display: inline-block;
    white-space: nowrap;
    word-wrap: normal;
    direction: ltr
}

.fc-not-allowed,
.fc-not-allowed .fc-event {
    cursor: not-allowed
}

.fc-unselectable {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-touch-callout: none;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0)
}

.fc {
    display: flex;
    flex-direction: column;
    font-size: 1em
}

.fc,
.fc *,
.fc *:before,
.fc *:after {
    box-sizing: border-box
}

.fc table {
    border-collapse: collapse;
    border-spacing: 0;
    font-size: 1em
}

.fc th {
    text-align: center
}

.fc th,
.fc td {
    vertical-align: top;
    padding: 0
}

.fc a[data-navlink] {
    cursor: pointer
}

.fc a[data-navlink]:hover {
    text-decoration: underline
}

.fc-direction-ltr {
    direction: ltr;
    text-align: left
}

.fc-direction-rtl {
    direction: rtl;
    text-align: right
}

.fc-theme-standard td,
.fc-theme-standard th {
    border: 1px solid #ddd;
    border: 1px solid var(--fc-border-color, #ddd)
}

.fc-liquid-hack td,
.fc-liquid-hack th {
    position: relative
}

@font-face {
    font-display: swap;
    font-family: 'fcicons';
    src: url("data:application/x-font-ttf;charset=utf-8;base64,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") format('truetype');
    font-weight: 400;
    font-style: normal
}

.fc-icon {
    display: inline-block;
    width: 1em;
    height: 1em;
    text-align: center;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    font-family: 'fcicons' !important;
    speak: none;
    font-style: normal;
    font-weight: 400;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale
}

.fc-icon-chevron-left:before {
    content: "\e900"
}

.fc-icon-chevron-right:before {
    content: "\e901"
}

.fc-icon-chevrons-left:before {
    content: "\e902"
}

.fc-icon-chevrons-right:before {
    content: "\e903"
}

.fc-icon-minus-square:before {
    content: "\e904"
}

.fc-icon-plus-square:before {
    content: "\e905"
}

.fc-icon-x:before {
    content: "\e906"
}

.fc .fc-button {
    border-radius: 0;
    overflow: visible;
    text-transform: none;
    margin: 0;
    font-family: inherit;
    font-size: inherit;
    line-height: inherit
}

.fc .fc-button:focus {
    outline: 1px dotted;
    outline: 5px auto -webkit-focus-ring-color
}

.fc .fc-button {
    -webkit-appearance: button
}

.fc .fc-button:not(:disabled) {
    cursor: pointer
}

.fc .fc-button::-moz-focus-inner {
    padding: 0;
    border-style: none
}

.fc .fc-button {
    display: inline-block;
    font-weight: 400;
    text-align: center;
    vertical-align: middle;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    background-color: transparent;
    border: 1px solid transparent;
    padding: .4em .65em;
    font-size: 1em;
    line-height: 1.5;
    border-radius: .25em
}

.fc .fc-button:hover {
    text-decoration: none
}

.fc .fc-button:focus {
    outline: 0;
    box-shadow: 0 0 0 .2rem rgba(44, 62, 80, .25)
}

.fc .fc-button:disabled {
    opacity: .65
}

.fc .fc-button-primary {
    color: #fff;
    color: var(--fc-button-text-color, #fff);
    background-color: #2c3e50;
    background-color: var(--fc-button-bg-color, #2c3e50);
    border-color: #2c3e50;
    border-color: var(--fc-button-border-color, #2c3e50)
}

.fc .fc-button-primary:hover {
    color: #fff;
    color: var(--fc-button-text-color, #fff);
    background-color: #1e2b37;
    background-color: var(--fc-button-hover-bg-color, #1e2b37);
    border-color: #1a252f;
    border-color: var(--fc-button-hover-border-color, #1a252f)
}

.fc .fc-button-primary:disabled {
    color: #fff;
    color: var(--fc-button-text-color, #fff);
    background-color: #2c3e50;
    background-color: var(--fc-button-bg-color, #2c3e50);
    border-color: #2c3e50;
    border-color: var(--fc-button-border-color, #2c3e50)
}

.fc .fc-button-primary:focus {
    box-shadow: 0 0 0 .2rem rgba(76, 91, 106, .5)
}

.fc .fc-button-primary:not(:disabled):active,
.fc .fc-button-primary:not(:disabled).fc-button-active {
    color: #fff;
    color: var(--fc-button-text-color, #fff);
    background-color: #1a252f;
    background-color: var(--fc-button-active-bg-color, #1a252f);
    border-color: #151e27;
    border-color: var(--fc-button-active-border-color, #151e27)
}

.fc .fc-button-primary:not(:disabled):active:focus,
.fc .fc-button-primary:not(:disabled).fc-button-active:focus {
    box-shadow: 0 0 0 .2rem rgba(76, 91, 106, .5)
}

.fc .fc-button .fc-icon {
    vertical-align: middle;
    font-size: 1.5em
}

.fc .fc-button-group {
    position: relative;
    display: inline-flex;
    vertical-align: middle
}

.fc .fc-button-group>.fc-button {
    position: relative;
    flex: 1 1 auto
}

.fc .fc-button-group>.fc-button:hover {
    z-index: 1
}

.fc .fc-button-group>.fc-button:focus,
.fc .fc-button-group>.fc-button:active,
.fc .fc-button-group>.fc-button.fc-button-active {
    z-index: 1
}

.fc-direction-ltr .fc-button-group>.fc-button:not(:first-child) {
    margin-left: -1px;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0
}

.fc-direction-ltr .fc-button-group>.fc-button:not(:last-child) {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0
}

.fc-direction-rtl .fc-button-group>.fc-button:not(:first-child) {
    margin-right: -1px;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0
}

.fc-direction-rtl .fc-button-group>.fc-button:not(:last-child) {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0
}

.fc .fc-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center
}

.fc .fc-toolbar.fc-header-toolbar {
    margin-bottom: 1.5em
}

.fc .fc-toolbar.fc-footer-toolbar {
    margin-top: 1.5em
}

.fc .fc-toolbar-title {
    font-size: 1.75em;
    margin: 0
}

.fc-direction-ltr .fc-toolbar>*>:not(:first-child) {
    margin-left: .75em
}

.fc-direction-rtl .fc-toolbar>*>:not(:first-child) {
    margin-right: .75em
}

.fc-direction-rtl .fc-toolbar-ltr {
    flex-direction: row-reverse
}

.fc .fc-scroller {
    -webkit-overflow-scrolling: touch;
    position: relative
}

.fc .fc-scroller-liquid {
    height: 100%
}

.fc .fc-scroller-liquid-absolute {
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
    bottom: 0
}

.fc .fc-scroller-harness {
    position: relative;
    overflow: hidden;
    direction: ltr
}

.fc .fc-scroller-harness-liquid {
    height: 100%
}

.fc-direction-rtl .fc-scroller-harness>.fc-scroller {
    direction: rtl
}

.fc-theme-standard .fc-scrollgrid {
    border: 1px solid #ddd;
    border: 1px solid var(--fc-border-color, #ddd)
}

.fc .fc-scrollgrid,
.fc .fc-scrollgrid table {
    width: 100%;
    table-layout: fixed
}

.fc .fc-scrollgrid table {
    border-top-style: hidden;
    border-left-style: hidden;
    border-right-style: hidden
}

.fc .fc-scrollgrid {
    border-collapse: separate;
    border-right-width: 0;
    border-bottom-width: 0
}

.fc .fc-scrollgrid-liquid {
    height: 100%
}

.fc .fc-scrollgrid-section {
    height: 1px
}

.fc .fc-scrollgrid-section>td {
    height: 1px
}

.fc .fc-scrollgrid-section table {
    height: 1px
}

.fc .fc-scrollgrid-section-liquid {
    height: auto
}

.fc .fc-scrollgrid-section-liquid>td {
    height: 100%
}

.fc .fc-scrollgrid-section>* {
    border-top-width: 0;
    border-left-width: 0
}

.fc .fc-scrollgrid-section-header>*,
.fc .fc-scrollgrid-section-footer>* {
    border-bottom-width: 0
}

.fc .fc-scrollgrid-section-body table,
.fc .fc-scrollgrid-section-footer table {
    border-bottom-style: hidden
}

.fc .fc-scrollgrid-section-sticky>* {
    background: #fff;
    background: var(--fc-page-bg-color, #fff);
    position: -webkit-sticky;
    position: sticky;
    z-index: 3
}

.fc .fc-scrollgrid-section-header.fc-scrollgrid-section-sticky>* {
    top: 0
}

.fc .fc-scrollgrid-section-footer.fc-scrollgrid-section-sticky>* {
    bottom: 0
}

.fc .fc-scrollgrid-sticky-shim {
    height: 1px;
    margin-bottom: -1px
}

.fc-sticky {
    position: -webkit-sticky;
    position: sticky
}

.fc .fc-view-harness {
    flex-grow: 1;
    position: relative
}

.fc .fc-view-harness-active>.fc-view {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0
}

.fc .fc-col-header-cell-cushion {
    display: inline-block;
    padding: 2px 4px
}

.fc .fc-bg-event,
.fc .fc-non-business,
.fc .fc-highlight {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0
}

.fc .fc-non-business {
    background: rgba(215, 215, 215, .3);
    background: var(--fc-non-business-color, rgba(215, 215, 215, .3))
}

.fc .fc-bg-event {
    background: #8fdf82;
    background: var(--fc-bg-event-color, #8fdf82);
    opacity: .3;
    opacity: var(--fc-bg-event-opacity, .3)
}

.fc .fc-bg-event .fc-event-title {
    margin: .5em;
    font-size: .85em;
    font-size: var(--fc-small-font-size, .85em);
    font-style: italic
}

.fc .fc-highlight {
    background: rgba(188, 232, 241, .3);
    background: var(--fc-highlight-color, rgba(188, 232, 241, .3))
}

.fc .fc-cell-shaded,
.fc .fc-day-disabled {
    background: rgba(208, 208, 208, .3);
    background: var(--fc-neutral-bg-color, rgba(208, 208, 208, .3))
}

a.fc-event,
a.fc-event:hover {
    text-decoration: none
}

.fc-event[href],
.fc-event.fc-event-draggable {
    cursor: pointer
}

.fc-event .fc-event-main {
    position: relative;
    z-index: 2
}

.fc-event-dragging:not(.fc-event-selected) {
    opacity: .75
}

.fc-event-dragging.fc-event-selected {
    box-shadow: 0 2px 7px rgba(0, 0, 0, .3)
}

.fc-event .fc-event-resizer {
    display: none;
    position: absolute;
    z-index: 4
}

.fc-event:hover .fc-event-resizer,
.fc-event-selected .fc-event-resizer {
    display: block
}

.fc-event-selected .fc-event-resizer {
    border-radius: 4px;
    border-radius: calc(var(--fc-event-resizer-dot-total-width, 8px)/2);
    border-width: 1px;
    border-width: var(--fc-event-resizer-dot-border-width, 1px);
    width: 8px;
    width: var(--fc-event-resizer-dot-total-width, 8px);
    height: 8px;
    height: var(--fc-event-resizer-dot-total-width, 8px);
    border-style: solid;
    border-color: inherit;
    background: #fff;
    background: var(--fc-page-bg-color, #fff)
}

.fc-event-selected .fc-event-resizer:before {
    content: '';
    position: absolute;
    top: -20px;
    left: -20px;
    right: -20px;
    bottom: -20px
}

.fc-event-selected {
    box-shadow: 0 2px 5px rgba(0, 0, 0, .2)
}

.fc-event-selected:before {
    content: "";
    position: absolute;
    z-index: 3;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0
}

.fc-event-selected:after {
    content: "";
    background: rgba(0, 0, 0, .25);
    background: var(--fc-event-selected-overlay-color, rgba(0, 0, 0, .25));
    position: absolute;
    z-index: 1;
    top: -1px;
    left: -1px;
    right: -1px;
    bottom: -1px
}

.fc-h-event {
    display: block;
    border: 1px solid #3788d8;
    border: 1px solid var(--fc-event-border-color, #3788d8);
    background-color: #3788d8;
    background-color: var(--fc-event-bg-color, #3788d8)
}

.fc-h-event .fc-event-main {
    color: #fff;
    color: var(--fc-event-text-color, #fff)
}

.fc-h-event .fc-event-main-frame {
    display: flex
}

.fc-h-event .fc-event-time {
    max-width: 100%;
    overflow: hidden
}

.fc-h-event .fc-event-title-container {
    flex-grow: 1;
    flex-shrink: 1;
    min-width: 0
}

.fc-h-event .fc-event-title {
    display: inline-block;
    vertical-align: top;
    left: 0;
    right: 0;
    max-width: 100%;
    overflow: hidden
}

.fc-h-event.fc-event-selected:before {
    top: -10px;
    bottom: -10px
}

.fc-direction-ltr .fc-daygrid-block-event:not(.fc-event-start),
.fc-direction-rtl .fc-daygrid-block-event:not(.fc-event-end) {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    border-left-width: 0
}

.fc-direction-ltr .fc-daygrid-block-event:not(.fc-event-end),
.fc-direction-rtl .fc-daygrid-block-event:not(.fc-event-start) {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-right-width: 0
}

.fc-h-event:not(.fc-event-selected) .fc-event-resizer {
    top: 0;
    bottom: 0;
    width: 8px;
    width: var(--fc-event-resizer-thickness, 8px)
}

.fc-direction-ltr .fc-h-event:not(.fc-event-selected) .fc-event-resizer-start,
.fc-direction-rtl .fc-h-event:not(.fc-event-selected) .fc-event-resizer-end {
    cursor: w-resize;
    left: -4px;
    left: calc(var(--fc-event-resizer-thickness, 8px)/-2)
}

.fc-direction-ltr .fc-h-event:not(.fc-event-selected) .fc-event-resizer-end,
.fc-direction-rtl .fc-h-event:not(.fc-event-selected) .fc-event-resizer-start {
    cursor: e-resize;
    right: -4px;
    right: calc(var(--fc-event-resizer-thickness, 8px)/-2)
}

.fc-h-event.fc-event-selected .fc-event-resizer {
    top: 50%;
    margin-top: -4px;
    margin-top: calc(var(--fc-event-resizer-dot-total-width, 8px)/-2)
}

.fc-direction-ltr .fc-h-event.fc-event-selected .fc-event-resizer-start,
.fc-direction-rtl .fc-h-event.fc-event-selected .fc-event-resizer-end {
    left: -4px;
    left: calc(var(--fc-event-resizer-dot-total-width, 8px)/-2)
}

.fc-direction-ltr .fc-h-event.fc-event-selected .fc-event-resizer-end,
.fc-direction-rtl .fc-h-event.fc-event-selected .fc-event-resizer-start {
    right: -4px;
    right: calc(var(--fc-event-resizer-dot-total-width, 8px)/-2)
}

.fc .fc-popover {
    position: absolute;
    z-index: 9999;
    box-shadow: 0 2px 6px rgba(0, 0, 0, .15)
}

.fc .fc-popover-header {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: 3px 4px
}

.fc .fc-popover-title {
    margin: 0 2px
}

.fc .fc-popover-close {
    cursor: pointer;
    opacity: .65;
    font-size: 1.1em
}

.fc-theme-standard .fc-popover {
    border: 1px solid #ddd;
    border: 1px solid var(--fc-border-color, #ddd);
    background: #fff;
    background: var(--fc-page-bg-color, #fff)
}

.fc-theme-standard .fc-popover-header {
    background: rgba(208, 208, 208, .3);
    background: var(--fc-neutral-bg-color, rgba(208, 208, 208, .3))
}

:root {
    --fc-daygrid-event-dot-width: 8px
}

.fc-daygrid-day-frame:before,
.fc-daygrid-day-events:before,
.fc-daygrid-event-harness:before {
    content: "";
    clear: both;
    display: table
}

.fc-daygrid-day-frame:after,
.fc-daygrid-day-events:after,
.fc-daygrid-event-harness:after {
    content: "";
    clear: both;
    display: table
}

.fc .fc-daygrid-body {
    position: relative;
    z-index: 1
}

.fc .fc-daygrid-day.fc-day-today {
    background-color: rgba(255, 220, 40, .15);
    background-color: var(--fc-today-bg-color, rgba(255, 220, 40, .15))
}

.fc .fc-daygrid-day-frame {
    position: relative;
    min-height: 100%
}

.fc .fc-daygrid-day-top {
    display: flex;
    flex-direction: row-reverse
}

.fc .fc-day-other .fc-daygrid-day-top {
    opacity: .3
}

.fc .fc-daygrid-day-number {
    position: relative;
    z-index: 4;
    padding: 4px
}

.fc .fc-daygrid-day-events {
    margin-top: 1px
}

.fc .fc-daygrid-body-balanced .fc-daygrid-day-events {
    position: absolute;
    left: 0;
    right: 0
}

.fc .fc-daygrid-body-unbalanced .fc-daygrid-day-events {
    position: relative;
    min-height: 2em
}

.fc .fc-daygrid-body-natural .fc-daygrid-day-events {
    margin-bottom: 1em
}

.fc .fc-daygrid-event-harness {
    position: relative
}

.fc .fc-daygrid-event-harness-abs {
    position: absolute;
    top: 0;
    left: 0;
    right: 0
}

.fc .fc-daygrid-bg-harness {
    position: absolute;
    top: 0;
    bottom: 0
}

.fc .fc-daygrid-day-bg .fc-non-business {
    z-index: 1
}

.fc .fc-daygrid-day-bg .fc-bg-event {
    z-index: 2
}

.fc .fc-daygrid-day-bg .fc-highlight {
    z-index: 3
}

.fc .fc-daygrid-event {
    z-index: 6;
    margin-top: 1px
}

.fc .fc-daygrid-event.fc-event-mirror {
    z-index: 7
}

.fc .fc-daygrid-day-bottom {
    font-size: .85em;
    padding: 2px 3px 0
}

.fc .fc-daygrid-day-bottom:before {
    content: "";
    clear: both;
    display: table
}

.fc .fc-daygrid-more-link {
    position: relative;
    z-index: 4;
    cursor: pointer
}

.fc .fc-daygrid-week-number {
    position: absolute;
    z-index: 5;
    top: 0;
    padding: 2px;
    min-width: 1.5em;
    text-align: center;
    background-color: rgba(208, 208, 208, .3);
    background-color: var(--fc-neutral-bg-color, rgba(208, 208, 208, .3));
    color: gray;
    color: var(--fc-neutral-text-color, gray)
}

.fc .fc-more-popover .fc-popover-body {
    min-width: 220px;
    padding: 10px
}

.fc-direction-ltr .fc-daygrid-event.fc-event-start,
.fc-direction-rtl .fc-daygrid-event.fc-event-end {
    margin-left: 2px
}

.fc-direction-ltr .fc-daygrid-event.fc-event-end,
.fc-direction-rtl .fc-daygrid-event.fc-event-start {
    margin-right: 2px
}

.fc-direction-ltr .fc-daygrid-week-number {
    left: 0;
    border-radius: 0 0 3px
}

.fc-direction-rtl .fc-daygrid-week-number {
    right: 0;
    border-radius: 0 0 0 3px
}

.fc-liquid-hack .fc-daygrid-day-frame {
    position: static
}

.fc-daygrid-event {
    position: relative;
    white-space: nowrap;
    border-radius: 3px;
    font-size: .85em;
    font-size: var(--fc-small-font-size, .85em)
}

.fc-daygrid-block-event .fc-event-time {
    font-weight: 700
}

.fc-daygrid-block-event .fc-event-time,
.fc-daygrid-block-event .fc-event-title {
    padding: 1px
}

.fc-daygrid-dot-event {
    display: flex;
    align-items: center;
    padding: 2px 0
}

.fc-daygrid-dot-event .fc-event-title {
    flex-grow: 1;
    flex-shrink: 1;
    min-width: 0;
    overflow: hidden;
    font-weight: 700
}

.fc-daygrid-dot-event:hover,
.fc-daygrid-dot-event.fc-event-mirror {
    background: rgba(0, 0, 0, .1)
}

.fc-daygrid-dot-event.fc-event-selected:before {
    top: -10px;
    bottom: -10px
}

.fc-daygrid-event-dot {
    margin: 0 4px;
    box-sizing: content-box;
    width: 0;
    height: 0;
    border: 4px solid #3788d8;
    border: calc(var(--fc-daygrid-event-dot-width, 8px)/2) solid var(--fc-event-border-color, #3788d8);
    border-radius: 4px;
    border-radius: calc(var(--fc-daygrid-event-dot-width, 8px)/2)
}

.fc-direction-ltr .fc-daygrid-event .fc-event-time {
    margin-right: 3px
}

.fc-direction-rtl .fc-daygrid-event .fc-event-time {
    margin-left: 3px
}

.fc-v-event {
    display: block;
    border: 1px solid #3788d8;
    border: 1px solid var(--fc-event-border-color, #3788d8);
    background-color: #3788d8;
    background-color: var(--fc-event-bg-color, #3788d8)
}

.fc-v-event .fc-event-main {
    color: #fff;
    color: var(--fc-event-text-color, #fff);
    height: 100%
}

.fc-v-event .fc-event-main-frame {
    height: 100%;
    display: flex;
    flex-direction: column
}

.fc-v-event .fc-event-time {
    flex-grow: 0;
    flex-shrink: 0;
    max-height: 100%;
    overflow: hidden
}

.fc-v-event .fc-event-title-container {
    flex-grow: 1;
    flex-shrink: 1;
    min-height: 0
}

.fc-v-event .fc-event-title {
    top: 0;
    bottom: 0;
    max-height: 100%;
    overflow: hidden
}

.fc-v-event:not(.fc-event-start) {
    border-top-width: 0;
    border-top-left-radius: 0;
    border-top-right-radius: 0
}

.fc-v-event:not(.fc-event-end) {
    border-bottom-width: 0;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0
}

.fc-v-event.fc-event-selected:before {
    left: -10px;
    right: -10px
}

.fc-v-event .fc-event-resizer-start {
    cursor: n-resize
}

.fc-v-event .fc-event-resizer-end {
    cursor: s-resize
}

.fc-v-event:not(.fc-event-selected) .fc-event-resizer {
    height: 8px;
    height: var(--fc-event-resizer-thickness, 8px);
    left: 0;
    right: 0
}

.fc-v-event:not(.fc-event-selected) .fc-event-resizer-start {
    top: -4px;
    top: calc(var(--fc-event-resizer-thickness, 8px)/-2)
}

.fc-v-event:not(.fc-event-selected) .fc-event-resizer-end {
    bottom: -4px;
    bottom: calc(var(--fc-event-resizer-thickness, 8px)/-2)
}

.fc-v-event.fc-event-selected .fc-event-resizer {
    left: 50%;
    margin-left: -4px;
    margin-left: calc(var(--fc-event-resizer-dot-total-width, 8px)/-2)
}

.fc-v-event.fc-event-selected .fc-event-resizer-start {
    top: -4px;
    top: calc(var(--fc-event-resizer-dot-total-width, 8px)/-2)
}

.fc-v-event.fc-event-selected .fc-event-resizer-end {
    bottom: -4px;
    bottom: calc(var(--fc-event-resizer-dot-total-width, 8px)/-2)
}

.fc .fc-timegrid .fc-daygrid-body {
    z-index: 2
}

.fc .fc-timegrid-divider {
    padding: 0 0 2px
}

.fc .fc-timegrid-body {
    position: relative;
    z-index: 1;
    min-height: 100%
}

.fc .fc-timegrid-axis-chunk {
    position: relative
}

.fc .fc-timegrid-axis-chunk>table {
    position: relative;
    z-index: 1
}

.fc .fc-timegrid-slots {
    position: relative;
    z-index: 1
}

.fc .fc-timegrid-slot {
    height: 1.5em;
    border-bottom: 0
}

.fc .fc-timegrid-slot:empty:before {
    content: '\00a0'
}

.fc .fc-timegrid-slot-minor {
    border-top-style: dotted
}

.fc .fc-timegrid-slot-label-cushion {
    display: inline-block;
    white-space: nowrap
}

.fc .fc-timegrid-slot-label {
    vertical-align: middle
}

.fc .fc-timegrid-axis-cushion,
.fc .fc-timegrid-slot-label-cushion {
    padding: 0 4px
}

.fc .fc-timegrid-axis-frame-liquid {
    height: 100%
}

.fc .fc-timegrid-axis-frame {
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: flex-end
}

.fc .fc-timegrid-axis-cushion {
    max-width: 60px;
    flex-shrink: 0
}

.fc-direction-ltr .fc-timegrid-slot-label-frame {
    text-align: right
}

.fc-direction-rtl .fc-timegrid-slot-label-frame {
    text-align: left
}

.fc-liquid-hack .fc-timegrid-axis-frame-liquid {
    height: auto;
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0
}

.fc .fc-timegrid-col.fc-day-today {
    background-color: rgba(255, 220, 40, .15);
    background-color: var(--fc-today-bg-color, rgba(255, 220, 40, .15))
}

.fc .fc-timegrid-col-frame {
    min-height: 100%;
    position: relative
}

.fc-liquid-hack .fc-timegrid-col-frame {
    height: auto;
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0
}

.fc-media-screen .fc-timegrid-cols {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0
}

.fc-media-screen .fc-timegrid-cols>table {
    height: 100%
}

.fc-media-screen .fc-timegrid-col-bg,
.fc-media-screen .fc-timegrid-col-events,
.fc-media-screen .fc-timegrid-now-indicator-container {
    position: absolute;
    top: 0;
    left: 0;
    right: 0
}

.fc .fc-timegrid-col-bg {
    z-index: 2
}

.fc .fc-timegrid-col-bg .fc-non-business {
    z-index: 1
}

.fc .fc-timegrid-col-bg .fc-bg-event {
    z-index: 2
}

.fc .fc-timegrid-col-bg .fc-highlight {
    z-index: 3
}

.fc .fc-timegrid-bg-harness {
    position: absolute;
    left: 0;
    right: 0
}

.fc .fc-timegrid-col-events {
    z-index: 3
}

.fc .fc-timegrid-now-indicator-container {
    bottom: 0;
    overflow: hidden
}

.fc-direction-ltr .fc-timegrid-col-events {
    margin: 0 2.5% 0 2px
}

.fc-direction-rtl .fc-timegrid-col-events {
    margin: 0 2px 0 2.5%
}

.fc-timegrid-event-harness {
    position: absolute
}

.fc-timegrid-event-harness>.fc-timegrid-event {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0
}

.fc-timegrid-event-harness-inset .fc-timegrid-event,
.fc-timegrid-event.fc-event-mirror,
.fc-timegrid-more-link {
    box-shadow: 0px 0px 0px 1px #fff;
    box-shadow: 0px 0px 0px 1px var(--fc-page-bg-color, #fff)
}

.fc-timegrid-event,
.fc-timegrid-more-link {
    font-size: .85em;
    font-size: var(--fc-small-font-size, .85em);
    border-radius: 3px
}

.fc-timegrid-event {
    margin-bottom: 1px
}

.fc-timegrid-event .fc-event-main {
    padding: 1px 1px 0
}

.fc-timegrid-event .fc-event-time {
    white-space: nowrap;
    font-size: .85em;
    font-size: var(--fc-small-font-size, .85em);
    margin-bottom: 1px
}

.fc-timegrid-event-short .fc-event-main-frame {
    flex-direction: row;
    overflow: hidden
}

.fc-timegrid-event-short .fc-event-time:after {
    content: '\00a0-\00a0'
}

.fc-timegrid-event-short .fc-event-title {
    font-size: .85em;
    font-size: var(--fc-small-font-size, .85em)
}

.fc-timegrid-more-link {
    position: absolute;
    z-index: 9999;
    color: inherit;
    color: var(--fc-more-link-text-color, inherit);
    background: #d0d0d0;
    background: var(--fc-more-link-bg-color, #d0d0d0);
    cursor: pointer;
    margin-bottom: 1px
}

.fc-timegrid-more-link-inner {
    padding: 3px 2px;
    top: 0
}

.fc-direction-ltr .fc-timegrid-more-link {
    right: 0
}

.fc-direction-rtl .fc-timegrid-more-link {
    left: 0
}

.fc .fc-timegrid-now-indicator-line {
    position: absolute;
    z-index: 4;
    left: 0;
    right: 0;
    border-style: solid;
    border-color: red;
    border-color: var(--fc-now-indicator-color, red);
    border-width: 1px 0 0
}

.fc .fc-timegrid-now-indicator-arrow {
    position: absolute;
    z-index: 4;
    margin-top: -5px;
    border-style: solid;
    border-color: red;
    border-color: var(--fc-now-indicator-color, red)
}

.fc-direction-ltr .fc-timegrid-now-indicator-arrow {
    left: 0;
    border-width: 5px 0 5px 6px;
    border-top-color: transparent;
    border-bottom-color: transparent
}

.fc-direction-rtl .fc-timegrid-now-indicator-arrow {
    right: 0;
    border-width: 5px 6px 5px 0;
    border-top-color: transparent;
    border-bottom-color: transparent
}

:root {
    --fc-list-event-dot-width: 10px;
    --fc-list-event-hover-bg-color: #f5f5f5
}

.fc-theme-standard .fc-list {
    border: 1px solid #ddd;
    border: 1px solid var(--fc-border-color, #ddd)
}

.fc .fc-list-empty {
    background-color: rgba(208, 208, 208, .3);
    background-color: var(--fc-neutral-bg-color, rgba(208, 208, 208, .3));
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center
}

.fc .fc-list-empty-cushion {
    margin: 5em 0
}

.fc .fc-list-table {
    width: 100%;
    border-style: hidden
}

.fc .fc-list-table tr>* {
    border-left: 0;
    border-right: 0
}

.fc .fc-list-sticky .fc-list-day>* {
    position: -webkit-sticky;
    position: sticky;
    top: 0;
    background: #fff;
    background: var(--fc-page-bg-color, #fff)
}

.fc .fc-list-table th {
    padding: 0
}

.fc .fc-list-table td,
.fc .fc-list-day-cushion {
    padding: 8px 14px
}

.fc .fc-list-day-cushion:after {
    content: "";
    clear: both;
    display: table
}

.fc-theme-standard .fc-list-day-cushion {
    background-color: rgba(208, 208, 208, .3);
    background-color: var(--fc-neutral-bg-color, rgba(208, 208, 208, .3))
}

.fc-direction-ltr .fc-list-day-text,
.fc-direction-rtl .fc-list-day-side-text {
    float: left
}

.fc-direction-ltr .fc-list-day-side-text,
.fc-direction-rtl .fc-list-day-text {
    float: right
}

.fc-direction-ltr .fc-list-table .fc-list-event-graphic {
    padding-right: 0
}

.fc-direction-rtl .fc-list-table .fc-list-event-graphic {
    padding-left: 0
}

.fc .fc-list-event.fc-event-forced-url {
    cursor: pointer
}

.fc .fc-list-event:hover td {
    background-color: #f5f5f5;
    background-color: var(--fc-list-event-hover-bg-color, #f5f5f5)
}

.fc .fc-list-event-graphic,
.fc .fc-list-event-time {
    white-space: nowrap;
    width: 1px
}

.fc .fc-list-event-dot {
    display: inline-block;
    box-sizing: content-box;
    width: 0;
    height: 0;
    border: 5px solid #3788d8;
    border: calc(var(--fc-list-event-dot-width, 10px)/2) solid var(--fc-event-border-color, #3788d8);
    border-radius: 5px;
    border-radius: calc(var(--fc-list-event-dot-width, 10px)/2)
}

.fc .fc-list-event-title a {
    color: inherit;
    text-decoration: none
}

.fc .fc-list-event.fc-event-forced-url:hover a {
    text-decoration: underline
}

.fc-theme-bootstrap a:not([href]) {
    color: inherit
}

img.cis-smiley,
img.emoji {
    display: inline !important;
    border: none !important;
    box-shadow: none !important;
    height: 1em !important;
    width: 1em !important;
    margin: 0 .07em !important;
    vertical-align: -.1em !important;
    background: 0 0 !important;
    padding: 0 !important
}

.cis-archives {
    box-sizing: border-box
}

.cis-archives-dropdown label {
    display: block
}

.cis-avatar {
    line-height: 0
}

.cis-avatar,
.cis-avatar img {
    box-sizing: border-box
}

.cis-avatar.aligncenter {
    text-align: center
}

.cis-audio {
    box-sizing: border-box
}

.cis-audio figcaption {
    margin-bottom: 1em;
    margin-top: .5em
}

.cis-audio audio {
    min-width: 300px;
    width: 100%
}

.cis-button__link {
    box-sizing: border-box;
    cursor: pointer;
    display: inline-block;
    text-align: center;
    word-break: break-word
}

.cis-button__link.aligncenter {
    text-align: center
}

.cis-button__link.alignright {
    text-align: right
}

:where(.cis-button__link) {
    border-radius: 9999px;
    box-shadow: none;
    padding: calc(.667em + 2px) calc(1.333em + 2px);
    text-decoration: none
}

.cis-button[style*=text-decoration] .cis-button__link {
    text-decoration: inherit
}

.cis-buttons>.cis-button.has-custom-width {
    max-width: none
}

.cis-buttons>.cis-button.has-custom-width .cis-button__link {
    width: 100%
}

.cis-buttons>.cis-button.has-custom-font-size .cis-button__link {
    font-size: inherit
}

.cis-buttons>.cis-button.cis-button__width-25 {
    width: calc(25% - var(--cis--style--block-gap, .5em)*.75)
}

.cis-buttons>.cis-button.cis-button__width-50 {
    width: calc(50% - var(--cis--style--block-gap, .5em)*.5)
}

.cis-buttons>.cis-button.cis-button__width-75 {
    width: calc(75% - var(--cis--style--block-gap, .5em)*.25)
}

.cis-buttons>.cis-button.cis-button__width-100 {
    flex-basis: 100%;
    width: 100%
}

.cis-buttons.is-vertical>.cis-button.cis-button__width-25 {
    width: 25%
}

.cis-buttons.is-vertical>.cis-button.cis-button__width-50 {
    width: 50%
}

.cis-buttons.is-vertical>.cis-button.cis-button__width-75 {
    width: 75%
}

.cis-button.is-style-squared,
.cis-button__link.cis-button.is-style-squared {
    border-radius: 0
}

.cis-button.no-border-radius,
.cis-button__link.no-border-radius {
    border-radius: 0 !important
}

.cis-button .cis-button__link.is-style-outline,
.cis-button.is-style-outline>.cis-button__link {
    border: 2px solid;
    padding: .667em 1.333em
}

.cis-button .cis-button__link.is-style-outline:not(.has-text-color),
.cis-button.is-style-outline>.cis-button__link:not(.has-text-color) {
    color: currentColor
}

.cis-button .cis-button__link.is-style-outline:not(.has-background),
.cis-button.is-style-outline>.cis-button__link:not(.has-background) {
    background-color: transparent;
    background-image: none
}

.cis-button .cis-button__link:where(.has-border-color) {
    border-width: initial
}

.cis-button .cis-button__link:where([style*=border-top-color]) {
    border-top-width: medium
}

.cis-button .cis-button__link:where([style*=border-right-color]) {
    border-right-width: medium
}

.cis-button .cis-button__link:where([style*=border-bottom-color]) {
    border-bottom-width: medium
}

.cis-button .cis-button__link:where([style*=border-left-color]) {
    border-left-width: medium
}

.cis-button .cis-button__link:where([style*=border-style]) {
    border-width: initial
}

.cis-button .cis-button__link:where([style*=border-top-style]) {
    border-top-width: medium
}

.cis-button .cis-button__link:where([style*=border-right-style]) {
    border-right-width: medium
}

.cis-button .cis-button__link:where([style*=border-bottom-style]) {
    border-bottom-width: medium
}

.cis-button .cis-button__link:where([style*=border-left-style]) {
    border-left-width: medium
}

.cis-buttons.is-vertical {
    flex-direction: column
}

.cis-buttons.is-vertical>.cis-button:last-child {
    margin-bottom: 0
}

.cis-buttons>.cis-button {
    display: inline-block;
    margin: 0
}

.cis-buttons.is-content-justification-left {
    justify-content: flex-start
}

.cis-buttons.is-content-justification-left.is-vertical {
    align-items: flex-start
}

.cis-buttons.is-content-justification-center {
    justify-content: center
}

.cis-buttons.is-content-justification-center.is-vertical {
    align-items: center
}

.cis-buttons.is-content-justification-right {
    justify-content: flex-end
}

.cis-buttons.is-content-justification-right.is-vertical {
    align-items: flex-end
}

.cis-buttons.is-content-justification-space-between {
    justify-content: space-between
}

.cis-buttons.aligncenter {
    text-align: center
}

.cis-buttons:not(.is-content-justification-space-between, .is-content-justification-right, .is-content-justification-left, .is-content-justification-center) .cis-button.aligncenter {
    margin-left: auto;
    margin-right: auto;
    width: 100%
}

.cis-buttons[style*=text-decoration] .cis-button,
.cis-buttons[style*=text-decoration] .cis-button__link {
    text-decoration: inherit
}

.cis-buttons.has-custom-font-size .cis-button__link {
    font-size: inherit
}

.cis-button.aligncenter,
.cis-calendar {
    text-align: center
}

.cis-calendar td,
.cis-calendar th {
    border: 1px solid;
    padding: .25em
}

.cis-calendar th {
    font-weight: 400
}

.cis-calendar caption {
    background-color: inherit
}

.cis-calendar table {
    border-collapse: collapse;
    width: 100%
}

.cis-calendar table:where(:not(.has-text-color)) {
    color: #40464d
}

.cis-calendar table:where(:not(.has-text-color)) td,
.cis-calendar table:where(:not(.has-text-color)) th {
    border-color: #ddd
}

.cis-calendar table.has-background th {
    background-color: inherit
}

.cis-calendar table.has-text-color th {
    color: inherit
}

:where(.cis-calendar table:not(.has-background) th) {
    background: #ddd
}

.cis-categories {
    box-sizing: border-box
}

.cis-categories.alignleft {
    margin-right: 2em
}

.cis-categories.alignright {
    margin-left: 2em
}

.cis-categories.cis-categories-dropdown.aligncenter {
    text-align: center
}

.cis-code {
    box-sizing: border-box
}

.cis-code code {
    display: block;
    font-family: inherit;
    overflow-wrap: break-word;
    white-space: pre-wrap
}

.cis-columns {
    align-items: normal !important;
    box-sizing: border-box;
    display: flex;
    flex-wrap: wrap !important
}

@media (min-width:782px) {
    .cis-columns {
        flex-wrap: nowrap !important
    }
}

.cis-columns.are-vertically-aligned-top {
    align-items: flex-start
}

.cis-columns.are-vertically-aligned-center {
    align-items: center
}

.cis-columns.are-vertically-aligned-bottom {
    align-items: flex-end
}

@media (max-width:781px) {
    .cis-columns:not(.is-not-stacked-on-mobile)>.cis-column {
        flex-basis: 100% !important
    }
}

@media (min-width:782px) {
    .cis-columns:not(.is-not-stacked-on-mobile)>.cis-column {
        flex-basis: 0;
        flex-grow: 1
    }

    .cis-columns:not(.is-not-stacked-on-mobile)>.cis-column[style*=flex-basis] {
        flex-grow: 0
    }
}

.cis-columns.is-not-stacked-on-mobile {
    flex-wrap: nowrap !important
}

.cis-columns.is-not-stacked-on-mobile>.cis-column {
    flex-basis: 0;
    flex-grow: 1
}

.cis-columns.is-not-stacked-on-mobile>.cis-column[style*=flex-basis] {
    flex-grow: 0
}

:where(.cis-columns) {
    margin-bottom: 1.75em
}

:where(.cis-columns.has-background) {
    padding: 1.25em 2.375em
}

.cis-column {
    flex-grow: 1;
    min-width: 0;
    overflow-wrap: break-word;
    word-break: break-word
}

.cis-column.is-vertically-aligned-top {
    align-self: flex-start
}

.cis-column.is-vertically-aligned-center {
    align-self: center
}

.cis-column.is-vertically-aligned-bottom {
    align-self: flex-end
}

.cis-column.is-vertically-aligned-stretch {
    align-self: stretch
}

.cis-column.is-vertically-aligned-bottom,
.cis-column.is-vertically-aligned-center,
.cis-column.is-vertically-aligned-top {
    width: 100%
}

.cis-post-comments {
    box-sizing: border-box
}

.cis-post-comments .alignleft {
    float: left
}

.cis-post-comments .alignright {
    float: right
}

.cis-post-comments .navigation:after {
    clear: both;
    content: "";
    display: table
}

.cis-post-comments .commentlist {
    clear: both;
    list-style: none;
    margin: 0;
    padding: 0
}

.cis-post-comments .commentlist .comment {
    min-height: 2.25em;
    padding-left: 3.25em
}

.cis-post-comments .commentlist .comment p {
    font-size: 1em;
    line-height: 1.8;
    margin: 1em 0
}

.cis-post-comments .commentlist .children {
    list-style: none;
    margin: 0;
    padding: 0
}

.cis-post-comments .comment-author {
    line-height: 1.5
}

.cis-post-comments .comment-author .avatar {
    border-radius: 1.5em;
    display: block;
    float: left;
    height: 2.5em;
    margin-right: .75em;
    margin-top: .5em;
    width: 2.5em
}

.cis-post-comments .comment-author cite {
    font-style: normal
}

.cis-post-comments .comment-meta {
    font-size: .875em;
    line-height: 1.5
}

.cis-post-comments .comment-meta b {
    font-weight: 400
}

.cis-post-comments .comment-meta .comment-awaiting-moderation {
    display: block;
    margin-bottom: 1em;
    margin-top: 1em
}

.cis-post-comments .comment-body .commentmetadata {
    font-size: .875em
}

.cis-post-comments .comment-form-author label,
.cis-post-comments .comment-form-comment label,
.cis-post-comments .comment-form-email label,
.cis-post-comments .comment-form-url label {
    display: block;
    margin-bottom: .25em
}

.cis-post-comments .comment-form input:not([type=submit]):not([type=checkbox]),
.cis-post-comments .comment-form textarea {
    box-sizing: border-box;
    display: block;
    width: 100%
}

.cis-post-comments .comment-form-cookies-consent {
    display: flex;
    gap: .25em
}

.cis-post-comments .comment-form-cookies-consent #cis-comment-cookies-consent {
    margin-top: .35em
}

.cis-post-comments .comment-reply-title {
    margin-bottom: 0
}

.cis-post-comments .comment-reply-title :where(small) {
    font-size: var(--cis--preset--font-size--medium, smaller);
    margin-left: .5em
}

.cis-post-comments .reply {
    font-size: .875em;
    margin-bottom: 1.4em
}

.cis-post-comments input:not([type=submit]),
.cis-post-comments textarea {
    border: 1px solid #949494;
    font-family: inherit;
    font-size: 1em
}

.cis-post-comments input:not([type=submit]):not([type=checkbox]),
.cis-post-comments textarea {
    padding: calc(.667em + 2px)
}

:where(.cis-post-comments input[type=submit]) {
    border: none
}

.cis-comments-pagination>.cis-comments-pagination-next,
.cis-comments-pagination>.cis-comments-pagination-numbers,
.cis-comments-pagination>.cis-comments-pagination-previous {
    margin-bottom: .5em;
    margin-right: .5em
}

.cis-comments-pagination>.cis-comments-pagination-next:last-child,
.cis-comments-pagination>.cis-comments-pagination-numbers:last-child,
.cis-comments-pagination>.cis-comments-pagination-previous:last-child {
    margin-right: 0
}

.cis-comments-pagination .cis-comments-pagination-previous-arrow {
    display: inline-block;
    margin-right: 1ch
}

.cis-comments-pagination .cis-comments-pagination-previous-arrow:not(.is-arrow-chevron) {
    transform: scaleX(1)
}

.cis-comments-pagination .cis-comments-pagination-next-arrow {
    display: inline-block;
    margin-left: 1ch
}

.cis-comments-pagination .cis-comments-pagination-next-arrow:not(.is-arrow-chevron) {
    transform: scaleX(1)
}

.cis-comments-pagination.aligncenter {
    justify-content: center
}

.cis-comment-template {
    box-sizing: border-box;
    list-style: none;
    margin-bottom: 0;
    max-width: 100%;
    padding: 0
}

.cis-comment-template li {
    clear: both
}

.cis-comment-template ol {
    list-style: none;
    margin-bottom: 0;
    max-width: 100%;
    padding-left: 2rem
}

.cis-comment-template.alignleft {
    float: left
}

.cis-comment-template.aligncenter {
    margin-left: auto;
    margin-right: auto;
    width: -moz-fit-content;
    width: fit-content
}

.cis-comment-template.alignright {
    float: right
}

.cis-cover,
.cis-cover-image {
    align-items: center;
    background-position: 50%;
    box-sizing: border-box;
    display: flex;
    justify-content: center;
    min-height: 430px;
    overflow: hidden;
    overflow: clip;
    padding: 1em;
    position: relative
}

.cis-cover .has-background-dim:not([class*=-background-color]),
.cis-cover-image .has-background-dim:not([class*=-background-color]),
.cis-cover-image.has-background-dim:not([class*=-background-color]),
.cis-cover.has-background-dim:not([class*=-background-color]) {
    background-color: #000
}

.cis-cover .has-background-dim.has-background-gradient,
.cis-cover-image .has-background-dim.has-background-gradient {
    background-color: transparent
}

.cis-cover-image.has-background-dim:before,
.cis-cover.has-background-dim:before {
    background-color: inherit;
    content: ""
}

.cis-cover .cis-cover__background,
.cis-cover .cis-cover__gradient-background,
.cis-cover-image .cis-cover__background,
.cis-cover-image .cis-cover__gradient-background,
.cis-cover-image.has-background-dim:not(.has-background-gradient):before,
.cis-cover.has-background-dim:not(.has-background-gradient):before {
    bottom: 0;
    left: 0;
    opacity: .5;
    position: absolute;
    right: 0;
    top: 0;
    z-index: 1
}

.cis-cover-image.has-background-dim.has-background-dim-10 .cis-cover__background,
.cis-cover-image.has-background-dim.has-background-dim-10 .cis-cover__gradient-background,
.cis-cover-image.has-background-dim.has-background-dim-10:not(.has-background-gradient):before,
.cis-cover.has-background-dim.has-background-dim-10 .cis-cover__background,
.cis-cover.has-background-dim.has-background-dim-10 .cis-cover__gradient-background,
.cis-cover.has-background-dim.has-background-dim-10:not(.has-background-gradient):before {
    opacity: .1
}

.cis-cover-image.has-background-dim.has-background-dim-20 .cis-cover__background,
.cis-cover-image.has-background-dim.has-background-dim-20 .cis-cover__gradient-background,
.cis-cover-image.has-background-dim.has-background-dim-20:not(.has-background-gradient):before,
.cis-cover.has-background-dim.has-background-dim-20 .cis-cover__background,
.cis-cover.has-background-dim.has-background-dim-20 .cis-cover__gradient-background,
.cis-cover.has-background-dim.has-background-dim-20:not(.has-background-gradient):before {
    opacity: .2
}

.cis-cover-image.has-background-dim.has-background-dim-30 .cis-cover__background,
.cis-cover-image.has-background-dim.has-background-dim-30 .cis-cover__gradient-background,
.cis-cover-image.has-background-dim.has-background-dim-30:not(.has-background-gradient):before,
.cis-cover.has-background-dim.has-background-dim-30 .cis-cover__background,
.cis-cover.has-background-dim.has-background-dim-30 .cis-cover__gradient-background,
.cis-cover.has-background-dim.has-background-dim-30:not(.has-background-gradient):before {
    opacity: .3
}

.cis-cover-image.has-background-dim.has-background-dim-40 .cis-cover__background,
.cis-cover-image.has-background-dim.has-background-dim-40 .cis-cover__gradient-background,
.cis-cover-image.has-background-dim.has-background-dim-40:not(.has-background-gradient):before,
.cis-cover.has-background-dim.has-background-dim-40 .cis-cover__background,
.cis-cover.has-background-dim.has-background-dim-40 .cis-cover__gradient-background,
.cis-cover.has-background-dim.has-background-dim-40:not(.has-background-gradient):before {
    opacity: .4
}

.cis-cover-image.has-background-dim.has-background-dim-50 .cis-cover__background,
.cis-cover-image.has-background-dim.has-background-dim-50 .cis-cover__gradient-background,
.cis-cover-image.has-background-dim.has-background-dim-50:not(.has-background-gradient):before,
.cis-cover.has-background-dim.has-background-dim-50 .cis-cover__background,
.cis-cover.has-background-dim.has-background-dim-50 .cis-cover__gradient-background,
.cis-cover.has-background-dim.has-background-dim-50:not(.has-background-gradient):before {
    opacity: .5
}

.cis-cover-image.has-background-dim.has-background-dim-60 .cis-cover__background,
.cis-cover-image.has-background-dim.has-background-dim-60 .cis-cover__gradient-background,
.cis-cover-image.has-background-dim.has-background-dim-60:not(.has-background-gradient):before,
.cis-cover.has-background-dim.has-background-dim-60 .cis-cover__background,
.cis-cover.has-background-dim.has-background-dim-60 .cis-cover__gradient-background,
.cis-cover.has-background-dim.has-background-dim-60:not(.has-background-gradient):before {
    opacity: .6
}

.cis-cover-image.has-background-dim.has-background-dim-70 .cis-cover__background,
.cis-cover-image.has-background-dim.has-background-dim-70 .cis-cover__gradient-background,
.cis-cover-image.has-background-dim.has-background-dim-70:not(.has-background-gradient):before,
.cis-cover.has-background-dim.has-background-dim-70 .cis-cover__background,
.cis-cover.has-background-dim.has-background-dim-70 .cis-cover__gradient-background,
.cis-cover.has-background-dim.has-background-dim-70:not(.has-background-gradient):before {
    opacity: .7
}

.cis-cover-image.has-background-dim.has-background-dim-80 .cis-cover__background,
.cis-cover-image.has-background-dim.has-background-dim-80 .cis-cover__gradient-background,
.cis-cover-image.has-background-dim.has-background-dim-80:not(.has-background-gradient):before,
.cis-cover.has-background-dim.has-background-dim-80 .cis-cover__background,
.cis-cover.has-background-dim.has-background-dim-80 .cis-cover__gradient-background,
.cis-cover.has-background-dim.has-background-dim-80:not(.has-background-gradient):before {
    opacity: .8
}

.cis-cover-image.has-background-dim.has-background-dim-90 .cis-cover__background,
.cis-cover-image.has-background-dim.has-background-dim-90 .cis-cover__gradient-background,
.cis-cover-image.has-background-dim.has-background-dim-90:not(.has-background-gradient):before,
.cis-cover.has-background-dim.has-background-dim-90 .cis-cover__background,
.cis-cover.has-background-dim.has-background-dim-90 .cis-cover__gradient-background,
.cis-cover.has-background-dim.has-background-dim-90:not(.has-background-gradient):before {
    opacity: .9
}

.cis-cover-image.has-background-dim.has-background-dim-100 .cis-cover__background,
.cis-cover-image.has-background-dim.has-background-dim-100 .cis-cover__gradient-background,
.cis-cover-image.has-background-dim.has-background-dim-100:not(.has-background-gradient):before,
.cis-cover.has-background-dim.has-background-dim-100 .cis-cover__background,
.cis-cover.has-background-dim.has-background-dim-100 .cis-cover__gradient-background,
.cis-cover.has-background-dim.has-background-dim-100:not(.has-background-gradient):before {
    opacity: 1
}

.cis-cover .cis-cover__background.has-background-dim.has-background-dim-0,
.cis-cover .cis-cover__gradient-background.has-background-dim.has-background-dim-0,
.cis-cover-image .cis-cover__background.has-background-dim.has-background-dim-0,
.cis-cover-image .cis-cover__gradient-background.has-background-dim.has-background-dim-0 {
    opacity: 0
}

.cis-cover .cis-cover__background.has-background-dim.has-background-dim-10,
.cis-cover .cis-cover__gradient-background.has-background-dim.has-background-dim-10,
.cis-cover-image .cis-cover__background.has-background-dim.has-background-dim-10,
.cis-cover-image .cis-cover__gradient-background.has-background-dim.has-background-dim-10 {
    opacity: .1
}

.cis-cover .cis-cover__background.has-background-dim.has-background-dim-20,
.cis-cover .cis-cover__gradient-background.has-background-dim.has-background-dim-20,
.cis-cover-image .cis-cover__background.has-background-dim.has-background-dim-20,
.cis-cover-image .cis-cover__gradient-background.has-background-dim.has-background-dim-20 {
    opacity: .2
}

.cis-cover .cis-cover__background.has-background-dim.has-background-dim-30,
.cis-cover .cis-cover__gradient-background.has-background-dim.has-background-dim-30,
.cis-cover-image .cis-cover__background.has-background-dim.has-background-dim-30,
.cis-cover-image .cis-cover__gradient-background.has-background-dim.has-background-dim-30 {
    opacity: .3
}

.cis-cover .cis-cover__background.has-background-dim.has-background-dim-40,
.cis-cover .cis-cover__gradient-background.has-background-dim.has-background-dim-40,
.cis-cover-image .cis-cover__background.has-background-dim.has-background-dim-40,
.cis-cover-image .cis-cover__gradient-background.has-background-dim.has-background-dim-40 {
    opacity: .4
}

.cis-cover .cis-cover__background.has-background-dim.has-background-dim-50,
.cis-cover .cis-cover__gradient-background.has-background-dim.has-background-dim-50,
.cis-cover-image .cis-cover__background.has-background-dim.has-background-dim-50,
.cis-cover-image .cis-cover__gradient-background.has-background-dim.has-background-dim-50 {
    opacity: .5
}

.cis-cover .cis-cover__background.has-background-dim.has-background-dim-60,
.cis-cover .cis-cover__gradient-background.has-background-dim.has-background-dim-60,
.cis-cover-image .cis-cover__background.has-background-dim.has-background-dim-60,
.cis-cover-image .cis-cover__gradient-background.has-background-dim.has-background-dim-60 {
    opacity: .6
}

.cis-cover .cis-cover__background.has-background-dim.has-background-dim-70,
.cis-cover .cis-cover__gradient-background.has-background-dim.has-background-dim-70,
.cis-cover-image .cis-cover__background.has-background-dim.has-background-dim-70,
.cis-cover-image .cis-cover__gradient-background.has-background-dim.has-background-dim-70 {
    opacity: .7
}

.cis-cover .cis-cover__background.has-background-dim.has-background-dim-80,
.cis-cover .cis-cover__gradient-background.has-background-dim.has-background-dim-80,
.cis-cover-image .cis-cover__background.has-background-dim.has-background-dim-80,
.cis-cover-image .cis-cover__gradient-background.has-background-dim.has-background-dim-80 {
    opacity: .8
}

.cis-cover .cis-cover__background.has-background-dim.has-background-dim-90,
.cis-cover .cis-cover__gradient-background.has-background-dim.has-background-dim-90,
.cis-cover-image .cis-cover__background.has-background-dim.has-background-dim-90,
.cis-cover-image .cis-cover__gradient-background.has-background-dim.has-background-dim-90 {
    opacity: .9
}

.cis-cover .cis-cover__background.has-background-dim.has-background-dim-100,
.cis-cover .cis-cover__gradient-background.has-background-dim.has-background-dim-100,
.cis-cover-image .cis-cover__background.has-background-dim.has-background-dim-100,
.cis-cover-image .cis-cover__gradient-background.has-background-dim.has-background-dim-100 {
    opacity: 1
}

.cis-cover-image.alignleft,
.cis-cover-image.alignright,
.cis-cover.alignleft,
.cis-cover.alignright {
    max-width: 420px;
    width: 100%
}

.cis-cover-image:after,
.cis-cover:after {
    content: "";
    display: block;
    font-size: 0;
    min-height: inherit
}

@supports (position:sticky) {

    .cis-cover-image:after,
    .cis-cover:after {
        content: none
    }
}

.cis-cover-image.aligncenter,
.cis-cover-image.alignleft,
.cis-cover-image.alignright,
.cis-cover.aligncenter,
.cis-cover.alignleft,
.cis-cover.alignright {
    display: flex
}

.cis-cover .cis-cover__inner-container,
.cis-cover-image .cis-cover__inner-container {
    color: inherit;
    width: 100%;
    z-index: 1
}

.cis-cover h1:not(.has-text-color),
.cis-cover h2:not(.has-text-color),
.cis-cover h3:not(.has-text-color),
.cis-cover h4:not(.has-text-color),
.cis-cover h5:not(.has-text-color),
.cis-cover h6:not(.has-text-color),
.cis-cover p:not(.has-text-color),
.cis-cover-image h1:not(.has-text-color),
.cis-cover-image h2:not(.has-text-color),
.cis-cover-image h3:not(.has-text-color),
.cis-cover-image h4:not(.has-text-color),
.cis-cover-image h5:not(.has-text-color),
.cis-cover-image h6:not(.has-text-color),
.cis-cover-image p:not(.has-text-color) {
    color: inherit
}

.cis-cover-image.is-position-top-left,
.cis-cover.is-position-top-left {
    align-items: flex-start;
    justify-content: flex-start
}

.cis-cover-image.is-position-top-center,
.cis-cover.is-position-top-center {
    align-items: flex-start;
    justify-content: center
}

.cis-cover-image.is-position-top-right,
.cis-cover.is-position-top-right {
    align-items: flex-start;
    justify-content: flex-end
}

.cis-cover-image.is-position-center-left,
.cis-cover.is-position-center-left {
    align-items: center;
    justify-content: flex-start
}

.cis-cover-image.is-position-center-center,
.cis-cover.is-position-center-center {
    align-items: center;
    justify-content: center
}

.cis-cover-image.is-position-center-right,
.cis-cover.is-position-center-right {
    align-items: center;
    justify-content: flex-end
}

.cis-cover-image.is-position-bottom-left,
.cis-cover.is-position-bottom-left {
    align-items: flex-end;
    justify-content: flex-start
}

.cis-cover-image.is-position-bottom-center,
.cis-cover.is-position-bottom-center {
    align-items: flex-end;
    justify-content: center
}

.cis-cover-image.is-position-bottom-right,
.cis-cover.is-position-bottom-right {
    align-items: flex-end;
    justify-content: flex-end
}

.cis-cover-image.has-custom-content-position.has-custom-content-position .cis-cover__inner-container,
.cis-cover.has-custom-content-position.has-custom-content-position .cis-cover__inner-container {
    margin: 0
}

.cis-cover-image.has-custom-content-position.has-custom-content-position.is-position-bottom-left .cis-cover__inner-container,
.cis-cover-image.has-custom-content-position.has-custom-content-position.is-position-bottom-right .cis-cover__inner-container,
.cis-cover-image.has-custom-content-position.has-custom-content-position.is-position-center-left .cis-cover__inner-container,
.cis-cover-image.has-custom-content-position.has-custom-content-position.is-position-center-right .cis-cover__inner-container,
.cis-cover-image.has-custom-content-position.has-custom-content-position.is-position-top-left .cis-cover__inner-container,
.cis-cover-image.has-custom-content-position.has-custom-content-position.is-position-top-right .cis-cover__inner-container,
.cis-cover.has-custom-content-position.has-custom-content-position.is-position-bottom-left .cis-cover__inner-container,
.cis-cover.has-custom-content-position.has-custom-content-position.is-position-bottom-right .cis-cover__inner-container,
.cis-cover.has-custom-content-position.has-custom-content-position.is-position-center-left .cis-cover__inner-container,
.cis-cover.has-custom-content-position.has-custom-content-position.is-position-center-right .cis-cover__inner-container,
.cis-cover.has-custom-content-position.has-custom-content-position.is-position-top-left .cis-cover__inner-container,
.cis-cover.has-custom-content-position.has-custom-content-position.is-position-top-right .cis-cover__inner-container {
    margin: 0;
    width: auto
}

.cis-cover .cis-cover__image-background,
.cis-cover video.cis-cover__video-background,
.cis-cover-image .cis-cover__image-background,
.cis-cover-image video.cis-cover__video-background {
    border: none;
    bottom: 0;
    box-shadow: none;
    height: 100%;
    left: 0;
    margin: 0;
    max-height: none;
    max-width: none;
    object-fit: cover;
    outline: none;
    padding: 0;
    position: absolute;
    right: 0;
    top: 0;
    width: 100%
}

.cis-cover-image.has-parallax,
.cis-cover.has-parallax,
.cis-cover__image-background.has-parallax,
video.cis-cover__video-background.has-parallax {
    background-attachment: fixed;
    background-repeat: no-repeat;
    background-size: cover
}

@supports (-webkit-touch-callout:inherit) {

    .cis-cover-image.has-parallax,
    .cis-cover.has-parallax,
    .cis-cover__image-background.has-parallax,
    video.cis-cover__video-background.has-parallax {
        background-attachment: scroll
    }
}

@media (prefers-reduced-motion:reduce) {

    .cis-cover-image.has-parallax,
    .cis-cover.has-parallax,
    .cis-cover__image-background.has-parallax,
    video.cis-cover__video-background.has-parallax {
        background-attachment: scroll
    }
}

.cis-cover-image.is-repeated,
.cis-cover.is-repeated,
.cis-cover__image-background.is-repeated,
video.cis-cover__video-background.is-repeated {
    background-repeat: repeat;
    background-size: auto
}

.cis-cover__image-background,
.cis-cover__video-background {
    z-index: 0
}

.cis-cover-image-text,
.cis-cover-image-text a,
.cis-cover-image-text a:active,
.cis-cover-image-text a:focus,
.cis-cover-image-text a:hover,
.cis-cover-text,
.cis-cover-text a,
.cis-cover-text a:active,
.cis-cover-text a:focus,
.cis-cover-text a:hover,
section.cis-cover-image h2,
section.cis-cover-image h2 a,
section.cis-cover-image h2 a:active,
section.cis-cover-image h2 a:focus,
section.cis-cover-image h2 a:hover {
    color: #fff
}

.cis-cover-image .cis-cover.has-left-content {
    justify-content: flex-start
}

.cis-cover-image .cis-cover.has-right-content {
    justify-content: flex-end
}

.cis-cover-image.has-left-content .cis-cover-image-text,
.cis-cover.has-left-content .cis-cover-text,
section.cis-cover-image.has-left-content>h2 {
    margin-left: 0;
    text-align: left
}

.cis-cover-image.has-right-content .cis-cover-image-text,
.cis-cover.has-right-content .cis-cover-text,
section.cis-cover-image.has-right-content>h2 {
    margin-right: 0;
    text-align: right
}

.cis-cover .cis-cover-text,
.cis-cover-image .cis-cover-image-text,
section.cis-cover-image>h2 {
    font-size: 2em;
    line-height: 1.25;
    margin-bottom: 0;
    max-width: 840px;
    padding: .44em;
    text-align: center;
    z-index: 1
}

:where(.cis-cover-image:not(.has-text-color)),
:where(.cis-cover:not(.has-text-color)) {
    color: #fff
}

:where(.cis-cover-image.is-light:not(.has-text-color)),
:where(.cis-cover.is-light:not(.has-text-color)) {
    color: #000
}

.cis-details {
    box-sizing: border-box;
    overflow: hidden
}

.cis-details summary {
    cursor: pointer
}

.cis-embed.alignleft,
.cis-embed.alignright,
.cis-block[data-align=left]>[data-type="core/embed"],
.cis-block[data-align=right]>[data-type="core/embed"] {
    max-width: 360px;
    width: 100%
}

.cis-embed.alignleft .cis-embed__wrapper,
.cis-embed.alignright .cis-embed__wrapper,
.cis-block[data-align=left]>[data-type="core/embed"] .cis-embed__wrapper,
.cis-block[data-align=right]>[data-type="core/embed"] .cis-embed__wrapper {
    min-width: 280px
}

.cis-cover .cis-embed {
    min-height: 240px;
    min-width: 320px
}

.cis-embed {
    overflow-wrap: break-word
}

.cis-embed figcaption {
    margin-bottom: 1em;
    margin-top: .5em
}

.cis-embed iframe {
    max-width: 100%
}

.cis-embed__wrapper {
    position: relative
}

.cis-embed-responsive .cis-has-aspect-ratio .cis-embed__wrapper:before {
    content: "";
    display: block;
    padding-top: 50%
}

.cis-embed-responsive .cis-has-aspect-ratio iframe {
    bottom: 0;
    height: 100%;
    left: 0;
    position: absolute;
    right: 0;
    top: 0;
    width: 100%
}

.cis-embed-responsive .cis-embed-aspect-21-9 .cis-embed__wrapper:before {
    padding-top: 42.85%
}

.cis-embed-responsive .cis-embed-aspect-18-9 .cis-embed__wrapper:before {
    padding-top: 50%
}

.cis-embed-responsive .cis-embed-aspect-16-9 .cis-embed__wrapper:before {
    padding-top: 56.25%
}

.cis-embed-responsive .cis-embed-aspect-4-3 .cis-embed__wrapper:before {
    padding-top: 75%
}

.cis-embed-responsive .cis-embed-aspect-1-1 .cis-embed__wrapper:before {
    padding-top: 100%
}

.cis-embed-responsive .cis-embed-aspect-9-16 .cis-embed__wrapper:before {
    padding-top: 177.77%
}

.cis-embed-responsive .cis-embed-aspect-1-2 .cis-embed__wrapper:before {
    padding-top: 200%
}

.cis-file {
    box-sizing: border-box
}

.cis-file:not(.cis-element-button) {
    font-size: .8em
}

.cis-file.aligncenter {
    text-align: center
}

.cis-file.alignright {
    text-align: right
}

.cis-file *+.cis-file__button {
    margin-left: .75em
}

:where(.cis-file) {
    margin-bottom: 1.5em
}

.cis-file__embed {
    margin-bottom: 1em
}

:where(.cis-file__button) {
    border-radius: 2em;
    display: inline-block;
    padding: .5em 1em
}

:where(.cis-file__button):is(a):active,
:where(.cis-file__button):is(a):focus,
:where(.cis-file__button):is(a):hover,
:where(.cis-file__button):is(a):visited {
    box-shadow: none;
    color: #fff;
    opacity: .85;
    text-decoration: none
}

.blocks-gallery-grid:not(.has-nested-images),
.cis-gallery:not(.has-nested-images) {
    display: flex;
    flex-wrap: wrap;
    list-style-type: none;
    margin: 0;
    padding: 0
}

.blocks-gallery-grid:not(.has-nested-images) .blocks-gallery-image,
.blocks-gallery-grid:not(.has-nested-images) .blocks-gallery-item,
.cis-gallery:not(.has-nested-images) .blocks-gallery-image,
.cis-gallery:not(.has-nested-images) .blocks-gallery-item {
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    justify-content: center;
    margin: 0 1em 1em 0;
    position: relative;
    width: calc(50% - 1em)
}

.blocks-gallery-grid:not(.has-nested-images) .blocks-gallery-image:nth-of-type(2n),
.blocks-gallery-grid:not(.has-nested-images) .blocks-gallery-item:nth-of-type(2n),
.cis-gallery:not(.has-nested-images) .blocks-gallery-image:nth-of-type(2n),
.cis-gallery:not(.has-nested-images) .blocks-gallery-item:nth-of-type(2n) {
    margin-right: 0
}

.blocks-gallery-grid:not(.has-nested-images) .blocks-gallery-image figure,
.blocks-gallery-grid:not(.has-nested-images) .blocks-gallery-item figure,
.cis-gallery:not(.has-nested-images) .blocks-gallery-image figure,
.cis-gallery:not(.has-nested-images) .blocks-gallery-item figure {
    align-items: flex-end;
    display: flex;
    height: 100%;
    justify-content: flex-start;
    margin: 0
}

.blocks-gallery-grid:not(.has-nested-images) .blocks-gallery-image img,
.blocks-gallery-grid:not(.has-nested-images) .blocks-gallery-item img,
.cis-gallery:not(.has-nested-images) .blocks-gallery-image img,
.cis-gallery:not(.has-nested-images) .blocks-gallery-item img {
    display: block;
    height: auto;
    max-width: 100%;
    width: auto
}

.blocks-gallery-grid:not(.has-nested-images) .blocks-gallery-image figcaption,
.blocks-gallery-grid:not(.has-nested-images) .blocks-gallery-item figcaption,
.cis-gallery:not(.has-nested-images) .blocks-gallery-image figcaption,
.cis-gallery:not(.has-nested-images) .blocks-gallery-item figcaption {
    background: linear-gradient(0deg, rgba(0, 0, 0, .7), rgba(0, 0, 0, .3) 70%, transparent);
    bottom: 0;
    box-sizing: border-box;
    color: #fff;
    font-size: .8em;
    margin: 0;
    max-height: 100%;
    overflow: auto;
    padding: 3em .77em .7em;
    position: absolute;
    text-align: center;
    width: 100%;
    z-index: 2
}

.blocks-gallery-grid:not(.has-nested-images) .blocks-gallery-image figcaption img,
.blocks-gallery-grid:not(.has-nested-images) .blocks-gallery-item figcaption img,
.cis-gallery:not(.has-nested-images) .blocks-gallery-image figcaption img,
.cis-gallery:not(.has-nested-images) .blocks-gallery-item figcaption img {
    display: inline
}

.blocks-gallery-grid:not(.has-nested-images) figcaption,
.cis-gallery:not(.has-nested-images) figcaption {
    flex-grow: 1
}

.blocks-gallery-grid:not(.has-nested-images).is-cropped .blocks-gallery-image a,
.blocks-gallery-grid:not(.has-nested-images).is-cropped .blocks-gallery-image img,
.blocks-gallery-grid:not(.has-nested-images).is-cropped .blocks-gallery-item a,
.blocks-gallery-grid:not(.has-nested-images).is-cropped .blocks-gallery-item img,
.cis-gallery:not(.has-nested-images).is-cropped .blocks-gallery-image a,
.cis-gallery:not(.has-nested-images).is-cropped .blocks-gallery-image img,
.cis-gallery:not(.has-nested-images).is-cropped .blocks-gallery-item a,
.cis-gallery:not(.has-nested-images).is-cropped .blocks-gallery-item img {
    flex: 1;
    height: 100%;
    object-fit: cover;
    width: 100%
}

.blocks-gallery-grid:not(.has-nested-images).columns-1 .blocks-gallery-image,
.blocks-gallery-grid:not(.has-nested-images).columns-1 .blocks-gallery-item,
.cis-gallery:not(.has-nested-images).columns-1 .blocks-gallery-image,
.cis-gallery:not(.has-nested-images).columns-1 .blocks-gallery-item {
    margin-right: 0;
    width: 100%
}

@media (min-width:600px) {

    .blocks-gallery-grid:not(.has-nested-images).columns-3 .blocks-gallery-image,
    .blocks-gallery-grid:not(.has-nested-images).columns-3 .blocks-gallery-item,
    .cis-gallery:not(.has-nested-images).columns-3 .blocks-gallery-image,
    .cis-gallery:not(.has-nested-images).columns-3 .blocks-gallery-item {
        margin-right: 1em;
        width: calc(33.33333% - .66667em)
    }

    .blocks-gallery-grid:not(.has-nested-images).columns-4 .blocks-gallery-image,
    .blocks-gallery-grid:not(.has-nested-images).columns-4 .blocks-gallery-item,
    .cis-gallery:not(.has-nested-images).columns-4 .blocks-gallery-image,
    .cis-gallery:not(.has-nested-images).columns-4 .blocks-gallery-item {
        margin-right: 1em;
        width: calc(25% - .75em)
    }

    .blocks-gallery-grid:not(.has-nested-images).columns-5 .blocks-gallery-image,
    .blocks-gallery-grid:not(.has-nested-images).columns-5 .blocks-gallery-item,
    .cis-gallery:not(.has-nested-images).columns-5 .blocks-gallery-image,
    .cis-gallery:not(.has-nested-images).columns-5 .blocks-gallery-item {
        margin-right: 1em;
        width: calc(20% - .8em)
    }

    .blocks-gallery-grid:not(.has-nested-images).columns-6 .blocks-gallery-image,
    .blocks-gallery-grid:not(.has-nested-images).columns-6 .blocks-gallery-item,
    .cis-gallery:not(.has-nested-images).columns-6 .blocks-gallery-image,
    .cis-gallery:not(.has-nested-images).columns-6 .blocks-gallery-item {
        margin-right: 1em;
        width: calc(16.66667% - .83333em)
    }

    .blocks-gallery-grid:not(.has-nested-images).columns-7 .blocks-gallery-image,
    .blocks-gallery-grid:not(.has-nested-images).columns-7 .blocks-gallery-item,
    .cis-gallery:not(.has-nested-images).columns-7 .blocks-gallery-image,
    .cis-gallery:not(.has-nested-images).columns-7 .blocks-gallery-item {
        margin-right: 1em;
        width: calc(14.28571% - .85714em)
    }

    .blocks-gallery-grid:not(.has-nested-images).columns-8 .blocks-gallery-image,
    .blocks-gallery-grid:not(.has-nested-images).columns-8 .blocks-gallery-item,
    .cis-gallery:not(.has-nested-images).columns-8 .blocks-gallery-image,
    .cis-gallery:not(.has-nested-images).columns-8 .blocks-gallery-item {
        margin-right: 1em;
        width: calc(12.5% - .875em)
    }

    .blocks-gallery-grid:not(.has-nested-images).columns-1 .blocks-gallery-image:nth-of-type(1n),
    .blocks-gallery-grid:not(.has-nested-images).columns-1 .blocks-gallery-item:nth-of-type(1n),
    .blocks-gallery-grid:not(.has-nested-images).columns-2 .blocks-gallery-image:nth-of-type(2n),
    .blocks-gallery-grid:not(.has-nested-images).columns-2 .blocks-gallery-item:nth-of-type(2n),
    .blocks-gallery-grid:not(.has-nested-images).columns-3 .blocks-gallery-image:nth-of-type(3n),
    .blocks-gallery-grid:not(.has-nested-images).columns-3 .blocks-gallery-item:nth-of-type(3n),
    .blocks-gallery-grid:not(.has-nested-images).columns-4 .blocks-gallery-image:nth-of-type(4n),
    .blocks-gallery-grid:not(.has-nested-images).columns-4 .blocks-gallery-item:nth-of-type(4n),
    .blocks-gallery-grid:not(.has-nested-images).columns-5 .blocks-gallery-image:nth-of-type(5n),
    .blocks-gallery-grid:not(.has-nested-images).columns-5 .blocks-gallery-item:nth-of-type(5n),
    .blocks-gallery-grid:not(.has-nested-images).columns-6 .blocks-gallery-image:nth-of-type(6n),
    .blocks-gallery-grid:not(.has-nested-images).columns-6 .blocks-gallery-item:nth-of-type(6n),
    .blocks-gallery-grid:not(.has-nested-images).columns-7 .blocks-gallery-image:nth-of-type(7n),
    .blocks-gallery-grid:not(.has-nested-images).columns-7 .blocks-gallery-item:nth-of-type(7n),
    .blocks-gallery-grid:not(.has-nested-images).columns-8 .blocks-gallery-image:nth-of-type(8n),
    .blocks-gallery-grid:not(.has-nested-images).columns-8 .blocks-gallery-item:nth-of-type(8n),
    .cis-gallery:not(.has-nested-images).columns-1 .blocks-gallery-image:nth-of-type(1n),
    .cis-gallery:not(.has-nested-images).columns-1 .blocks-gallery-item:nth-of-type(1n),
    .cis-gallery:not(.has-nested-images).columns-2 .blocks-gallery-image:nth-of-type(2n),
    .cis-gallery:not(.has-nested-images).columns-2 .blocks-gallery-item:nth-of-type(2n),
    .cis-gallery:not(.has-nested-images).columns-3 .blocks-gallery-image:nth-of-type(3n),
    .cis-gallery:not(.has-nested-images).columns-3 .blocks-gallery-item:nth-of-type(3n),
    .cis-gallery:not(.has-nested-images).columns-4 .blocks-gallery-image:nth-of-type(4n),
    .cis-gallery:not(.has-nested-images).columns-4 .blocks-gallery-item:nth-of-type(4n),
    .cis-gallery:not(.has-nested-images).columns-5 .blocks-gallery-image:nth-of-type(5n),
    .cis-gallery:not(.has-nested-images).columns-5 .blocks-gallery-item:nth-of-type(5n),
    .cis-gallery:not(.has-nested-images).columns-6 .blocks-gallery-image:nth-of-type(6n),
    .cis-gallery:not(.has-nested-images).columns-6 .blocks-gallery-item:nth-of-type(6n),
    .cis-gallery:not(.has-nested-images).columns-7 .blocks-gallery-image:nth-of-type(7n),
    .cis-gallery:not(.has-nested-images).columns-7 .blocks-gallery-item:nth-of-type(7n),
    .cis-gallery:not(.has-nested-images).columns-8 .blocks-gallery-image:nth-of-type(8n),
    .cis-gallery:not(.has-nested-images).columns-8 .blocks-gallery-item:nth-of-type(8n) {
        margin-right: 0
    }
}

.blocks-gallery-grid:not(.has-nested-images) .blocks-gallery-image:last-child,
.blocks-gallery-grid:not(.has-nested-images) .blocks-gallery-item:last-child,
.cis-gallery:not(.has-nested-images) .blocks-gallery-image:last-child,
.cis-gallery:not(.has-nested-images) .blocks-gallery-item:last-child {
    margin-right: 0
}

.blocks-gallery-grid:not(.has-nested-images).alignleft,
.blocks-gallery-grid:not(.has-nested-images).alignright,
.cis-gallery:not(.has-nested-images).alignleft,
.cis-gallery:not(.has-nested-images).alignright {
    max-width: 420px;
    width: 100%
}

.blocks-gallery-grid:not(.has-nested-images).aligncenter .blocks-gallery-item figure,
.cis-gallery:not(.has-nested-images).aligncenter .blocks-gallery-item figure {
    justify-content: center
}

.cis-gallery:not(.is-cropped) .blocks-gallery-item {
    align-self: flex-start
}

figure.cis-gallery.has-nested-images {
    align-items: normal
}

.cis-gallery.has-nested-images figure.cis-image:not(#individual-image) {
    margin: 0;
    width: calc(50% - var(--cis--style--unstable-gallery-gap, 16px)/2)
}

.cis-gallery.has-nested-images figure.cis-image {
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    justify-content: center;
    max-width: 100%;
    position: relative
}

.cis-gallery.has-nested-images figure.cis-image>a,
.cis-gallery.has-nested-images figure.cis-image>div {
    flex-direction: column;
    flex-grow: 1;
    margin: 0
}

.cis-gallery.has-nested-images figure.cis-image img {
    display: block;
    height: auto;
    max-width: 100% !important;
    width: auto
}

.cis-gallery.has-nested-images figure.cis-image figcaption {
    background: linear-gradient(0deg, rgba(0, 0, 0, .7), rgba(0, 0, 0, .3) 70%, transparent);
    bottom: 0;
    box-sizing: border-box;
    color: #fff;
    font-size: 13px;
    left: 0;
    margin-bottom: 0;
    max-height: 60%;
    overflow: auto;
    padding: 0 8px 8px;
    position: absolute;
    text-align: center;
    width: 100%
}

.cis-gallery.has-nested-images figure.cis-image figcaption img {
    display: inline
}

.cis-gallery.has-nested-images figure.cis-image figcaption a {
    color: inherit
}

.cis-gallery.has-nested-images figure.cis-image.has-custom-border img {
    box-sizing: border-box
}

.cis-gallery.has-nested-images figure.cis-image.has-custom-border>a,
.cis-gallery.has-nested-images figure.cis-image.has-custom-border>div,
.cis-gallery.has-nested-images figure.cis-image.is-style-rounded>a,
.cis-gallery.has-nested-images figure.cis-image.is-style-rounded>div {
    flex: 1 1 auto
}

.cis-gallery.has-nested-images figure.cis-image.has-custom-border figcaption,
.cis-gallery.has-nested-images figure.cis-image.is-style-rounded figcaption {
    background: 0 0;
    color: inherit;
    flex: initial;
    margin: 0;
    padding: 10px 10px 9px;
    position: relative
}

.cis-gallery.has-nested-images figcaption {
    flex-basis: 100%;
    flex-grow: 1;
    text-align: center
}

.cis-gallery.has-nested-images:not(.is-cropped) figure.cis-image:not(#individual-image) {
    margin-bottom: auto;
    margin-top: 0
}

.cis-gallery.has-nested-images.is-cropped figure.cis-image:not(#individual-image) {
    align-self: inherit
}

.cis-gallery.has-nested-images.is-cropped figure.cis-image:not(#individual-image)>a,
.cis-gallery.has-nested-images.is-cropped figure.cis-image:not(#individual-image)>div:not(.components-drop-zone) {
    display: flex
}

.cis-gallery.has-nested-images.is-cropped figure.cis-image:not(#individual-image) a,
.cis-gallery.has-nested-images.is-cropped figure.cis-image:not(#individual-image) img {
    flex: 1 0 0%;
    height: 100%;
    object-fit: cover;
    width: 100%
}

.cis-gallery.has-nested-images.columns-1 figure.cis-image:not(#individual-image) {
    width: 100%
}

@media (min-width:600px) {
    .cis-gallery.has-nested-images.columns-3 figure.cis-image:not(#individual-image) {
        width: calc(33.33333% - var(--cis--style--unstable-gallery-gap, 16px)*.66667)
    }

    .cis-gallery.has-nested-images.columns-4 figure.cis-image:not(#individual-image) {
        width: calc(25% - var(--cis--style--unstable-gallery-gap, 16px)*.75)
    }

    .cis-gallery.has-nested-images.columns-5 figure.cis-image:not(#individual-image) {
        width: calc(20% - var(--cis--style--unstable-gallery-gap, 16px)*.8)
    }

    .cis-gallery.has-nested-images.columns-6 figure.cis-image:not(#individual-image) {
        width: calc(16.66667% - var(--cis--style--unstable-gallery-gap, 16px)*.83333)
    }

    .cis-gallery.has-nested-images.columns-7 figure.cis-image:not(#individual-image) {
        width: calc(14.28571% - var(--cis--style--unstable-gallery-gap, 16px)*.85714)
    }

    .cis-gallery.has-nested-images.columns-8 figure.cis-image:not(#individual-image) {
        width: calc(12.5% - var(--cis--style--unstable-gallery-gap, 16px)*.875)
    }

    .cis-gallery.has-nested-images.columns-default figure.cis-image:not(#individual-image) {
        width: calc(33.33% - var(--cis--style--unstable-gallery-gap, 16px)*.66667)
    }

    .cis-gallery.has-nested-images.columns-default figure.cis-image:not(#individual-image):first-child:nth-last-child(2),
    .cis-gallery.has-nested-images.columns-default figure.cis-image:not(#individual-image):first-child:nth-last-child(2)~figure.cis-image:not(#individual-image) {
        width: calc(50% - var(--cis--style--unstable-gallery-gap, 16px)*.5)
    }

    .cis-gallery.has-nested-images.columns-default figure.cis-image:not(#individual-image):first-child:last-child {
        width: 100%
    }
}

.cis-gallery.has-nested-images.alignleft,
.cis-gallery.has-nested-images.alignright {
    max-width: 420px;
    width: 100%
}

.cis-gallery.has-nested-images.aligncenter {
    justify-content: center
}

.cis-group {
    box-sizing: border-box
}

h1.has-background,
h2.has-background,
h3.has-background,
h4.has-background,
h5.has-background,
h6.has-background {
    padding: 1.25em 2.375em
}

h1.has-text-align-left[style*=writing-mode]:where([style*=vertical-lr]),
h1.has-text-align-right[style*=writing-mode]:where([style*=vertical-rl]),
h2.has-text-align-left[style*=writing-mode]:where([style*=vertical-lr]),
h2.has-text-align-right[style*=writing-mode]:where([style*=vertical-rl]),
h3.has-text-align-left[style*=writing-mode]:where([style*=vertical-lr]),
h3.has-text-align-right[style*=writing-mode]:where([style*=vertical-rl]),
h4.has-text-align-left[style*=writing-mode]:where([style*=vertical-lr]),
h4.has-text-align-right[style*=writing-mode]:where([style*=vertical-rl]),
h5.has-text-align-left[style*=writing-mode]:where([style*=vertical-lr]),
h5.has-text-align-right[style*=writing-mode]:where([style*=vertical-rl]),
h6.has-text-align-left[style*=writing-mode]:where([style*=vertical-lr]),
h6.has-text-align-right[style*=writing-mode]:where([style*=vertical-rl]) {
    rotate: 180deg
}

.cis-image img {
    box-sizing: border-box;
    height: auto;
    max-width: 100%;
    vertical-align: bottom
}

.cis-image[style*=border-radius] img,
.cis-image[style*=border-radius]>a {
    border-radius: inherit
}

.cis-image.has-custom-border img {
    box-sizing: border-box
}

.cis-image.aligncenter {
    text-align: center
}

.cis-image.alignfull img,
.cis-image.alignwide img {
    height: auto;
    width: 100%
}

.cis-image .aligncenter,
.cis-image .alignleft,
.cis-image .alignright,
.cis-image.aligncenter,
.cis-image.alignleft,
.cis-image.alignright {
    display: table
}

.cis-image .aligncenter>figcaption,
.cis-image .alignleft>figcaption,
.cis-image .alignright>figcaption,
.cis-image.aligncenter>figcaption,
.cis-image.alignleft>figcaption,
.cis-image.alignright>figcaption {
    caption-side: bottom;
    display: table-caption
}

.cis-image .alignleft {
    float: left;
    margin: .5em 1em .5em 0
}

.cis-image .alignright {
    float: right;
    margin: .5em 0 .5em 1em
}

.cis-image .aligncenter {
    margin-left: auto;
    margin-right: auto
}

.cis-image figcaption {
    margin-bottom: 1em;
    margin-top: .5em
}

.cis-image .is-style-rounded img,
.cis-image.is-style-circle-mask img,
.cis-image.is-style-rounded img {
    border-radius: 9999px
}

@supports ((-webkit-mask-image:none) or (mask-image:none)) or (-webkit-mask-image:none) {
    .cis-image.is-style-circle-mask img {
        border-radius: 0;
        -webkit-mask-image: url('data:image/svg+xml;utf8,<svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg"><circle cx="50" cy="50" r="50"/></svg>');
        mask-image: url('data:image/svg+xml;utf8,<svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg"><circle cx="50" cy="50" r="50"/></svg>');
        mask-mode: alpha;
        -webkit-mask-position: center;
        mask-position: center;
        -webkit-mask-repeat: no-repeat;
        mask-repeat: no-repeat;
        -webkit-mask-size: contain;
        mask-size: contain
    }
}

.cis-image :where(.has-border-color) {
    border-style: solid
}

.cis-image :where([style*=border-top-color]) {
    border-top-style: solid
}

.cis-image :where([style*=border-right-color]) {
    border-right-style: solid
}

.cis-image :where([style*=border-bottom-color]) {
    border-bottom-style: solid
}

.cis-image :where([style*=border-left-color]) {
    border-left-style: solid
}

.cis-image :where([style*=border-width]) {
    border-style: solid
}

.cis-image :where([style*=border-top-width]) {
    border-top-style: solid
}

.cis-image :where([style*=border-right-width]) {
    border-right-style: solid
}

.cis-image :where([style*=border-bottom-width]) {
    border-bottom-style: solid
}

.cis-image :where([style*=border-left-width]) {
    border-left-style: solid
}

.cis-image figure {
    margin: 0
}

.cis-lightbox-container {
    display: flex;
    flex-direction: column;
    position: relative
}

.cis-lightbox-container img {
    cursor: zoom-in
}

.cis-lightbox-container img:hover+button {
    opacity: 1
}

.cis-lightbox-container button {
    align-items: center;
    -webkit-backdrop-filter: blur(16px) saturate(180%);
    backdrop-filter: blur(16px) saturate(180%);
    background-color: rgba(90, 90, 90, .25);
    border: none;
    border-radius: 4px;
    cursor: zoom-in;
    display: flex;
    height: 20px;
    justify-content: center;
    opacity: 0;
    padding: 0;
    position: absolute;
    right: 16px;
    text-align: center;
    top: 16px;
    transition: opacity .2s ease;
    width: 20px;
    z-index: 100
}

.cis-lightbox-container button:focus-visible {
    outline: 3px auto rgba(90, 90, 90, .25);
    outline: 3px auto -webkit-focus-ring-color;
    outline-offset: 3px
}

.cis-lightbox-container button:hover {
    cursor: pointer;
    opacity: 1
}

.cis-lightbox-container button:focus {
    opacity: 1
}

.cis-lightbox-container button:focus,
.cis-lightbox-container button:hover,
.cis-lightbox-container button:not(:hover):not(:active):not(.has-background) {
    background-color: rgba(90, 90, 90, .25);
    border: none
}

.cis-lightbox-overlay {
    box-sizing: border-box;
    cursor: zoom-out;
    height: 100vh;
    left: 0;
    overflow: hidden;
    position: fixed;
    top: 0;
    visibility: hidden;
    width: 100vw;
    z-index: 100000
}

.cis-lightbox-overlay .close-button {
    align-items: center;
    cursor: pointer;
    display: flex;
    justify-content: center;
    min-height: 40px;
    min-width: 40px;
    padding: 0;
    position: absolute;
    right: calc(env(safe-area-inset-right) + 16px);
    top: calc(env(safe-area-inset-top) + 16px);
    z-index: 5000000
}

.cis-lightbox-overlay .close-button:focus,
.cis-lightbox-overlay .close-button:hover,
.cis-lightbox-overlay .close-button:not(:hover):not(:active):not(.has-background) {
    background: 0 0;
    border: none
}

.cis-lightbox-overlay .lightbox-image-container {
    height: var(--cis--lightbox-container-height);
    left: 50%;
    overflow: hidden;
    position: absolute;
    top: 50%;
    transform: translate(-50%, -50%);
    transform-origin: top left;
    width: var(--cis--lightbox-container-width);
    z-index: 9999999999
}

.cis-lightbox-overlay .cis-image {
    align-items: center;
    box-sizing: border-box;
    display: flex;
    height: 100%;
    justify-content: center;
    margin: 0;
    position: relative;
    transform-origin: 0 0;
    width: 100%;
    z-index: 3000000
}

.cis-lightbox-overlay .cis-image img {
    height: var(--cis--lightbox-image-height);
    min-height: var(--cis--lightbox-image-height);
    min-width: var(--cis--lightbox-image-width);
    width: var(--cis--lightbox-image-width)
}

.cis-lightbox-overlay .cis-image figcaption {
    display: none
}

.cis-lightbox-overlay button {
    background: 0 0;
    border: none
}

.cis-lightbox-overlay .scrim {
    background-color: #fff;
    height: 100%;
    opacity: .9;
    position: absolute;
    width: 100%;
    z-index: 2000000
}

.cis-lightbox-overlay.active {
    animation: turn-on-visibility .25s both;
    visibility: visible
}

.cis-lightbox-overlay.active img {
    animation: turn-on-visibility .35s both
}

.cis-lightbox-overlay.hideanimationenabled:not(.active) {
    animation: turn-off-visibility .35s both
}

.cis-lightbox-overlay.hideanimationenabled:not(.active) img {
    animation: turn-off-visibility .25s both
}

@media (prefers-reduced-motion:no-preference) {
    .cis-lightbox-overlay.zoom.active {
        animation: none;
        opacity: 1;
        visibility: visible
    }

    .cis-lightbox-overlay.zoom.active .lightbox-image-container {
        animation: lightbox-zoom-in .4s
    }

    .cis-lightbox-overlay.zoom.active .lightbox-image-container img {
        animation: none
    }

    .cis-lightbox-overlay.zoom.active .scrim {
        animation: turn-on-visibility .4s forwards
    }

    .cis-lightbox-overlay.zoom.hideanimationenabled:not(.active) {
        animation: none
    }

    .cis-lightbox-overlay.zoom.hideanimationenabled:not(.active) .lightbox-image-container {
        animation: lightbox-zoom-out .4s
    }

    .cis-lightbox-overlay.zoom.hideanimationenabled:not(.active) .lightbox-image-container img {
        animation: none
    }

    .cis-lightbox-overlay.zoom.hideanimationenabled:not(.active) .scrim {
        animation: turn-off-visibility .4s forwards
    }
}

@keyframes turn-on-visibility {
    0% {
        opacity: 0
    }

    to {
        opacity: 1
    }
}

@keyframes turn-off-visibility {
    0% {
        opacity: 1;
        visibility: visible
    }

    99% {
        opacity: 0;
        visibility: visible
    }

    to {
        opacity: 0;
        visibility: hidden
    }
}

@keyframes lightbox-zoom-in {
    0% {
        transform: translate(calc(-50vw + var(--cis--lightbox-initial-left-position)), calc(-50vh + var(--cis--lightbox-initial-top-position))) scale(var(--cis--lightbox-scale))
    }

    to {
        transform: translate(-50%, -50%) scale(1)
    }
}

@keyframes lightbox-zoom-out {
    0% {
        transform: translate(-50%, -50%) scale(1);
        visibility: visible
    }

    99% {
        visibility: visible
    }

    to {
        transform: translate(calc(-50vw + var(--cis--lightbox-initial-left-position)), calc(-50vh + var(--cis--lightbox-initial-top-position))) scale(var(--cis--lightbox-scale));
        visibility: hidden
    }
}

ol.cis-latest-comments {
    box-sizing: border-box;
    margin-left: 0
}

:where(.cis-latest-comments:not([style*=line-height] .cis-latest-comments__comment)) {
    line-height: 1.1
}

:where(.cis-latest-comments:not([style*=line-height] .cis-latest-comments__comment-excerpt p)) {
    line-height: 1.8
}

.has-dates :where(.cis-latest-comments:not([style*=line-height])),
.has-excerpts :where(.cis-latest-comments:not([style*=line-height])) {
    line-height: 1.5
}

.cis-latest-comments .cis-latest-comments {
    padding-left: 0
}

.cis-latest-comments__comment {
    list-style: none;
    margin-bottom: 1em
}

.has-avatars .cis-latest-comments__comment {
    list-style: none;
    min-height: 2.25em
}

.has-avatars .cis-latest-comments__comment .cis-latest-comments__comment-excerpt,
.has-avatars .cis-latest-comments__comment .cis-latest-comments__comment-meta {
    margin-left: 3.25em
}

.cis-latest-comments__comment-excerpt p {
    font-size: .875em;
    margin: .36em 0 1.4em
}

.cis-latest-comments__comment-date {
    display: block;
    font-size: .75em
}

.cis-latest-comments .avatar,
.cis-latest-comments__comment-avatar {
    border-radius: 1.5em;
    display: block;
    float: left;
    height: 2.5em;
    margin-right: .75em;
    width: 2.5em
}

.cis-latest-comments[class*=-font-size] a,
.cis-latest-comments[style*=font-size] a {
    font-size: inherit
}

.cis-latest-posts {
    box-sizing: border-box
}

.cis-latest-posts.alignleft {
    margin-right: 2em
}

.cis-latest-posts.alignright {
    margin-left: 2em
}

.cis-latest-posts.cis-latest-posts__list {
    list-style: none;
    padding-left: 0
}

.cis-latest-posts.cis-latest-posts__list li {
    clear: both
}

.cis-latest-posts.is-grid {
    display: flex;
    flex-wrap: wrap;
    padding: 0
}

.cis-latest-posts.is-grid li {
    margin: 0 1.25em 1.25em 0;
    width: 100%
}

@media (min-width:600px) {
    .cis-latest-posts.columns-2 li {
        width: calc(50% - .625em)
    }

    .cis-latest-posts.columns-2 li:nth-child(2n) {
        margin-right: 0
    }

    .cis-latest-posts.columns-3 li {
        width: calc(33.33333% - .83333em)
    }

    .cis-latest-posts.columns-3 li:nth-child(3n) {
        margin-right: 0
    }

    .cis-latest-posts.columns-4 li {
        width: calc(25% - .9375em)
    }

    .cis-latest-posts.columns-4 li:nth-child(4n) {
        margin-right: 0
    }

    .cis-latest-posts.columns-5 li {
        width: calc(20% - 1em)
    }

    .cis-latest-posts.columns-5 li:nth-child(5n) {
        margin-right: 0
    }

    .cis-latest-posts.columns-6 li {
        width: calc(16.66667% - 1.04167em)
    }

    .cis-latest-posts.columns-6 li:nth-child(6n) {
        margin-right: 0
    }
}

.cis-latest-posts__post-author,
.cis-latest-posts__post-date {
    display: block;
    font-size: .8125em
}

.cis-latest-posts__post-excerpt {
    margin-bottom: 1em;
    margin-top: .5em
}

.cis-latest-posts__featured-image a {
    display: inline-block
}

.cis-latest-posts__featured-image img {
    height: auto;
    max-width: 100%;
    width: auto
}

.cis-latest-posts__featured-image.alignleft {
    float: left;
    margin-right: 1em
}

.cis-latest-posts__featured-image.alignright {
    float: right;
    margin-left: 1em
}

.cis-latest-posts__featured-image.aligncenter {
    margin-bottom: 1em;
    text-align: center
}

ol,
ul {
    box-sizing: border-box
}

ol.has-background,
ul.has-background {
    padding: 1.25em 2.375em
}

.cis-media-text {
    box-sizing: border-box;
    direction: ltr;
    display: grid;
    grid-template-columns: 50% 1fr;
    grid-template-rows: auto
}

.cis-media-text.has-media-on-the-right {
    grid-template-columns: 1fr 50%
}

.cis-media-text.is-vertically-aligned-top .cis-media-text__content,
.cis-media-text.is-vertically-aligned-top .cis-media-text__media {
    align-self: start
}

.cis-media-text .cis-media-text__content,
.cis-media-text .cis-media-text__media,
.cis-media-text.is-vertically-aligned-center .cis-media-text__content,
.cis-media-text.is-vertically-aligned-center .cis-media-text__media {
    align-self: center
}

.cis-media-text.is-vertically-aligned-bottom .cis-media-text__content,
.cis-media-text.is-vertically-aligned-bottom .cis-media-text__media {
    align-self: end
}

.cis-media-text .cis-media-text__media {
    grid-column: 1;
    grid-row: 1;
    margin: 0
}

.cis-media-text .cis-media-text__content {
    direction: ltr;
    grid-column: 2;
    grid-row: 1;
    padding: 0 8%;
    word-break: break-word
}

.cis-media-text.has-media-on-the-right .cis-media-text__media {
    grid-column: 2;
    grid-row: 1
}

.cis-media-text.has-media-on-the-right .cis-media-text__content {
    grid-column: 1;
    grid-row: 1
}

.cis-media-text__media img,
.cis-media-text__media video {
    height: auto;
    max-width: unset;
    vertical-align: middle;
    width: 100%
}

.cis-media-text.is-image-fill .cis-media-text__media {
    background-size: cover;
    height: 100%;
    min-height: 250px
}

.cis-media-text.is-image-fill .cis-media-text__media>a {
    display: block;
    height: 100%
}

.cis-media-text.is-image-fill .cis-media-text__media img {
    clip: rect(0, 0, 0, 0);
    border: 0;
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px
}

@media (max-width:600px) {
    .cis-media-text.is-stacked-on-mobile {
        grid-template-columns: 100% !important
    }

    .cis-media-text.is-stacked-on-mobile .cis-media-text__media {
        grid-column: 1;
        grid-row: 1
    }

    .cis-media-text.is-stacked-on-mobile .cis-media-text__content {
        grid-column: 1;
        grid-row: 2
    }
}

.cis-navigation {
    --navigation-layout-justification-setting: flex-start;
    --navigation-layout-direction: row;
    --navigation-layout-wrap: wrap;
    --navigation-layout-justify: flex-start;
    --navigation-layout-align: center;
    position: relative
}

.cis-navigation ul {
    margin-bottom: 0;
    margin-left: 0;
    margin-top: 0;
    padding-left: 0
}

.cis-navigation ul,
.cis-navigation ul li {
    list-style: none;
    padding: 0
}

.cis-navigation .cis-navigation-item {
    align-items: center;
    display: flex;
    position: relative
}

.cis-navigation .cis-navigation-item .cis-navigation__submenu-container:empty {
    display: none
}

.cis-navigation .cis-navigation-item__content {
    display: block
}

.cis-navigation .cis-navigation-item__content.cis-navigation-item__content {
    color: inherit
}

.cis-navigation.has-text-decoration-underline .cis-navigation-item__content,
.cis-navigation.has-text-decoration-underline .cis-navigation-item__content:active,
.cis-navigation.has-text-decoration-underline .cis-navigation-item__content:focus {
    text-decoration: underline
}

.cis-navigation.has-text-decoration-line-through .cis-navigation-item__content,
.cis-navigation.has-text-decoration-line-through .cis-navigation-item__content:active,
.cis-navigation.has-text-decoration-line-through .cis-navigation-item__content:focus {
    text-decoration: line-through
}

.cis-navigation:where(:not([class*=has-text-decoration])) a {
    text-decoration: none
}

.cis-navigation:where(:not([class*=has-text-decoration])) a:active,
.cis-navigation:where(:not([class*=has-text-decoration])) a:focus {
    text-decoration: none
}

.cis-navigation .cis-navigation__submenu-icon {
    align-self: center;
    background-color: inherit;
    border: none;
    color: currentColor;
    display: inline-block;
    font-size: inherit;
    height: .6em;
    line-height: 0;
    margin-left: .25em;
    padding: 0;
    width: .6em
}

.cis-navigation .cis-navigation__submenu-icon svg {
    stroke: currentColor;
    display: inline-block;
    height: inherit;
    margin-top: .075em;
    width: inherit
}

.cis-navigation.is-vertical {
    --navigation-layout-direction: column;
    --navigation-layout-justify: initial;
    --navigation-layout-align: flex-start
}

.cis-navigation.no-wrap {
    --navigation-layout-wrap: nowrap
}

.cis-navigation.items-justified-center {
    --navigation-layout-justification-setting: center;
    --navigation-layout-justify: center
}

.cis-navigation.items-justified-center.is-vertical {
    --navigation-layout-align: center
}

.cis-navigation.items-justified-right {
    --navigation-layout-justification-setting: flex-end;
    --navigation-layout-justify: flex-end
}

.cis-navigation.items-justified-right.is-vertical {
    --navigation-layout-align: flex-end
}

.cis-navigation.items-justified-space-between {
    --navigation-layout-justification-setting: space-between;
    --navigation-layout-justify: space-between
}

.cis-navigation .has-child .cis-navigation__submenu-container {
    align-items: normal;
    background-color: inherit;
    color: inherit;
    display: flex;
    flex-direction: column;
    height: 0;
    left: -1px;
    opacity: 0;
    overflow: hidden;
    position: absolute;
    top: 100%;
    transition: opacity .1s linear;
    visibility: hidden;
    width: 0;
    z-index: 2
}

.cis-navigation .has-child .cis-navigation__submenu-container>.cis-navigation-item>.cis-navigation-item__content {
    display: flex;
    flex-grow: 1
}

.cis-navigation .has-child .cis-navigation__submenu-container>.cis-navigation-item>.cis-navigation-item__content .cis-navigation__submenu-icon {
    margin-left: auto;
    margin-right: 0
}

.cis-navigation .has-child .cis-navigation__submenu-container .cis-navigation-item__content {
    margin: 0
}

@media (min-width:782px) {
    .cis-navigation .has-child .cis-navigation__submenu-container .cis-navigation__submenu-container {
        left: 100%;
        top: -1px
    }

    .cis-navigation .has-child .cis-navigation__submenu-container .cis-navigation__submenu-container:before {
        background: 0 0;
        content: "";
        display: block;
        height: 100%;
        position: absolute;
        right: 100%;
        width: .5em
    }

    .cis-navigation .has-child .cis-navigation__submenu-container .cis-navigation__submenu-icon {
        margin-right: .25em
    }

    .cis-navigation .has-child .cis-navigation__submenu-container .cis-navigation__submenu-icon svg {
        transform: rotate(-90deg)
    }
}

.cis-navigation .has-child:not(.open-on-click):hover>.cis-navigation__submenu-container {
    height: auto;
    min-width: 200px;
    opacity: 1;
    overflow: visible;
    visibility: visible;
    width: auto
}

.cis-navigation .has-child:not(.open-on-click):not(.open-on-hover-click):focus-within>.cis-navigation__submenu-container {
    height: auto;
    min-width: 200px;
    opacity: 1;
    overflow: visible;
    visibility: visible;
    width: auto
}

.cis-navigation .has-child .cis-navigation-submenu__toggle[aria-expanded=true]~.cis-navigation__submenu-container {
    height: auto;
    min-width: 200px;
    opacity: 1;
    overflow: visible;
    visibility: visible;
    width: auto
}

.cis-navigation.has-background .has-child .cis-navigation__submenu-container {
    left: 0;
    top: 100%
}

@media (min-width:782px) {
    .cis-navigation.has-background .has-child .cis-navigation__submenu-container .cis-navigation__submenu-container {
        left: 100%;
        top: 0
    }
}

.cis-navigation-submenu {
    display: flex;
    position: relative
}

.cis-navigation-submenu .cis-navigation__submenu-icon svg {
    stroke: currentColor
}

button.cis-navigation-item__content {
    background-color: transparent;
    border: none;
    color: currentColor;
    font-family: inherit;
    font-size: inherit;
    font-style: inherit;
    font-weight: inherit;
    line-height: inherit;
    text-align: left;
    text-transform: inherit
}

.cis-navigation-submenu__toggle {
    cursor: pointer
}

.cis-navigation-item.open-on-click .cis-navigation-submenu__toggle {
    padding-right: .85em
}

.cis-navigation-item.open-on-click .cis-navigation-submenu__toggle+.cis-navigation__submenu-icon {
    margin-left: -.6em;
    pointer-events: none
}

.cis-navigation .cis-page-list,
.cis-navigation__container,
.cis-navigation__responsive-close,
.cis-navigation__responsive-container,
.cis-navigation__responsive-container-content,
.cis-navigation__responsive-dialog {
    gap: inherit
}

:where(.cis-navigation.has-background .cis-navigation-item a:not(.cis-element-button)),
:where(.cis-navigation.has-background .cis-navigation-submenu a:not(.cis-element-button)) {
    padding: .5em 1em
}

:where(.cis-navigation .cis-navigation__submenu-container .cis-navigation-item a:not(.cis-element-button)),
:where(.cis-navigation .cis-navigation__submenu-container .cis-navigation-submenu a:not(.cis-element-button)),
:where(.cis-navigation .cis-navigation__submenu-container .cis-navigation-submenu button.cis-navigation-item__content),
:where(.cis-navigation .cis-navigation__submenu-container .cis-pages-list__item button.cis-navigation-item__content) {
    padding: .5em 1em
}

.cis-navigation.items-justified-right .cis-navigation__container .has-child .cis-navigation__submenu-container,
.cis-navigation.items-justified-right .cis-page-list>.has-child .cis-navigation__submenu-container,
.cis-navigation.items-justified-space-between .cis-page-list>.has-child:last-child .cis-navigation__submenu-container,
.cis-navigation.items-justified-space-between>.cis-navigation__container>.has-child:last-child .cis-navigation__submenu-container {
    left: auto;
    right: 0
}

.cis-navigation.items-justified-right .cis-navigation__container .has-child .cis-navigation__submenu-container .cis-navigation__submenu-container,
.cis-navigation.items-justified-right .cis-page-list>.has-child .cis-navigation__submenu-container .cis-navigation__submenu-container,
.cis-navigation.items-justified-space-between .cis-page-list>.has-child:last-child .cis-navigation__submenu-container .cis-navigation__submenu-container,
.cis-navigation.items-justified-space-between>.cis-navigation__container>.has-child:last-child .cis-navigation__submenu-container .cis-navigation__submenu-container {
    left: -1px;
    right: -1px
}

@media (min-width:782px) {

    .cis-navigation.items-justified-right .cis-navigation__container .has-child .cis-navigation__submenu-container .cis-navigation__submenu-container,
    .cis-navigation.items-justified-right .cis-page-list>.has-child .cis-navigation__submenu-container .cis-navigation__submenu-container,
    .cis-navigation.items-justified-space-between .cis-page-list>.has-child:last-child .cis-navigation__submenu-container .cis-navigation__submenu-container,
    .cis-navigation.items-justified-space-between>.cis-navigation__container>.has-child:last-child .cis-navigation__submenu-container .cis-navigation__submenu-container {
        left: auto;
        right: 100%
    }
}

.cis-navigation:not(.has-background) .cis-navigation__submenu-container {
    background-color: #fff;
    border: 1px solid rgba(0, 0, 0, .15)
}

.cis-navigation.has-background .cis-navigation__submenu-container {
    background-color: inherit
}

.cis-navigation:not(.has-text-color) .cis-navigation__submenu-container {
    color: #000
}

.cis-navigation__container {
    align-items: var(--navigation-layout-align, initial);
    display: flex;
    flex-direction: var(--navigation-layout-direction, initial);
    flex-wrap: var(--navigation-layout-wrap, wrap);
    justify-content: var(--navigation-layout-justify, initial);
    list-style: none;
    margin: 0;
    padding-left: 0
}

.cis-navigation__container .is-responsive {
    display: none
}

.cis-navigation__container:only-child,
.cis-page-list:only-child {
    flex-grow: 1
}

@keyframes overlay-menu__fade-in-animation {
    0% {
        opacity: 0;
        transform: translateY(.5em)
    }

    to {
        opacity: 1;
        transform: translateY(0)
    }
}

.cis-navigation__responsive-container {
    bottom: 0;
    display: none;
    left: 0;
    position: fixed;
    right: 0;
    top: 0
}

.cis-navigation__responsive-container :where(.cis-navigation-item a) {
    color: inherit
}

.cis-navigation__responsive-container .cis-navigation__responsive-container-content {
    align-items: var(--navigation-layout-align, initial);
    display: flex;
    flex-direction: var(--navigation-layout-direction, initial);
    flex-wrap: var(--navigation-layout-wrap, wrap);
    justify-content: var(--navigation-layout-justify, initial)
}

.cis-navigation__responsive-container:not(.is-menu-open.is-menu-open) {
    background-color: inherit !important;
    color: inherit !important
}

.cis-navigation__responsive-container.is-menu-open {
    animation: overlay-menu__fade-in-animation .1s ease-out;
    animation-fill-mode: forwards;
    background-color: inherit;
    display: flex;
    flex-direction: column;
    overflow: auto;
    padding: clamp(1rem, var(--cis--style--root--padding-top), 20rem) clamp(1rem, var(--cis--style--root--padding-right), 20rem) clamp(1rem, var(--cis--style--root--padding-bottom), 20rem) clamp(1rem, var(--cis--style--root--padding-left), 20em);
    z-index: 100000
}

@media (prefers-reduced-motion:reduce) {
    .cis-navigation__responsive-container.is-menu-open {
        animation-delay: 0s;
        animation-duration: 1ms
    }
}

.cis-navigation__responsive-container.is-menu-open .cis-navigation__responsive-container-content {
    align-items: var(--navigation-layout-justification-setting, inherit);
    display: flex;
    flex-direction: column;
    flex-wrap: nowrap;
    overflow: visible;
    padding-top: calc(2rem + 24px)
}

.cis-navigation__responsive-container.is-menu-open .cis-navigation__responsive-container-content,
.cis-navigation__responsive-container.is-menu-open .cis-navigation__responsive-container-content .cis-navigation__container,
.cis-navigation__responsive-container.is-menu-open .cis-navigation__responsive-container-content .cis-page-list {
    justify-content: flex-start
}

.cis-navigation__responsive-container.is-menu-open .cis-navigation__responsive-container-content .cis-navigation__submenu-icon {
    display: none
}

.cis-navigation__responsive-container.is-menu-open .cis-navigation__responsive-container-content .has-child .cis-navigation__submenu-container {
    border: none;
    height: auto;
    min-width: 200px;
    opacity: 1;
    overflow: initial;
    padding-left: 2rem;
    padding-right: 2rem;
    position: static;
    visibility: visible;
    width: auto
}

.cis-navigation__responsive-container.is-menu-open .cis-navigation__responsive-container-content .cis-navigation__container,
.cis-navigation__responsive-container.is-menu-open .cis-navigation__responsive-container-content .cis-navigation__submenu-container {
    gap: inherit
}

.cis-navigation__responsive-container.is-menu-open .cis-navigation__responsive-container-content .cis-navigation__submenu-container {
    padding-top: var(--cis--style--block-gap, 2em)
}

.cis-navigation__responsive-container.is-menu-open .cis-navigation__responsive-container-content .cis-navigation-item__content {
    padding: 0
}

.cis-navigation__responsive-container.is-menu-open .cis-navigation__responsive-container-content .cis-navigation-item,
.cis-navigation__responsive-container.is-menu-open .cis-navigation__responsive-container-content .cis-navigation__container,
.cis-navigation__responsive-container.is-menu-open .cis-navigation__responsive-container-content .cis-page-list {
    align-items: var(--navigation-layout-justification-setting, initial);
    display: flex;
    flex-direction: column
}

.cis-navigation__responsive-container.is-menu-open .cis-navigation-item,
.cis-navigation__responsive-container.is-menu-open .cis-navigation-item .cis-navigation__submenu-container,
.cis-navigation__responsive-container.is-menu-open .cis-navigation__container,
.cis-navigation__responsive-container.is-menu-open .cis-page-list {
    background: transparent !important;
    color: inherit !important
}

.cis-navigation__responsive-container.is-menu-open .cis-navigation__submenu-container.cis-navigation__submenu-container.cis-navigation__submenu-container.cis-navigation__submenu-container {
    left: auto;
    right: auto
}

@media (min-width:600px) {
    .cis-navigation__responsive-container:not(.hidden-by-default):not(.is-menu-open) {
        background-color: inherit;
        display: block;
        position: relative;
        width: 100%;
        z-index: auto
    }

    .cis-navigation__responsive-container:not(.hidden-by-default):not(.is-menu-open) .cis-navigation__responsive-container-close {
        display: none
    }

    .cis-navigation__responsive-container.is-menu-open .cis-navigation__submenu-container.cis-navigation__submenu-container.cis-navigation__submenu-container.cis-navigation__submenu-container {
        left: 0
    }
}

.cis-navigation:not(.has-background) .cis-navigation__responsive-container.is-menu-open {
    background-color: #fff
}

.cis-navigation:not(.has-text-color) .cis-navigation__responsive-container.is-menu-open {
    color: #000
}

.cis-navigation__toggle_button_label {
    font-size: 1rem;
    font-weight: 700
}

.cis-navigation__responsive-container-close,
.cis-navigation__responsive-container-open {
    background: 0 0;
    border: none;
    color: currentColor;
    cursor: pointer;
    margin: 0;
    padding: 0;
    text-transform: inherit;
    vertical-align: middle
}

.cis-navigation__responsive-container-close svg,
.cis-navigation__responsive-container-open svg {
    fill: currentColor;
    display: block;
    height: 24px;
    pointer-events: none;
    width: 24px
}

.cis-navigation__responsive-container-open {
    display: flex
}

.cis-navigation__responsive-container-open.cis-navigation__responsive-container-open.cis-navigation__responsive-container-open {
    font-family: inherit;
    font-size: inherit;
    font-weight: inherit
}

@media (min-width:600px) {
    .cis-navigation__responsive-container-open:not(.always-shown) {
        display: none
    }
}

.cis-navigation__responsive-container-close {
    position: absolute;
    right: 0;
    top: 0;
    z-index: 2
}

.cis-navigation__responsive-container-close.cis-navigation__responsive-container-close.cis-navigation__responsive-container-close {
    font-family: inherit;
    font-size: inherit;
    font-weight: inherit
}

.cis-navigation__responsive-close {
    width: 100%
}

.has-modal-open .cis-navigation__responsive-close {
    margin-left: auto;
    margin-right: auto;
    max-width: var(--cis--style--global--wide-size, 100%)
}

.cis-navigation__responsive-close:focus {
    outline: none
}

.is-menu-open .cis-navigation__responsive-close,
.is-menu-open .cis-navigation__responsive-container-content,
.is-menu-open .cis-navigation__responsive-dialog {
    box-sizing: border-box
}

.cis-navigation__responsive-dialog {
    position: relative
}

.has-modal-open .admin-bar .is-menu-open .cis-navigation__responsive-dialog {
    margin-top: 46px
}

@media (min-width:782px) {
    .has-modal-open .admin-bar .is-menu-open .cis-navigation__responsive-dialog {
        margin-top: 32px
    }
}

html.has-modal-open {
    overflow: hidden
}

.cis-navigation .cis-navigation-item__label {
    overflow-wrap: break-word
}

.cis-navigation .cis-navigation-item__description {
    display: none
}

.cis-navigation .cis-page-list {
    align-items: var(--navigation-layout-align, initial);
    background-color: inherit;
    display: flex;
    flex-direction: var(--navigation-layout-direction, initial);
    flex-wrap: var(--navigation-layout-wrap, wrap);
    justify-content: var(--navigation-layout-justify, initial)
}

.cis-navigation .cis-navigation-item {
    background-color: inherit
}

.is-small-text {
    font-size: .875em
}

.is-regular-text {
    font-size: 1em
}

.is-large-text {
    font-size: 2.25em
}

.is-larger-text {
    font-size: 3em
}

.has-drop-cap:not(:focus):first-letter {
    float: left;
    font-size: 8.4em;
    font-style: normal;
    font-weight: 100;
    line-height: .68;
    margin: .05em .1em 0 0;
    text-transform: uppercase
}

body.rtl .has-drop-cap:not(:focus):first-letter {
    float: none;
    margin-left: .1em
}

p.has-drop-cap.has-background {
    overflow: hidden
}

p.has-background {
    padding: 1.25em 2.375em
}

:where(p.has-text-color:not(.has-link-color)) a {
    color: inherit
}

p.has-text-align-left[style*="writing-mode:vertical-lr"],
p.has-text-align-right[style*="writing-mode:vertical-rl"] {
    rotate: 180deg
}

.cis-post-author {
    display: flex;
    flex-wrap: wrap
}

.cis-post-author__byline {
    font-size: .5em;
    margin-bottom: 0;
    margin-top: 0;
    width: 100%
}

.cis-post-author__avatar {
    margin-right: 1em
}

.cis-post-author__bio {
    font-size: .7em;
    margin-bottom: .7em
}

.cis-post-author__content {
    flex-basis: 0;
    flex-grow: 1
}

.cis-post-author__name {
    margin: 0
}

.cis-post-comments-form {
    box-sizing: border-box
}

.cis-post-comments-form[style*=font-weight] :where(.comment-reply-title) {
    font-weight: inherit
}

.cis-post-comments-form[style*=font-family] :where(.comment-reply-title) {
    font-family: inherit
}

.cis-post-comments-form[class*=-font-size] :where(.comment-reply-title),
.cis-post-comments-form[style*=font-size] :where(.comment-reply-title) {
    font-size: inherit
}

.cis-post-comments-form[style*=line-height] :where(.comment-reply-title) {
    line-height: inherit
}

.cis-post-comments-form[style*=font-style] :where(.comment-reply-title) {
    font-style: inherit
}

.cis-post-comments-form[style*=letter-spacing] :where(.comment-reply-title) {
    letter-spacing: inherit
}

.cis-post-comments-form input[type=submit] {
    box-shadow: none;
    cursor: pointer;
    display: inline-block;
    overflow-wrap: break-word;
    text-align: center
}

.cis-post-comments-form input:not([type=submit]),
.cis-post-comments-form textarea {
    border: 1px solid #949494;
    font-family: inherit;
    font-size: 1em
}

.cis-post-comments-form input:not([type=submit]):not([type=checkbox]),
.cis-post-comments-form textarea {
    padding: calc(.667em + 2px)
}

.cis-post-comments-form .comment-form input:not([type=submit]):not([type=checkbox]):not([type=hidden]),
.cis-post-comments-form .comment-form textarea {
    box-sizing: border-box;
    display: block;
    width: 100%
}

.cis-post-comments-form .comment-form-author label,
.cis-post-comments-form .comment-form-email label,
.cis-post-comments-form .comment-form-url label {
    display: block;
    margin-bottom: .25em
}

.cis-post-comments-form .comment-form-cookies-consent {
    display: flex;
    gap: .25em
}

.cis-post-comments-form .comment-form-cookies-consent #cis-comment-cookies-consent {
    margin-top: .35em
}

.cis-post-comments-form .comment-reply-title {
    margin-bottom: 0
}

.cis-post-comments-form .comment-reply-title :where(small) {
    font-size: var(--cis--preset--font-size--medium, smaller);
    margin-left: .5em
}

.cis-post-date {
    box-sizing: border-box
}

:where(.cis-post-excerpt) {
    margin-bottom: var(--cis--style--block-gap);
    margin-top: var(--cis--style--block-gap)
}

.cis-post-excerpt__excerpt {
    margin-bottom: 0;
    margin-top: 0
}

.cis-post-excerpt__more-text {
    margin-bottom: 0;
    margin-top: var(--cis--style--block-gap)
}

.cis-post-excerpt__more-link {
    display: inline-block
}

.cis-post-featured-image {
    margin-left: 0;
    margin-right: 0
}

.cis-post-featured-image a {
    display: block;
    height: 100%
}

.cis-post-featured-image img {
    box-sizing: border-box;
    height: auto;
    max-width: 100%;
    vertical-align: bottom;
    width: 100%
}

.cis-post-featured-image.alignfull img,
.cis-post-featured-image.alignwide img {
    width: 100%
}

.cis-post-featured-image .cis-post-featured-image__overlay.has-background-dim {
    background-color: #000;
    inset: 0;
    position: absolute
}

.cis-post-featured-image {
    position: relative
}

.cis-post-featured-image .cis-post-featured-image__overlay.has-background-gradient {
    background-color: transparent
}

.cis-post-featured-image .cis-post-featured-image__overlay.has-background-dim-0 {
    opacity: 0
}

.cis-post-featured-image .cis-post-featured-image__overlay.has-background-dim-10 {
    opacity: .1
}

.cis-post-featured-image .cis-post-featured-image__overlay.has-background-dim-20 {
    opacity: .2
}

.cis-post-featured-image .cis-post-featured-image__overlay.has-background-dim-30 {
    opacity: .3
}

.cis-post-featured-image .cis-post-featured-image__overlay.has-background-dim-40 {
    opacity: .4
}

.cis-post-featured-image .cis-post-featured-image__overlay.has-background-dim-50 {
    opacity: .5
}

.cis-post-featured-image .cis-post-featured-image__overlay.has-background-dim-60 {
    opacity: .6
}

.cis-post-featured-image .cis-post-featured-image__overlay.has-background-dim-70 {
    opacity: .7
}

.cis-post-featured-image .cis-post-featured-image__overlay.has-background-dim-80 {
    opacity: .8
}

.cis-post-featured-image .cis-post-featured-image__overlay.has-background-dim-90 {
    opacity: .9
}

.cis-post-featured-image .cis-post-featured-image__overlay.has-background-dim-100 {
    opacity: 1
}

.cis-post-featured-image:where(.alignleft, .alignright) {
    width: 100%
}

.cis-post-navigation-link .cis-post-navigation-link__arrow-previous {
    display: inline-block;
    margin-right: 1ch
}

.cis-post-navigation-link .cis-post-navigation-link__arrow-previous:not(.is-arrow-chevron) {
    transform: scaleX(1)
}

.cis-post-navigation-link .cis-post-navigation-link__arrow-next {
    display: inline-block;
    margin-left: 1ch
}

.cis-post-navigation-link .cis-post-navigation-link__arrow-next:not(.is-arrow-chevron) {
    transform: scaleX(1)
}

.cis-post-navigation-link.has-text-align-left[style*="writing-mode: vertical-lr"],
.cis-post-navigation-link.has-text-align-right[style*="writing-mode: vertical-rl"] {
    rotate: 180deg
}

.cis-post-terms {
    box-sizing: border-box
}

.cis-post-terms .cis-post-terms__separator {
    white-space: pre-wrap
}

.cis-post-time-to-read,
.cis-post-title {
    box-sizing: border-box
}

.cis-post-title {
    word-break: break-word
}

.cis-post-title a {
    display: inline-block
}

.cis-preformatted {
    box-sizing: border-box;
    white-space: pre-wrap
}

:where(.cis-preformatted.has-background) {
    padding: 1.25em 2.375em
}

.cis-pullquote {
    box-sizing: border-box;
    overflow-wrap: break-word;
    padding: 3em 0;
    text-align: center
}

.cis-pullquote blockquote,
.cis-pullquote cite,
.cis-pullquote p {
    color: inherit
}

.cis-pullquote.alignleft,
.cis-pullquote.alignright {
    max-width: 420px
}

.cis-pullquote cite,
.cis-pullquote footer {
    position: relative
}

.cis-pullquote .has-text-color a {
    color: inherit
}

:where(.cis-pullquote) {
    margin: 0 0 1em
}

.cis-pullquote.has-text-align-left blockquote {
    text-align: left
}

.cis-pullquote.has-text-align-right blockquote {
    text-align: right
}

.cis-pullquote.is-style-solid-color {
    border: none
}

.cis-pullquote.is-style-solid-color blockquote {
    margin-left: auto;
    margin-right: auto;
    max-width: 60%
}

.cis-pullquote.is-style-solid-color blockquote p {
    font-size: 2em;
    margin-bottom: 0;
    margin-top: 0
}

.cis-pullquote.is-style-solid-color blockquote cite {
    font-style: normal;
    text-transform: none
}

.cis-pullquote cite {
    color: inherit
}

.cis-post-template {
    list-style: none;
    margin-bottom: 0;
    margin-top: 0;
    max-width: 100%;
    padding: 0
}

.cis-post-template.cis-post-template {
    background: 0 0
}

.cis-post-template.is-flex-container {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    gap: 1.25em
}

.cis-post-template.is-flex-container>li {
    margin: 0;
    width: 100%
}

@media (min-width:600px) {
    .cis-post-template.is-flex-container.is-flex-container.columns-2>li {
        width: calc(50% - .625em)
    }

    .cis-post-template.is-flex-container.is-flex-container.columns-3>li {
        width: calc(33.33333% - .83333em)
    }

    .cis-post-template.is-flex-container.is-flex-container.columns-4>li {
        width: calc(25% - .9375em)
    }

    .cis-post-template.is-flex-container.is-flex-container.columns-5>li {
        width: calc(20% - 1em)
    }

    .cis-post-template.is-flex-container.is-flex-container.columns-6>li {
        width: calc(16.66667% - 1.04167em)
    }
}

@media (max-width:600px) {
    .cis-post-template-is-layout-grid.cis-post-template-is-layout-grid.cis-post-template-is-layout-grid.cis-post-template-is-layout-grid {
        grid-template-columns: 1fr
    }
}

.cis-post-template-is-layout-constrained>li>.alignright,
.cis-post-template-is-layout-flow>li>.alignright {
    -webkit-margin-start: 2em;
    -webkit-margin-end: 0;
    float: right;
    margin-inline-end: 0;
    margin-inline-start: 2em
}

.cis-post-template-is-layout-constrained>li>.alignleft,
.cis-post-template-is-layout-flow>li>.alignleft {
    -webkit-margin-start: 0;
    -webkit-margin-end: 2em;
    float: left;
    margin-inline-end: 2em;
    margin-inline-start: 0
}

.cis-post-template-is-layout-constrained>li>.aligncenter,
.cis-post-template-is-layout-flow>li>.aligncenter {
    -webkit-margin-start: auto;
    -webkit-margin-end: auto;
    margin-inline-end: auto;
    margin-inline-start: auto
}

.cis-query-pagination>.cis-query-pagination-next,
.cis-query-pagination>.cis-query-pagination-numbers,
.cis-query-pagination>.cis-query-pagination-previous {
    margin-bottom: .5em;
    margin-right: .5em
}

.cis-query-pagination>.cis-query-pagination-next:last-child,
.cis-query-pagination>.cis-query-pagination-numbers:last-child,
.cis-query-pagination>.cis-query-pagination-previous:last-child {
    margin-right: 0
}

.cis-query-pagination.is-content-justification-space-between>.cis-query-pagination-next:last-of-type {
    -webkit-margin-start: auto;
    margin-inline-start: auto
}

.cis-query-pagination.is-content-justification-space-between>.cis-query-pagination-previous:first-child {
    -webkit-margin-end: auto;
    margin-inline-end: auto
}

.cis-query-pagination .cis-query-pagination-previous-arrow {
    display: inline-block;
    margin-right: 1ch
}

.cis-query-pagination .cis-query-pagination-previous-arrow:not(.is-arrow-chevron) {
    transform: scaleX(1)
}

.cis-query-pagination .cis-query-pagination-next-arrow {
    display: inline-block;
    margin-left: 1ch
}

.cis-query-pagination .cis-query-pagination-next-arrow:not(.is-arrow-chevron) {
    transform: scaleX(1)
}

.cis-query-pagination.aligncenter {
    justify-content: center
}

.cis-query-title,
.cis-quote {
    box-sizing: border-box
}

.cis-quote {
    overflow-wrap: break-word
}

.cis-quote.is-large:where(:not(.is-style-plain)),
.cis-quote.is-style-large:where(:not(.is-style-plain)) {
    margin-bottom: 1em;
    padding: 0 1em
}

.cis-quote.is-large:where(:not(.is-style-plain)) p,
.cis-quote.is-style-large:where(:not(.is-style-plain)) p {
    font-size: 1.5em;
    font-style: italic;
    line-height: 1.6
}

.cis-quote.is-large:where(:not(.is-style-plain)) cite,
.cis-quote.is-large:where(:not(.is-style-plain)) footer,
.cis-quote.is-style-large:where(:not(.is-style-plain)) cite,
.cis-quote.is-style-large:where(:not(.is-style-plain)) footer {
    font-size: 1.125em;
    text-align: right
}

.cis-read-more {
    display: block;
    width: -moz-fit-content;
    width: fit-content
}

.cis-read-more:not([style*=text-decoration]),
.cis-read-more:not([style*=text-decoration]):active,
.cis-read-more:not([style*=text-decoration]):focus {
    text-decoration: none
}

ul.cis-rss {
    list-style: none;
    padding: 0
}

ul.cis-rss.cis-rss {
    box-sizing: border-box
}

ul.cis-rss.alignleft {
    margin-right: 2em
}

ul.cis-rss.alignright {
    margin-left: 2em
}

ul.cis-rss.is-grid {
    display: flex;
    flex-wrap: wrap;
    list-style: none;
    padding: 0
}

ul.cis-rss.is-grid li {
    margin: 0 1em 1em 0;
    width: 100%
}

@media (min-width:600px) {
    ul.cis-rss.columns-2 li {
        width: calc(50% - 1em)
    }

    ul.cis-rss.columns-3 li {
        width: calc(33.33333% - 1em)
    }

    ul.cis-rss.columns-4 li {
        width: calc(25% - 1em)
    }

    ul.cis-rss.columns-5 li {
        width: calc(20% - 1em)
    }

    ul.cis-rss.columns-6 li {
        width: calc(16.66667% - 1em)
    }
}

.cis-rss__item-author,
.cis-rss__item-publish-date {
    display: block;
    font-size: .8125em
}

.cis-search__button {
    margin-left: 10px;
    word-break: normal
}

.cis-search__button.has-icon {
    line-height: 0
}

.cis-search__button svg {
    fill: currentColor;
    min-height: 24px;
    min-width: 24px;
    vertical-align: text-bottom
}

:where(.cis-search__button) {
    border: 1px solid #ccc;
    padding: 6px 10px
}

.cis-search__inside-wrapper {
    display: flex;
    flex: auto;
    flex-wrap: nowrap;
    max-width: 100%
}

.cis-search__label {
    width: 100%
}

.cis-search__input {
    -webkit-appearance: initial;
    appearance: none;
    border: 1px solid #949494;
    flex-grow: 1;
    margin-left: 0;
    margin-right: 0;
    min-width: 3rem;
    padding: 8px;
    text-decoration: unset !important
}

.cis-search.cis-search__button-only .cis-search__button {
    flex-shrink: 0;
    margin-left: 0;
    max-width: calc(100% - 100px)
}

:where(.cis-search__button-inside .cis-search__inside-wrapper) {
    border: 1px solid #949494;
    box-sizing: border-box;
    padding: 4px
}

:where(.cis-search__button-inside .cis-search__inside-wrapper) .cis-search__input {
    border: none;
    border-radius: 0;
    padding: 0 4px
}

:where(.cis-search__button-inside .cis-search__inside-wrapper) .cis-search__input:focus {
    outline: none
}

:where(.cis-search__button-inside .cis-search__inside-wrapper) :where(.cis-search__button) {
    padding: 4px 8px
}

.cis-search.aligncenter .cis-search__inside-wrapper {
    margin: auto
}

.cis-search__button-behavior-expand .cis-search__inside-wrapper {
    min-width: 0 !important;
    transition-property: width
}

.cis-search__button-behavior-expand .cis-search__input {
    flex-basis: 100%;
    transition-duration: .3s
}

.cis-search__button-behavior-expand.cis-search__searchfield-hidden,
.cis-search__button-behavior-expand.cis-search__searchfield-hidden .cis-search__inside-wrapper {
    overflow: hidden
}

.cis-search__button-behavior-expand.cis-search__searchfield-hidden .cis-search__input {
    border-left-width: 0 !important;
    border-right-width: 0 !important;
    flex-basis: 0;
    flex-grow: 0;
    margin: 0;
    min-width: 0 !important;
    padding-left: 0 !important;
    padding-right: 0 !important;
    width: 0 !important
}

.cis-block[data-align=right] .cis-search__button-behavior-expand .cis-search__inside-wrapper {
    float: right
}

.cis-separator {
    border: 1px solid;
    border-left: none;
    border-right: none
}

.cis-separator.is-style-dots {
    background: none !important;
    border: none;
    height: auto;
    line-height: 1;
    text-align: center
}

.cis-separator.is-style-dots:before {
    color: currentColor;
    content: "···";
    font-family: serif;
    font-size: 1.5em;
    letter-spacing: 2em;
    padding-left: 2em
}

.cis-site-logo {
    box-sizing: border-box;
    line-height: 0
}

.cis-site-logo a {
    display: inline-block;
    line-height: 0
}

.cis-site-logo.is-default-size img {
    height: auto;
    width: 120px
}

.cis-site-logo img {
    height: auto;
    max-width: 100%
}

.cis-site-logo a,
.cis-site-logo img {
    border-radius: inherit
}

.cis-site-logo.aligncenter {
    margin-left: auto;
    margin-right: auto;
    text-align: center
}

.cis-site-logo.is-style-rounded {
    border-radius: 9999px
}

.cis-site-title a {
    color: inherit
}

.cis-social-links {
    background: 0 0;
    box-sizing: border-box;
    margin-left: 0;
    padding-left: 0;
    padding-right: 0;
    text-indent: 0
}

.cis-social-links .cis-social-link a,
.cis-social-links .cis-social-link a:hover {
    border-bottom: 0;
    box-shadow: none;
    text-decoration: none
}

.cis-social-links .cis-social-link a {
    padding: .25em
}

.cis-social-links .cis-social-link svg {
    height: 1em;
    width: 1em
}

.cis-social-links .cis-social-link span:not(.screen-reader-text) {
    font-size: .65em;
    margin-left: .5em;
    margin-right: .5em
}

.cis-social-links.has-small-icon-size {
    font-size: 16px
}

.cis-social-links,
.cis-social-links.has-normal-icon-size {
    font-size: 24px
}

.cis-social-links.has-large-icon-size {
    font-size: 36px
}

.cis-social-links.has-huge-icon-size {
    font-size: 48px
}

.cis-social-links.aligncenter {
    display: flex;
    justify-content: center
}

.cis-social-links.alignright {
    justify-content: flex-end
}

.cis-social-link {
    border-radius: 9999px;
    display: block;
    height: auto;
    transition: transform .1s ease
}

@media (prefers-reduced-motion:reduce) {
    .cis-social-link {
        transition-delay: 0s;
        transition-duration: 0s
    }
}

.cis-social-link a {
    align-items: center;
    display: flex;
    line-height: 0;
    transition: transform .1s ease
}

.cis-social-link:hover {
    transform: scale(1.1)
}

.cis-social-links .cis-social-link .cis-social-link-anchor,
.cis-social-links .cis-social-link .cis-social-link-anchor svg,
.cis-social-links .cis-social-link .cis-social-link-anchor:active,
.cis-social-links .cis-social-link .cis-social-link-anchor:hover,
.cis-social-links .cis-social-link .cis-social-link-anchor:visited {
    fill: currentColor;
    color: currentColor
}

.cis-social-links:not(.is-style-logos-only) .cis-social-link {
    background-color: #f0f0f0;
    color: #444
}

.cis-social-links:not(.is-style-logos-only) .cis-social-link-amazon {
    background-color: #f90;
    color: #fff
}

.cis-social-links:not(.is-style-logos-only) .cis-social-link-bandcamp {
    background-color: #1ea0c3;
    color: #fff
}

.cis-social-links:not(.is-style-logos-only) .cis-social-link-behance {
    background-color: #0757fe;
    color: #fff
}

.cis-social-links:not(.is-style-logos-only) .cis-social-link-codepen {
    background-color: #1e1f26;
    color: #fff
}

.cis-social-links:not(.is-style-logos-only) .cis-social-link-deviantart {
    background-color: #02e49b;
    color: #fff
}

.cis-social-links:not(.is-style-logos-only) .cis-social-link-dribbble {
    background-color: #e94c89;
    color: #fff
}

.cis-social-links:not(.is-style-logos-only) .cis-social-link-dropbox {
    background-color: #4280ff;
    color: #fff
}

.cis-social-links:not(.is-style-logos-only) .cis-social-link-etsy {
    background-color: #f45800;
    color: #fff
}

.cis-social-links:not(.is-style-logos-only) .cis-social-link-facebook {
    background-color: #1778f2;
    color: #fff
}

.cis-social-links:not(.is-style-logos-only) .cis-social-link-fivehundredpx {
    background-color: #000;
    color: #fff
}

.cis-social-links:not(.is-style-logos-only) .cis-social-link-flickr {
    background-color: #0461dd;
    color: #fff
}

.cis-social-links:not(.is-style-logos-only) .cis-social-link-foursquare {
    background-color: #e65678;
    color: #fff
}

.cis-social-links:not(.is-style-logos-only) .cis-social-link-github {
    background-color: #24292d;
    color: #fff
}

.cis-social-links:not(.is-style-logos-only) .cis-social-link-goodreads {
    background-color: #eceadd;
    color: #382110
}

.cis-social-links:not(.is-style-logos-only) .cis-social-link-google {
    background-color: #ea4434;
    color: #fff
}

.cis-social-links:not(.is-style-logos-only) .cis-social-link-instagram {
    background-color: #f00075;
    color: #fff
}

.cis-social-links:not(.is-style-logos-only) .cis-social-link-lastfm {
    background-color: #e21b24;
    color: #fff
}

.cis-social-links:not(.is-style-logos-only) .cis-social-link-linkedin {
    background-color: #0d66c2;
    color: #fff
}

.cis-social-links:not(.is-style-logos-only) .cis-social-link-mastodon {
    background-color: #3288d4;
    color: #fff
}

.cis-social-links:not(.is-style-logos-only) .cis-social-link-medium {
    background-color: #02ab6c;
    color: #fff
}

.cis-social-links:not(.is-style-logos-only) .cis-social-link-meetup {
    background-color: #f6405f;
    color: #fff
}

.cis-social-links:not(.is-style-logos-only) .cis-social-link-patreon {
    background-color: #ff424d;
    color: #fff
}

.cis-social-links:not(.is-style-logos-only) .cis-social-link-pinterest {
    background-color: #e60122;
    color: #fff
}

.cis-social-links:not(.is-style-logos-only) .cis-social-link-pocket {
    background-color: #ef4155;
    color: #fff
}

.cis-social-links:not(.is-style-logos-only) .cis-social-link-reddit {
    background-color: #ff4500;
    color: #fff
}

.cis-social-links:not(.is-style-logos-only) .cis-social-link-skype {
    background-color: #0478d7;
    color: #fff
}

.cis-social-links:not(.is-style-logos-only) .cis-social-link-snapchat {
    stroke: #000;
    background-color: #fefc00;
    color: #fff
}

.cis-social-links:not(.is-style-logos-only) .cis-social-link-soundcloud {
    background-color: #ff5600;
    color: #fff
}

.cis-social-links:not(.is-style-logos-only) .cis-social-link-spotify {
    background-color: #1bd760;
    color: #fff
}

.cis-social-links:not(.is-style-logos-only) .cis-social-link-telegram {
    background-color: #2aabee;
    color: #fff
}

.cis-social-links:not(.is-style-logos-only) .cis-social-link-threads,
.cis-social-links:not(.is-style-logos-only) .cis-social-link-tiktok {
    background-color: #000;
    color: #fff
}

.cis-social-links:not(.is-style-logos-only) .cis-social-link-tumblr {
    background-color: #011835;
    color: #fff
}

.cis-social-links:not(.is-style-logos-only) .cis-social-link-twitch {
    background-color: #6440a4;
    color: #fff
}

.cis-social-links:not(.is-style-logos-only) .cis-social-link-twitter {
    background-color: #1da1f2;
    color: #fff
}

.cis-social-links:not(.is-style-logos-only) .cis-social-link-vimeo {
    background-color: #1eb7ea;
    color: #fff
}

.cis-social-links:not(.is-style-logos-only) .cis-social-link-vk {
    background-color: #4680c2;
    color: #fff
}

.cis-social-links:not(.is-style-logos-only) .cis-social-link-wordpress {
    background-color: #3499cd;
    color: #fff
}

.cis-social-links:not(.is-style-logos-only) .cis-social-link-whatsapp {
    background-color: #25d366;
    color: #fff
}

.cis-social-links:not(.is-style-logos-only) .cis-social-link-x {
    background-color: #000;
    color: #fff
}

.cis-social-links:not(.is-style-logos-only) .cis-social-link-yelp {
    background-color: #d32422;
    color: #fff
}

.cis-social-links:not(.is-style-logos-only) .cis-social-link-youtube {
    background-color: red;
    color: #fff
}

.cis-social-links.is-style-logos-only .cis-social-link {
    background: 0 0
}

.cis-social-links.is-style-logos-only .cis-social-link a {
    padding: 0
}

.cis-social-links.is-style-logos-only .cis-social-link svg {
    height: 1.25em;
    width: 1.25em
}

.cis-social-links.is-style-logos-only .cis-social-link-amazon {
    color: #f90
}

.cis-social-links.is-style-logos-only .cis-social-link-bandcamp {
    color: #1ea0c3
}

.cis-social-links.is-style-logos-only .cis-social-link-behance {
    color: #0757fe
}

.cis-social-links.is-style-logos-only .cis-social-link-codepen {
    color: #1e1f26
}

.cis-social-links.is-style-logos-only .cis-social-link-deviantart {
    color: #02e49b
}

.cis-social-links.is-style-logos-only .cis-social-link-dribbble {
    color: #e94c89
}

.cis-social-links.is-style-logos-only .cis-social-link-dropbox {
    color: #4280ff
}

.cis-social-links.is-style-logos-only .cis-social-link-etsy {
    color: #f45800
}

.cis-social-links.is-style-logos-only .cis-social-link-facebook {
    color: #1778f2
}

.cis-social-links.is-style-logos-only .cis-social-link-fivehundredpx {
    color: #000
}

.cis-social-links.is-style-logos-only .cis-social-link-flickr {
    color: #0461dd
}

.cis-social-links.is-style-logos-only .cis-social-link-foursquare {
    color: #e65678
}

.cis-social-links.is-style-logos-only .cis-social-link-github {
    color: #24292d
}

.cis-social-links.is-style-logos-only .cis-social-link-goodreads {
    color: #382110
}

.cis-social-links.is-style-logos-only .cis-social-link-google {
    color: #ea4434
}

.cis-social-links.is-style-logos-only .cis-social-link-instagram {
    color: #f00075
}

.cis-social-links.is-style-logos-only .cis-social-link-lastfm {
    color: #e21b24
}

.cis-social-links.is-style-logos-only .cis-social-link-linkedin {
    color: #0d66c2
}

.cis-social-links.is-style-logos-only .cis-social-link-mastodon {
    color: #3288d4
}

.cis-social-links.is-style-logos-only .cis-social-link-medium {
    color: #02ab6c
}

.cis-social-links.is-style-logos-only .cis-social-link-meetup {
    color: #f6405f
}

.cis-social-links.is-style-logos-only .cis-social-link-patreon {
    color: #ff424d
}

.cis-social-links.is-style-logos-only .cis-social-link-pinterest {
    color: #e60122
}

.cis-social-links.is-style-logos-only .cis-social-link-pocket {
    color: #ef4155
}

.cis-social-links.is-style-logos-only .cis-social-link-reddit {
    color: #ff4500
}

.cis-social-links.is-style-logos-only .cis-social-link-skype {
    color: #0478d7
}

.cis-social-links.is-style-logos-only .cis-social-link-snapchat {
    stroke: #000;
    color: #fff
}

.cis-social-links.is-style-logos-only .cis-social-link-soundcloud {
    color: #ff5600
}

.cis-social-links.is-style-logos-only .cis-social-link-spotify {
    color: #1bd760
}

.cis-social-links.is-style-logos-only .cis-social-link-telegram {
    color: #2aabee
}

.cis-social-links.is-style-logos-only .cis-social-link-threads,
.cis-social-links.is-style-logos-only .cis-social-link-tiktok {
    color: #000
}

.cis-social-links.is-style-logos-only .cis-social-link-tumblr {
    color: #011835
}

.cis-social-links.is-style-logos-only .cis-social-link-twitch {
    color: #6440a4
}

.cis-social-links.is-style-logos-only .cis-social-link-twitter {
    color: #1da1f2
}

.cis-social-links.is-style-logos-only .cis-social-link-vimeo {
    color: #1eb7ea
}

.cis-social-links.is-style-logos-only .cis-social-link-vk {
    color: #4680c2
}

.cis-social-links.is-style-logos-only .cis-social-link-whatsapp {
    color: #25d366
}

.cis-social-links.is-style-logos-only .cis-social-link-wordpress {
    color: #3499cd
}

.cis-social-links.is-style-logos-only .cis-social-link-x {
    color: #000
}

.cis-social-links.is-style-logos-only .cis-social-link-yelp {
    color: #d32422
}

.cis-social-links.is-style-logos-only .cis-social-link-youtube {
    color: red
}

.cis-social-links.is-style-pill-shape .cis-social-link {
    width: auto
}

.cis-social-links.is-style-pill-shape .cis-social-link a {
    padding-left: .66667em;
    padding-right: .66667em
}

.cis-social-links:not(.has-icon-color):not(.has-icon-background-color) .cis-social-link-snapchat .cis-social-link-label {
    color: #000
}

.cis-spacer {
    clear: both
}

.cis-tag-cloud {
    box-sizing: border-box
}

.cis-tag-cloud.aligncenter {
    justify-content: center;
    text-align: center
}

.cis-tag-cloud.alignfull {
    padding-left: 1em;
    padding-right: 1em
}

.cis-tag-cloud a {
    display: inline-block;
    margin-right: 5px
}

.cis-tag-cloud span {
    display: inline-block;
    margin-left: 5px;
    text-decoration: none
}

.cis-tag-cloud.is-style-outline {
    display: flex;
    flex-wrap: wrap;
    gap: 1ch
}

.cis-tag-cloud.is-style-outline a {
    border: 1px solid;
    font-size: unset !important;
    margin-right: 0;
    padding: 1ch 2ch;
    text-decoration: none !important
}

.cis-table {
    overflow-x: auto
}

.cis-table table {
    border-collapse: collapse;
    width: 100%
}

.cis-table thead {
    border-bottom: 3px solid
}

.cis-table tfoot {
    border-top: 3px solid
}

.cis-table td,
.cis-table th {
    border: 1px solid;
    padding: .5em
}

.cis-table .has-fixed-layout {
    table-layout: fixed;
    width: 100%
}

.cis-table .has-fixed-layout td,
.cis-table .has-fixed-layout th {
    word-break: break-word
}

.cis-table.aligncenter,
.cis-table.alignleft,
.cis-table.alignright {
    display: table;
    width: auto
}

.cis-table.aligncenter td,
.cis-table.aligncenter th,
.cis-table.alignleft td,
.cis-table.alignleft th,
.cis-table.alignright td,
.cis-table.alignright th {
    word-break: break-word
}

.cis-table .has-subtle-light-gray-background-color {
    background-color: #f3f4f5
}

.cis-table .has-subtle-pale-green-background-color {
    background-color: #e9fbe5
}

.cis-table .has-subtle-pale-blue-background-color {
    background-color: #e7f5fe
}

.cis-table .has-subtle-pale-pink-background-color {
    background-color: #fcf0ef
}

.cis-table.is-style-stripes {
    background-color: transparent;
    border-bottom: 1px solid #f0f0f0;
    border-collapse: inherit;
    border-spacing: 0
}

.cis-table.is-style-stripes tbody tr:nth-child(odd) {
    background-color: #f0f0f0
}

.cis-table.is-style-stripes.has-subtle-light-gray-background-color tbody tr:nth-child(odd) {
    background-color: #f3f4f5
}

.cis-table.is-style-stripes.has-subtle-pale-green-background-color tbody tr:nth-child(odd) {
    background-color: #e9fbe5
}

.cis-table.is-style-stripes.has-subtle-pale-blue-background-color tbody tr:nth-child(odd) {
    background-color: #e7f5fe
}

.cis-table.is-style-stripes.has-subtle-pale-pink-background-color tbody tr:nth-child(odd) {
    background-color: #fcf0ef
}

.cis-table.is-style-stripes td,
.cis-table.is-style-stripes th {
    border-color: transparent
}

.cis-table .has-border-color td,
.cis-table .has-border-color th,
.cis-table .has-border-color tr,
.cis-table .has-border-color>* {
    border-color: inherit
}

.cis-table table[style*=border-top-color] tr:first-child,
.cis-table table[style*=border-top-color] tr:first-child td,
.cis-table table[style*=border-top-color] tr:first-child th,
.cis-table table[style*=border-top-color]>*,
.cis-table table[style*=border-top-color]>* td,
.cis-table table[style*=border-top-color]>* th {
    border-top-color: inherit
}

.cis-table table[style*=border-top-color] tr:not(:first-child) {
    border-top-color: currentColor
}

.cis-table table[style*=border-right-color] td:last-child,
.cis-table table[style*=border-right-color] th,
.cis-table table[style*=border-right-color] tr,
.cis-table table[style*=border-right-color]>* {
    border-right-color: inherit
}

.cis-table table[style*=border-bottom-color] tr:last-child,
.cis-table table[style*=border-bottom-color] tr:last-child td,
.cis-table table[style*=border-bottom-color] tr:last-child th,
.cis-table table[style*=border-bottom-color]>*,
.cis-table table[style*=border-bottom-color]>* td,
.cis-table table[style*=border-bottom-color]>* th {
    border-bottom-color: inherit
}

.cis-table table[style*=border-bottom-color] tr:not(:last-child) {
    border-bottom-color: currentColor
}

.cis-table table[style*=border-left-color] td:first-child,
.cis-table table[style*=border-left-color] th,
.cis-table table[style*=border-left-color] tr,
.cis-table table[style*=border-left-color]>* {
    border-left-color: inherit
}

.cis-table table[style*=border-style] td,
.cis-table table[style*=border-style] th,
.cis-table table[style*=border-style] tr,
.cis-table table[style*=border-style]>* {
    border-style: inherit
}

.cis-table table[style*=border-width] td,
.cis-table table[style*=border-width] th,
.cis-table table[style*=border-width] tr,
.cis-table table[style*=border-width]>* {
    border-style: inherit;
    border-width: inherit
}

:where(.cis-term-description) {
    margin-bottom: var(--cis--style--block-gap);
    margin-top: var(--cis--style--block-gap)
}

.cis-term-description p {
    margin-bottom: 0;
    margin-top: 0
}

.cis-text-columns,
.cis-text-columns.aligncenter {
    display: flex
}

.cis-text-columns .cis-column {
    margin: 0 1em;
    padding: 0
}

.cis-text-columns .cis-column:first-child {
    margin-left: 0
}

.cis-text-columns .cis-column:last-child {
    margin-right: 0
}

.cis-text-columns.columns-2 .cis-column {
    width: 50%
}

.cis-text-columns.columns-3 .cis-column {
    width: 33.33333%
}

.cis-text-columns.columns-4 .cis-column {
    width: 25%
}

pre.cis-verse {
    overflow: auto;
    white-space: pre-wrap
}

:where(pre.cis-verse) {
    font-family: inherit
}

.cis-video {
    box-sizing: border-box
}

.cis-video video {
    vertical-align: middle;
    width: 100%
}

@supports (position:sticky) {
    .cis-video [poster] {
        object-fit: cover
    }
}

.cis-video.aligncenter {
    text-align: center
}

.cis-video figcaption {
    margin-bottom: 1em;
    margin-top: .5em
}

.editor-styles-wrapper,
.entry-content {
    counter-reset: footnotes
}

a[data-fn].fn {
    counter-increment: footnotes;
    display: inline-flex;
    font-size: smaller;
    text-decoration: none;
    text-indent: -9999999px;
    vertical-align: super
}

a[data-fn].fn:after {
    content: "[" counter(footnotes) "]";
    float: left;
    text-indent: 0
}

.cis-element-button {
    cursor: pointer
}

:root {
    --cis--preset--font-size--normal: 16px;
    --cis--preset--font-size--huge: 42px
}

:root .has-very-light-gray-background-color {
    background-color: #eee
}

:root .has-very-dark-gray-background-color {
    background-color: #313131
}

:root .has-very-light-gray-color {
    color: #eee
}

:root .has-very-dark-gray-color {
    color: #313131
}

:root .has-vivid-green-cyan-to-vivid-cyan-blue-gradient-background {
    background: linear-gradient(135deg, #00d084, #0693e3)
}

:root .has-purple-crush-gradient-background {
    background: linear-gradient(135deg, #34e2e4, #4721fb 50%, #ab1dfe)
}

:root .has-hazy-dawn-gradient-background {
    background: linear-gradient(135deg, #faaca8, #dad0ec)
}

:root .has-subdued-olive-gradient-background {
    background: linear-gradient(135deg, #fafae1, #67a671)
}

:root .has-atomic-cream-gradient-background {
    background: linear-gradient(135deg, #fdd79a, #004a59)
}

:root .has-nightshade-gradient-background {
    background: linear-gradient(135deg, #330968, #31cdcf)
}

:root .has-midnight-gradient-background {
    background: linear-gradient(135deg, #020381, #2874fc)
}

.has-regular-font-size {
    font-size: 1em
}

.has-larger-font-size {
    font-size: 2.625em
}

.has-normal-font-size {
    font-size: var(--cis--preset--font-size--normal)
}

.has-huge-font-size {
    font-size: var(--cis--preset--font-size--huge)
}

.has-text-align-center {
    text-align: center
}

.has-text-align-left {
    text-align: left
}

.has-text-align-right {
    text-align: right
}

#end-resizable-editor-section {
    display: none
}

.aligncenter {
    clear: both
}

.items-justified-left {
    justify-content: flex-start
}

.items-justified-center {
    justify-content: center
}

.items-justified-right {
    justify-content: flex-end
}

.items-justified-space-between {
    justify-content: space-between
}

.screen-reader-text {
    clip: rect(1px, 1px, 1px, 1px);
    word-wrap: normal !important;
    border: 0;
    -webkit-clip-path: inset(50%);
    clip-path: inset(50%);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px
}

.screen-reader-text:focus {
    clip: auto !important;
    background-color: #ddd;
    -webkit-clip-path: none;
    clip-path: none;
    color: #444;
    display: block;
    font-size: 1em;
    height: auto;
    left: 5px;
    line-height: normal;
    padding: 15px 23px 14px;
    text-decoration: none;
    top: 5px;
    width: auto;
    z-index: 100000
}

html :where(.has-border-color) {
    border-style: solid
}

html :where([style*=border-top-color]) {
    border-top-style: solid
}

html :where([style*=border-right-color]) {
    border-right-style: solid
}

html :where([style*=border-bottom-color]) {
    border-bottom-style: solid
}

html :where([style*=border-left-color]) {
    border-left-style: solid
}

html :where([style*=border-width]) {
    border-style: solid
}

html :where([style*=border-top-width]) {
    border-top-style: solid
}

html :where([style*=border-right-width]) {
    border-right-style: solid
}

html :where([style*=border-bottom-width]) {
    border-bottom-style: solid
}

html :where([style*=border-left-width]) {
    border-left-style: solid
}

html :where(img[class*=cis-image-]) {
    height: auto;
    max-width: 100%
}

:where(figure) {
    margin: 0 0 1em
}

html :where(.is-position-sticky) {
    --cis-admin--admin-bar--position-offset: var(--cis-admin--admin-bar--height, 0px)
}

@media screen and (max-width:600px) {
    html :where(.is-position-sticky) {
        --cis-admin--admin-bar--position-offset: 0px
    }
}

/*! This file is auto-generated */

.cis-button__link {
    color: #fff;
    background-color: #32373c;
    border-radius: 9999px;
    box-shadow: none;
    text-decoration: none;
    padding: calc(.667em + 2px) calc(1.333em + 2px);
    font-size: 1.125em
}

.cis-file__button {
    background: #32373c;
    color: #fff;
    text-decoration: none
}

body {
    --cis--preset--color--black: #000;
    --cis--preset--color--cyan-bluish-gray: #abb8c3;
    --cis--preset--color--white: #fff;
    --cis--preset--color--pale-pink: #f78da7;
    --cis--preset--color--vivid-red: #cf2e2e;
    --cis--preset--color--luminous-vivid-orange: #ff6900;
    --cis--preset--color--luminous-vivid-amber: #fcb900;
    --cis--preset--color--light-green-cyan: #7bdcb5;
    --cis--preset--color--vivid-green-cyan: #00d084;
    --cis--preset--color--pale-cyan-blue: #8ed1fc;
    --cis--preset--color--vivid-cyan-blue: #0693e3;
    --cis--preset--color--vivid-purple: #9b51e0;
    --cis--preset--gradient--vivid-cyan-blue-to-vivid-purple: linear-gradient(135deg, rgba(6, 147, 227, 1) 0%, #9b51e0 100%);
    --cis--preset--gradient--light-green-cyan-to-vivid-green-cyan: linear-gradient(135deg, #7adcb4 0%, #00d082 100%);
    --cis--preset--gradient--luminous-vivid-amber-to-luminous-vivid-orange: linear-gradient(135deg, rgba(252, 185, 0, 1) 0%, rgba(255, 105, 0, 1) 100%);
    --cis--preset--gradient--luminous-vivid-orange-to-vivid-red: linear-gradient(135deg, rgba(255, 105, 0, 1) 0%, #cf2e2e 100%);
    --cis--preset--gradient--very-light-gray-to-cyan-bluish-gray: linear-gradient(135deg, #eee 0%, #a9b8c3 100%);
    --cis--preset--gradient--cool-to-warm-spectrum: linear-gradient(135deg, #4aeadc 0%, #9778d1 20%, #cf2aba 40%, #ee2c82 60%, #fb6962 80%, #fef84c 100%);
    --cis--preset--gradient--blush-light-purple: linear-gradient(135deg, #ffceec 0%, #9896f0 100%);
    --cis--preset--gradient--blush-bordeaux: linear-gradient(135deg, #fecda5 0%, #fe2d2d 50%, #6b003e 100%);
    --cis--preset--gradient--luminous-dusk: linear-gradient(135deg, #ffcb70 0%, #c751c0 50%, #4158d0 100%);
    --cis--preset--gradient--pale-ocean: linear-gradient(135deg, #fff5cb 0%, #b6e3d4 50%, #33a7b5 100%);
    --cis--preset--gradient--electric-grass: linear-gradient(135deg, #caf880 0%, #71ce7e 100%);
    --cis--preset--gradient--midnight: linear-gradient(135deg, #020381 0%, #2874fc 100%);
    --cis--preset--font-size--small: 13px;
    --cis--preset--font-size--medium: 20px;
    --cis--preset--font-size--large: 36px;
    --cis--preset--font-size--x-large: 42px;
    --cis--preset--spacing--20: .44rem;
    --cis--preset--spacing--30: .67rem;
    --cis--preset--spacing--40: 1rem;
    --cis--preset--spacing--50: 1.5rem;
    --cis--preset--spacing--60: 2.25rem;
    --cis--preset--spacing--70: 3.38rem;
    --cis--preset--spacing--80: 5.06rem;
    --cis--preset--shadow--natural: 6px 6px 9px rgba(0, 0, 0, .2);
    --cis--preset--shadow--deep: 12px 12px 50px rgba(0, 0, 0, .4);
    --cis--preset--shadow--sharp: 6px 6px 0px rgba(0, 0, 0, .2);
    --cis--preset--shadow--outlined: 6px 6px 0px -3px rgba(255, 255, 255, 1), 6px 6px rgba(0, 0, 0, 1);
    --cis--preset--shadow--crisp: 6px 6px 0px rgba(0, 0, 0, 1)
}

:where(.is-layout-flex) {
    gap: .5em
}

:where(.is-layout-grid) {
    gap: .5em
}

body .is-layout-flow>.alignleft {
    float: left;
    margin-inline-start: 0;
    margin-inline-end: 2em
}

body .is-layout-flow>.alignright {
    float: right;
    margin-inline-start: 2em;
    margin-inline-end: 0
}

body .is-layout-flow>.aligncenter {
    margin-left: auto !important;
    margin-right: auto !important
}

body .is-layout-constrained>.alignleft {
    float: left;
    margin-inline-start: 0;
    margin-inline-end: 2em
}

body .is-layout-constrained>.alignright {
    float: right;
    margin-inline-start: 2em;
    margin-inline-end: 0
}

body .is-layout-constrained>.aligncenter {
    margin-left: auto !important;
    margin-right: auto !important
}

body .is-layout-constrained>:where(:not(.alignleft):not(.alignright):not(.alignfull)) {
    max-width: var(--cis--style--global--content-size);
    margin-left: auto !important;
    margin-right: auto !important
}

body .is-layout-constrained>.alignwide {
    max-width: var(--cis--style--global--wide-size)
}

body .is-layout-flex {
    display: flex
}

body .is-layout-flex {
    flex-wrap: wrap;
    align-items: center
}

body .is-layout-flex>* {
    margin: 0
}

body .is-layout-grid {
    display: grid
}

body .is-layout-grid>* {
    margin: 0
}

:where(.cis-columns.is-layout-flex) {
    gap: 2em
}

:where(.cis-columns.is-layout-grid) {
    gap: 2em
}

:where(.cis-post-template.is-layout-flex) {
    gap: 1.25em
}

:where(.cis-post-template.is-layout-grid) {
    gap: 1.25em
}

.has-black-color {
    color: var(--cis--preset--color--black) !important
}

.has-cyan-bluish-gray-color {
    color: var(--cis--preset--color--cyan-bluish-gray) !important
}

.has-white-color {
    color: var(--cis--preset--color--white) !important
}

.has-pale-pink-color {
    color: var(--cis--preset--color--pale-pink) !important
}

.has-vivid-red-color {
    color: var(--cis--preset--color--vivid-red) !important
}

.has-luminous-vivid-orange-color {
    color: var(--cis--preset--color--luminous-vivid-orange) !important
}

.has-luminous-vivid-amber-color {
    color: var(--cis--preset--color--luminous-vivid-amber) !important
}

.has-light-green-cyan-color {
    color: var(--cis--preset--color--light-green-cyan) !important
}

.has-vivid-green-cyan-color {
    color: var(--cis--preset--color--vivid-green-cyan) !important
}

.has-pale-cyan-blue-color {
    color: var(--cis--preset--color--pale-cyan-blue) !important
}

.has-vivid-cyan-blue-color {
    color: var(--cis--preset--color--vivid-cyan-blue) !important
}

.has-vivid-purple-color {
    color: var(--cis--preset--color--vivid-purple) !important
}

.has-black-background-color {
    background-color: var(--cis--preset--color--black) !important
}

.has-cyan-bluish-gray-background-color {
    background-color: var(--cis--preset--color--cyan-bluish-gray) !important
}

.has-white-background-color {
    background-color: var(--cis--preset--color--white) !important
}

.has-pale-pink-background-color {
    background-color: var(--cis--preset--color--pale-pink) !important
}

.has-vivid-red-background-color {
    background-color: var(--cis--preset--color--vivid-red) !important
}

.has-luminous-vivid-orange-background-color {
    background-color: var(--cis--preset--color--luminous-vivid-orange) !important
}

.has-luminous-vivid-amber-background-color {
    background-color: var(--cis--preset--color--luminous-vivid-amber) !important
}

.has-light-green-cyan-background-color {
    background-color: var(--cis--preset--color--light-green-cyan) !important
}

.has-vivid-green-cyan-background-color {
    background-color: var(--cis--preset--color--vivid-green-cyan) !important
}

.has-pale-cyan-blue-background-color {
    background-color: var(--cis--preset--color--pale-cyan-blue) !important
}

.has-vivid-cyan-blue-background-color {
    background-color: var(--cis--preset--color--vivid-cyan-blue) !important
}

.has-vivid-purple-background-color {
    background-color: var(--cis--preset--color--vivid-purple) !important
}

.has-black-border-color {
    border-color: var(--cis--preset--color--black) !important
}

.has-cyan-bluish-gray-border-color {
    border-color: var(--cis--preset--color--cyan-bluish-gray) !important
}

.has-white-border-color {
    border-color: var(--cis--preset--color--white) !important
}

.has-pale-pink-border-color {
    border-color: var(--cis--preset--color--pale-pink) !important
}

.has-vivid-red-border-color {
    border-color: var(--cis--preset--color--vivid-red) !important
}

.has-luminous-vivid-orange-border-color {
    border-color: var(--cis--preset--color--luminous-vivid-orange) !important
}

.has-luminous-vivid-amber-border-color {
    border-color: var(--cis--preset--color--luminous-vivid-amber) !important
}

.has-light-green-cyan-border-color {
    border-color: var(--cis--preset--color--light-green-cyan) !important
}

.has-vivid-green-cyan-border-color {
    border-color: var(--cis--preset--color--vivid-green-cyan) !important
}

.has-pale-cyan-blue-border-color {
    border-color: var(--cis--preset--color--pale-cyan-blue) !important
}

.has-vivid-cyan-blue-border-color {
    border-color: var(--cis--preset--color--vivid-cyan-blue) !important
}

.has-vivid-purple-border-color {
    border-color: var(--cis--preset--color--vivid-purple) !important
}

.has-vivid-cyan-blue-to-vivid-purple-gradient-background {
    background: var(--cis--preset--gradient--vivid-cyan-blue-to-vivid-purple) !important
}

.has-light-green-cyan-to-vivid-green-cyan-gradient-background {
    background: var(--cis--preset--gradient--light-green-cyan-to-vivid-green-cyan) !important
}

.has-luminous-vivid-amber-to-luminous-vivid-orange-gradient-background {
    background: var(--cis--preset--gradient--luminous-vivid-amber-to-luminous-vivid-orange) !important
}

.has-luminous-vivid-orange-to-vivid-red-gradient-background {
    background: var(--cis--preset--gradient--luminous-vivid-orange-to-vivid-red) !important
}

.has-very-light-gray-to-cyan-bluish-gray-gradient-background {
    background: var(--cis--preset--gradient--very-light-gray-to-cyan-bluish-gray) !important
}

.has-cool-to-warm-spectrum-gradient-background {
    background: var(--cis--preset--gradient--cool-to-warm-spectrum) !important
}

.has-blush-light-purple-gradient-background {
    background: var(--cis--preset--gradient--blush-light-purple) !important
}

.has-blush-bordeaux-gradient-background {
    background: var(--cis--preset--gradient--blush-bordeaux) !important
}

.has-luminous-dusk-gradient-background {
    background: var(--cis--preset--gradient--luminous-dusk) !important
}

.has-pale-ocean-gradient-background {
    background: var(--cis--preset--gradient--pale-ocean) !important
}

.has-electric-grass-gradient-background {
    background: var(--cis--preset--gradient--electric-grass) !important
}

.has-midnight-gradient-background {
    background: var(--cis--preset--gradient--midnight) !important
}

.has-small-font-size {
    font-size: var(--cis--preset--font-size--small) !important
}

.has-medium-font-size {
    font-size: var(--cis--preset--font-size--medium) !important
}

.has-large-font-size {
    font-size: var(--cis--preset--font-size--large) !important
}

.has-x-large-font-size {
    font-size: var(--cis--preset--font-size--x-large) !important
}

.cis-navigation a:where(:not(.cis-element-button)) {
    color: inherit
}

:where(.cis-post-template.is-layout-flex) {
    gap: 1.25em
}

:where(.cis-post-template.is-layout-grid) {
    gap: 1.25em
}

:where(.cis-columns.is-layout-flex) {
    gap: 2em
}

:where(.cis-columns.is-layout-grid) {
    gap: 2em
}

.cis-pullquote {
    font-size: 1.5em;
    line-height: 1.6
}

.c-accordion__item.no-js .c-accordion__content {
    display: block !important
}

.c-accordion__item.no-js .c-accordion__title {
    cursor: default;
    padding-right: none
}

.c-accordion__item.no-js .c-accordion__title:after {
    display: none
}

.c-accordion__title--button {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    border: none;
    border-radius: 0;
    box-shadow: none;
    direction: ltr;
    display: inline-block;
    font: inherit;
    height: auto;
    margin: 0;
    overflow: auto;
    padding: 0;
    text-align: left;
    text-decoration: none;
    transition: 0;
    vertical-align: middle;
    width: 100%
}

.c-accordion__title--button,
.c-accordion__title--button:focus,
.c-accordion__title--button:hover {
    background-color: transparent;
    color: inherit
}

.c-accordion__title {
    cursor: pointer;
    padding-right: 2rem;
    position: relative
}

.c-accordion__title:after {
    color: #777;
    content: "+";
    font-weight: 300;
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%)
}

.is-open>.c-accordion__title:after {
    content: "−"
}

[data-initially-open=false] .c-accordion__content {
    display: none
}

@media print {
    .c-accordion__content {
        display: block !important
    }
}

.editor-styles-wrapper .c-accordion__item.is-selected {
    border-bottom: 1px solid var(--cis-admin-theme-color) !important
}

@media (min-width:1740px) {
    .logo {
        width: 30%;
        margin-left: -50px
    }
}

@media (max-width:1740px) {
    .logo {
        width: 30%;
        margin-left: -30px
    }
}

@media (max-width:1023px) {
    .logo {
        width: 50%;
        margin: auto
    }
}
.swCatalogSearchKey.keywordSearchBox input{
    min-width: auto !important;
}  
.swCatalogSearchKey.keywordSearchBox button{
    right: 7px !important;
}  
<cfsavecontent variable="local.contribCCJS">
	<cfoutput>
	<script language="javascript">
	function useProfile() {
		mca_hideAlert('err_contribcc');
		$('##paymentTabs').children().hide();
		
		var pid = $('##paySource').val();
		$('##profileid').val(pid);
		$('##paymentTab' + pid).show();

		if ($('##profileid').val() == 0) toggleSaveBtn(pid, false);
		else toggleSaveBtn(pid, true);
	}
	function toggleSaveBtn(pid, f) {
		$('##btnSavePayment'+pid).attr('disabled',!f);
	}
	<cfif val(local.qryContributorPayProfile.MPProfileID)>
		function removeCCPayMethod() {
			$('input[name="p_#val(local.qryContributorPayProfile.MPProfileID)#_mppid"][value="0"]').prop('checked',true);
			$('##frmPayment button[type="submit"]').trigger('click');
		}
	</cfif>

	$(function() {
		top.$('##MCModalLabel').html('Associate Pay Method for #encodeForJavaScript("#qryContributionDetails.fullMemberName#")#');
		useProfile();
	});
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.contribCCJS)#">

<cfoutput>
<div id="selectCCDIV">
	<form name="frmPayment" id="frmPayment" action="#local.formlink#" method="post" class="px-3 mt-2" onsubmit="return checkPayForm();">
	<input type="hidden" name="pmid" id="pmid" value="#local.qryContributionDetails.memberid#">
	<input type="hidden" name="cid" id="cid" value="#local.contributionID#">
	<input type="hidden" name="profileid" id="profileid" value="0">
	
	<div class="mb-4">
		<b>Contribution: #local.qryContributionDetails.programName# - #local.qryContributionDetails.frequency#</b>
	</div>

	<div class="form-label-group mb-2">
		<select id="paySource" name="paySource" class="form-control" onchange="useProfile();">
			<cfloop query="local.qryMerchantProfiles">
				<option value="#local.qryMerchantProfiles.profileID#">#local.qryMerchantProfiles.profileName#</option>
			</cfloop>
		</select>
		<label for="paySource">Payment Method *</label>
	</div>

	<div id="err_contribcc" class="alert alert-danger mb-2 d-none"></div>
	
	<div id="paymentTabs">
		<cfset local.strPaymentFeatures = {}>
		<cfloop query="local.qryMerchantProfiles">
			<cfset local.thisProfileID = local.qryMerchantProfiles.profileID>
			<cfset local.strPaymentFeatures[local.thisProfileID] = application.objPayments.setDefaultPayFeaturesStruct()>
			<cfif local.qryMerchantProfiles.enableProcessingFeeDonation AND val(local.qryMerchantProfiles.processFeeDonationFeePercent) GT 0>
				<cfset local.strPaymentFeatures[local.thisProfileID].processingFee = { 
					"enable":1, 
					"select":val(local.qryContributorPayProfile.MPProfileID) EQ local.thisProfileID AND val(local.qryContributorPayProfile.payProcessFee) EQ 1, 
					"label":replaceNoCase(local.qryMerchantProfiles.processFeeContributionsFELabel,"{{PERCENT}}","#local.qryMerchantProfiles.processFeeDonationFeePercent#%"), 
					"denylabel":local.qryMerchantProfiles.processFeeContributionsFEDenyLabel,
					"title":local.qryMerchantProfiles.processFeeDonationFETitle,
					"msg":local.qryMerchantProfiles.processFeeDonationFEMsg 
				}>
			</cfif>
			
			<cfset local.strPaymentForm = application.objPayments.showGatewayInputForm(siteid=arguments.event.getValue('mc_siteinfo.siteid'), 
				profilecode=local.qryMerchantProfiles.profileCode, pmid=local.qryContributionDetails.memberid, showCOF=true, usePopup=false, 
				usePopupDIVName='pf_#local.thisProfileID#_', adminForm=1, paymentFeatures=local.strPaymentFeatures[local.thisProfileID])>

			<cfif len(local.strPaymentForm.headcode)>
				<cfhtmlhead text="#application.objCommon.minText(local.strPaymentForm.headcode)#">
			</cfif>
		
			<div id="paymentTab#local.thisProfileID#" style="display:none;">
				<div id="pf_#local.thisProfileID#_">
					<cfif len(local.strPaymentForm.inputForm)>
						<br/>
						<div>
							<div>#replaceNoCase(local.strPaymentForm.inputForm,'fld_','p_#local.thisProfileID#_fld_','ALL')#</div>
							<cfif val(local.qryContributorPayProfile.payProfileID) gt 0>
								<input type="radio" name="p_#local.thisProfileID#_mppid" id="p_#local.thisProfileID#_mppid_0" value="0" class="d-none">
								<div class="my-3">
									<button type="button" class="btn btn-xs btn-link btnSavePayment" onclick="removeCCPayMethod();"><i class="fa-regular fa-trash pr-2"></i>Remove Pay Method Associated with this Contribution</button>
								</div>
							</cfif>
						</div>
						<div id="divBtnWrapper#local.thisProfileID#" class="mt-3">
							<button name="btnSavePayment#local.thisProfileID#" id="btnSavePayment#local.thisProfileID#" type="submit" class="btn btn-sm btn-secondary btnSavePayment" disabled>Associate Pay Method</button>
						</div>
					</cfif>
				</div>
			</div>
			
			<cfsavecontent variable="local.extrapayJS">
				#local.extrapayJS#
				if ($('##profileid').val()==#local.thisProfileID#) {
					#replaceNoCase(local.strPaymentForm.jsvalidation,'fld_','p_#local.thisProfileID#_fld_','ALL')#
				}
			</cfsavecontent>
		</cfloop>
	</div>

	<cfif val(local.qryContributorPayProfile.payProfileID) gt 0 AND (NOT listLen(local.qryContributorPayProfile.eligibleProfileIDList) OR NOT listFind(local.qryContributorPayProfile.eligibleProfileIDList,local.qryContributorPayProfile.MPProfileID))>
		<cfset local.memberPayProfileDetail = application.objCustomPageUtils.getMemberPaymentProfileInfoForDisplay(siteID=arguments.event.getValue('mc_siteinfo.siteid'), orgID=arguments.event.getValue('mc_siteinfo.orgID'), memberID=local.qryContributionDetails.memberID, payProfileID=local.qryContributorPayProfile.payProfileID)>
		
		<input type="radio" name="p_#local.qryContributorPayProfile.MPProfileID#_mppid" id="p_#local.qryContributorPayProfile.MPProfileID#_mppid_0" value="0" checked class="d-none">

		<div class="alert d-flex align-items-center pl-2 align-content-center alert-danger py-1 font-size-sm" role="alert" style="width:350px;">
			<span class="font-size-lg d-block d-40 mr-2 text-center">
				<i class="fa-solid fa-circle-info"></i>
			</span>
			<span>This payment method is no longer supported.</span>
		</div>
		<div class="card card-box p-3 bg-secondary border-danger" style="width:250px;">#local.memberPayProfileDetail#</div>

		<div class="my-3">
			<button type="submit" class="btn btn-xs btn-link btnSavePayment"><i class="fa-regular fa-trash pr-2"></i>Remove Pay Method Associated with this Contribution</button>
		</div>
	</cfif>
	</form>
</div>
</cfoutput>

<cfif val(local.qryContributorPayProfile.MPProfileID) neq 0>
	<cfoutput>
	<script>
		var thisForm = $('##frmPayment')[0];
		$('##paySource').val(#val(local.qryContributorPayProfile.MPProfileID)#);
		useProfile();
		
		if ((thisForm.p_#val(local.qryContributorPayProfile.MPProfileID)#_mppid) && (thisForm.p_#val(local.qryContributorPayProfile.MPProfileID)#_mppid.length)) {
			$('input[name="p_#val(local.qryContributorPayProfile.MPProfileID)#_mppid"][value="#val(local.qryContributorPayProfile.payProfileID)#"]').prop('checked',true).trigger('click');
			<cfif local.keyExists("strPaymentFeatures") AND local.strPaymentFeatures.keyExists(local.qryContributorPayProfile.MPProfileID) AND local.strPaymentFeatures[local.qryContributorPayProfile.MPProfileID].processingFee.enable AND NOT local.strPaymentFeatures[local.qryContributorPayProfile.MPProfileID].processingFee.select>
				$('##processFeeDonation#val(local.qryContributorPayProfile.MPProfileID)#_no').prop('checked',true);
			</cfif>
		}
	</script>
	</cfoutput>
</cfif>

<cfsavecontent variable="local.validationJS">
	<cfoutput>
	<script language="javascript">
	function checkPayForm() {
		mca_hideAlert('err_contribcc');
		$('.btnSavePayment').attr('disabled',true);
		var arrReq = new Array();

		let pid = $('##paySource').val();
		if (pid == '') arrReq[arrReq.length] = 'Select which payment method you are using for this payment.';
	
		<cfif len(local.extrapayJS)>
			var thisForm = document.forms["frmPayment"];
			#local.extrapayJS#
		</cfif>
		
		if (arrReq.length > 0) {
			$('.btnSavePayment').removeAttr('disabled');
			var msg = '<strong>The following requires your attention:</strong><br/>' + arrReq.join('<br/>');
			mca_showAlert('err_contribcc', msg);
			return false;
		}

		if ($('.inputForm'+pid+'StepBtn').length) {
			$('.inputForm'+pid+'StepBtn').prop('disabled',true);
		}

		return true;
	};
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.validationJS)#">
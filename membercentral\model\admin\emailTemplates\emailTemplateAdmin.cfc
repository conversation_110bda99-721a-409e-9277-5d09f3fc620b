<cfcomponent output="false">

	<cffunction name="manageEmailTemplates" access="public" output="false" returntype="struct">
		<cfargument name="strETData" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.strReturn = { js="", html="", idExt="et#createUUID()#" }>
		<cfset local.baseURL = "/?mode=stream&pg=admin&mca_ajaxlib=emailTemplates&mca_ajaxfunc=">
		<cfset arguments.strETData.editURL = "/?pg=admin&mca_ajaxlib=emailTemplates&mca_ajaxfunc=">
		<cfsavecontent variable="local.strReturn.js">
			<cfoutput>
			<script language="javascript">
				var #toScript(local.baseURL,"mcet_baseURL")#
				<cfif len(arguments.strETData.gridExt)>
					<cfset local.thisGridJS = getGridJS(strETData=arguments.strETData, baseURL=local.baseURL)>
					#local.thisGridJS# 
				</cfif>
			</script>
			</cfoutput>
		</cfsavecontent>

		<cfsavecontent variable="local.strReturn.html">
			<cfoutput>
			<div id="mcet_div_grids#local.strReturn.idExt#" data-idExt="#local.strReturn.idExt#">
				<cfif len(arguments.strETData.gridExt)>
					<cfset local.thisGridHTML = getGridBoxHTML(strETData=arguments.strETData)>
					#local.thisGridHTML#
				</cfif>
			</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="getGridJS" access="public" output="false" returntype="string">
		<cfargument name="strETData" type="struct" required="true">
		<cfargument name="baseURL" type="string" required="true">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.js">
			<cfoutput>
				<cfset local.emailTemplatesJSONURL = "/?pg=admin&mca_jsonlib=mcdatatable&com=emailTemplateJSON&meth=getEmailTemplates&treeCode=#arguments.strETData.treeCode#&gridext=#arguments.strETData.gridext#&siteid=#arguments.strETData.siteID#&mode=stream">
				<cfset local.editCategoryURL = "#arguments.strETData.editURL#editCategory&_ettreecode=#arguments.strETData.treeCode#&_etg=#arguments.strETData.gridext#&mode=direct">
				<cfset local.editTemplateURL = "#arguments.strETData.editURL#editTemplate&_ettreecode=#arguments.strETData.treeCode#&_etg=#arguments.strETData.gridext#&mode=direct">

				var #toScript(local.emailTemplatesJSONURL,"mcet_#arguments.strETData.gridExt#_sourceURL")#
				var #toScript(application.regEx.email, "mcet_emailregex")#
				var #toScript(local.editCategoryURL,"mcet_editCategory#arguments.strETData.gridext#URL")#
				var #toScript(local.editTemplateURL,"mcet_editTemplate#arguments.strETData.gridext#URL")#
				var emailTemplateTable#arguments.strETData.gridExt#;
				
				<cfif arguments.strETData.initGridOnLoad>
					$(function(){
						mcet_initEmailTemplatesTable('#arguments.strETData.gridExt#');
					});
				</cfif>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.js>
	</cffunction>

	<cffunction name="getGridBoxHTML" access="public" output="false" returntype="string">
		<cfargument name="strETData" type="struct" required="true">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.html">
			<cfoutput>
				<div<cfif structKeyExists(arguments.strETData,'gridID')> id="#arguments.strETData.gridID#"</cfif> class="mcet_div_gridContainer<cfif structKeyExists(arguments.strETData,'gridClassList')> #replace(arguments.strETData.gridClassList,',',' ','all')#</cfif>" data-ETGridExt="#arguments.strETData.gridext#">
					<div class="d-flex align-items-end">
						<div class="col pl-0">
							<cfif len(arguments.strETData.title)>
								<h5>#arguments.strETData.title#</h5>
							</cfif>
							<div style="min-width:#arguments.strETData.gridWidth#px;">
								<cfif len(arguments.strETData.intro)>#arguments.strETData.intro#</cfif>
							</div>
						</div>
						<div class="ml-auto">
							<button type="button" title="Create New Template" class="btn btn-sm btn-primary" onclick="mcet_editTemplate('#arguments.strETData.gridext#',0);">
								<i class="fa-regular fa-circle-plus"></i> Create Template
							</button>
						</div>
					</div>
					<table id="emailTemplateTable#arguments.strETData.gridExt#" class="table table-sm table-striped table-bordered" style="width:100%">
						<thead>
							<tr>
								<th>Template</th>
								<th>Actions</th>
							</tr>
						</thead>
					</table>
				</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.html>
	</cffunction>

	<cffunction name="editTemplate" access="public" output="false" returntype="string">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="templateID" type="numeric" required="true">
		<cfargument name="treeCode" type="string" required="true">
		<cfargument name="gridExt" type="string" required="true">
		
		<cfset var local = structNew()>
		<cfset local.EmailTemplateAdminSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='EmailTemplateAdmin',siteID=arguments.siteID)>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryCategories">
			SET NOCOUNT ON;

			declare @siteID int, @controllingSiteResourceID int, @categoryTreeCode varchar(20), @categoryTreeID int;
			set @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;
			set @categoryTreeCode = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.treeCode#">;
			set @controllingSiteResourceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.EmailTemplateAdminSRID#">;

			select @categoryTreeID = categoryTreeID 
			from dbo.cms_categoryTrees 
			where controllingSiteResourceID = @controllingSiteResourceID 
			and categoryTreeCode = @categoryTreeCode;
				
			select c.categoryID, c.categoryName, oi.organizationName as orgName
			from dbo.cms_categories as c
			inner join dbo.cms_categoryTrees as ct on ct.categoryTreeID = c.categoryTreeID
			inner join dbo.sites as s on s.siteID = ct.siteID
			inner join dbo.organizations as o on o.orgID = s.orgID
			inner join dbo.orgIdentities as oi on oi.orgID = o.orgID and oi.orgIdentityID = o.defaultOrgIdentityID
			where c.categoryTreeID = @categoryTreeID
			and c.isActive = 1
			order by c.categoryName;
		</cfquery>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryTemplate">
			select et.templateID, et.templateName, et.categoryID, et.templateDescription, 
				et.status, et.dateCreated, et.subjectLine, et.emailFromName, et.emailFrom,
				emailTemplateContent.contentID, emailTemplateContent.rawContent
			from dbo.et_emailTemplates et
			cross apply dbo.fn_getContent(et.contentID,1) as emailTemplateContent
			where et.templateID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.templateID#">
		</cfquery>

		<cfif val(local.qryTemplate.templateID)>
			<cfset local.actionWord = "Edit">
		<cfelse>
			<cfset local.actionWord = "Add">
		</cfif>

		<cfscript>
		local.mergeInstLink = "/?pg=ContentEditor&ceAction=showMergeCodeInstructions&mode=stream";
		local.mergeCodeList = "";
		switch(arguments.treeCode) {
			case "ETCONTRIBUTIONS":
				local.heading = "#local.actionWord# Contribution Email Template";
				break;
			case "ETGROUPS":
				local.heading = "#local.actionWord# Group Email Template";
				break;
			case "ETHISTORY":
				local.heading = "#local.actionWord# Member History / Notes / Relationships Email Template";
				local.mergeCodeList = listAppend(local.mergeCodeList, "incMH");
				break;
			case "ETSUBS":
				local.heading = "#local.actionWord# Subscription Email Template";
				local.mergeCodeList = listAppend(local.mergeCodeList, "incSub");
				break;
			case "ETINVOICES":
				local.heading = "#local.actionWord# Invoice Email Template";
				break;
			case "ETEVENTS":
				local.heading = "#local.actionWord# Event Email Template";
				local.mergeCodeList = listAppend(local.mergeCodeList, "incEV");
				break;
			case "ETTASKS":
				local.heading = "#local.actionWord# Task Email Template";
				local.mergeCodeList = listAppend(local.mergeCodeList, "incTasks");
				break;
			case "ETSEMWEB":
				local.heading = "#local.actionWord# SeminarWeb Email Template";
				break;
			case "ETSUCCESS":
				local.heading = "#local.actionWord# Submission Email Template";
				local.mergeCodeList = listAppend(local.mergeCodeList, "incSuccess");
				break;
			case "ETREFCLIENTS":
				local.heading = "#local.actionWord# Referral Client Template";
				local.mergeCodeList = listAppend(local.mergeCodeList, "incReferral,exMem");
				break;
			case "ETREFMEMBERS":
				local.heading = "#local.actionWord# Referral Member Template";
				local.mergeCodeList = listAppend(local.mergeCodeList, "incReferral");
				break;
			case "ETREFCLIENTRCPT":
				local.heading = "#local.actionWord# Client Receipt Template";
				local.mergeCodeList = listAppend(local.mergeCodeList, "incReferral,exMem");
				break;
			case "ETREFFEEDSCRPNCY":
				local.heading = "#local.actionWord# Fee Discrepancy Template";
				local.mergeCodeList = listAppend(local.mergeCodeList, "incReferral,exMem");
				break;
			case "ETREFREPORT":
				local.heading = "#local.actionWord# Automated Referral Reports Template";
				local.mergeCodeList = listAppend(local.mergeCodeList, "incReferral");
				break;
			case "ETREFCSTRACKREPORT":
				local.heading = "#local.actionWord# Automated Internal Case Tracking Reports Template";
				local.mergeCodeList = listAppend(local.mergeCodeList, "incReferral");
				break;
		}

		if (len(local.mergeCodeList)){
			local.mergeInstLink = "#local.mergeInstLink#&" & listChangeDelims(local.mergeCodeList, "=1&") & "=1";
		}
		</cfscript>
		
		<cfsavecontent variable="local.data">
			<cfinclude template="frm_emailTemplate.cfm">
		</cfsavecontent>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="editCategory" access="public" output="false" returntype="string">
		<cfargument name="categoryID" type="numeric" required="true">
		<cfargument name="treeCode" type="string" required="true">
		<cfargument name="gridExt" type="string" required="true">
		
		<cfset var local = structNew()>
		
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryCategory">
			select c.categoryID, c.categoryName, c.categoryDesc
			from dbo.cms_categories c
			where c.categoryID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.categoryID#">
			and c.isActive = 1
		</cfquery>
			
		<cfsavecontent variable="local.data">
			<cfinclude template="frm_category.cfm">
		</cfsavecontent>
		
		<cfreturn local.data>
	</cffunction>

</cfcomponent>
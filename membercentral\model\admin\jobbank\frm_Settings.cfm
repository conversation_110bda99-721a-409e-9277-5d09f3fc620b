<cfset local.defaultMaxPostingDays = '7,14,30,60,90,120'>
<cfset local.defaultRecordsPerPage = '5,10,15,20,25,30'>
<cfset local.defaultMaxRecords = 'Unlimited,10,25,50,100,250,500,1000,2500'>

<cfsavecontent variable="local.settingsJS">
	<cfoutput>
	<!--- GL Account Widget JavaScript --->
	#local.strGLAcctWidget.js#
	<script language="javascript">
		function validateSettingsForm() {
			mca_hideAlert('err_settings');

			if($('##newApplicationInstanceName').val() == ''){
				mca_showAlert('err_settings', 'Enter the Job Bank Name');
				return false;
			}

			$('##saveSettingsBtn').attr('disabled', true);
			return true;
		}
	</cfoutput>
	</script>
</cfsavecontent>
<cfhtmlhead text="#local.settingsJS#">

<cfswitch expression="#arguments.event.getValue('msg',0)#">
	<cfcase value="1"><cfset local.messageClass = "text-green"><cfset local.message = "Settings saved."></cfcase>
	<cfcase value="2"><cfset local.messageClass = "text-danger"><cfset local.message = "Settings were not saved."></cfcase>
	<cfdefaultcase><cfset local.messageClass = "text-green"><cfset local.message = ""></cfdefaultcase>
</cfswitch>

<cfoutput>
<form name="frmJBSettings" id="frmJBSettings" action="#this.link.saveSettings#" method="post" onsubmit="return validateSettingsForm();">
	<input type="hidden" name="jbID" value="#this.jbData.jobBankID#">

	<div class="row mb-2">
		<div class="col text-right">
			<cfif len(local.message)>
				<span id="saveResponse" class="#local.messageClass# mr-2">#local.message#</span>
			</cfif>
			<button type="button" name="btnShowPerms" id="btnShowPerms" class="btn btn-sm btn-secondary" onclick="mca_showPermissions(#this.jbData.siteResourceID#,'#urlencodedformat(this.jbData.jobBankName)#');">Permissions</button>
			<button type="submit" name="saveSettingsBtn" id="saveSettingsBtn" class="btn btn-sm btn-primary">Save Information</button>
		</div>
	</div>

	<div id="err_settings" class="alert alert-danger mb-2 d-none"></div>
	<div class="card-body">
		<div class="form-group row mb-3">
			<div class="col font-weight-bold">Basic Job Bank Settings</div>
		</div>
		<div class="form-group">
			<div class="form-label-group">
				<input type="hidden" name="oldApplicationInstanceName" value="#this.jbData.applicationInstanceName#">
				<input type="text" name="newApplicationInstanceName" id="newApplicationInstanceName" value="#this.jbData.applicationInstanceName#" class="form-control"  maxlength="100">					
				<label for="newApplicationInstanceName">Job Bank Name *</label>
			</div>
		</div>
		<div class="form-group row">
			<label for="disabled" class="col-sm-3 col-form-label-sm font-size-md">Job Bank Disabled</label>
			<div class="col-sm mt-1">				
					<div class="form-check">
						<input type="radio" name="disabled" id="disabled_1" class="form-check-input" value="1" <cfif this.jbData.disabled>checked</cfif>>
						<label class="form-check-label" for="disabled_1">Yes</label>
					</div>
					<div class="form-check">
						<input type="radio" name="disabled" id="disabled_0" class="form-check-input" value="0" <cfif NOT this.jbData.disabled>checked</cfif>>
						<label class="form-check-label" for="disabled_0">No</label>
					</div>			
			</div>
		</div>
		<div class="form-group">
			<div class="form-label-group">
				<select name="maxDaysForPostingJobs" id="maxDaysForPostingJobs" class="custom-select">
					<cfloop list="#local.defaultMaxPostingDays#" index="local.cItem">
						<option value="#local.cItem#"<cfif local.cItem EQ this.jbData.maxDaysForPostingJobs> SELECTED</cfif>>#local.cItem#</option>
					</cfloop>
				</select>
				<label for="maxDaysForPostingJobs">Days to Show Jobs</label>
			</div>
		</div>	
		<div class="form-group">
			<div class="form-label-group">
				<select name="maxDaysForPostingResumes" id="maxDaysForPostingResumes" class="custom-select">
					<cfloop list="#local.defaultMaxPostingDays#" index="local.cItem">
						<option value="#local.cItem#"<cfif local.cItem EQ this.jbData.maxDaysForPostingResumes> SELECTED</cfif>>#local.cItem#</option>
					</cfloop>
				</select>
				<label for="maxDaysForPostingResumes">Days to Show Resumes</label>
			</div>
		</div>	
		<div class="form-group">
			<div class="form-label-group">
				<select name="recordsPerPage" id="recordsPerPage" class="custom-select">
					<cfloop list="#local.defaultRecordsPerPage#" index="local.cItem">
						<option value="#local.cItem#"<cfif local.cItem EQ this.jbData.recordsPerPage> SELECTED</cfif>>#local.cItem#</option>
					</cfloop>
				</select>
				<label for="recordsPerPage">Records Shown Per Page</label>
			</div>
		</div>
		<div class="form-group">
			<div class="form-label-group">
				<select name="maxSearchResults" id="maxSearchResults" class="custom-select">
					<cfloop list="#local.defaultMaxRecords#" index="local.cItem">
						<option value="#local.cItem#"<cfif local.cItem EQ this.jbData.maxSearchResults> SELECTED</cfif>>#local.cItem#</option>
					</cfloop>
				</select>
				<label for="maxSearchResults">Maximum Search Results Shown</label>
			</div>
		</div>	
		<div class="form-group row my-3">
			<div class="col">
				<div class="font-weight-bold">Exclusive Access Period</div>
				<div class="mt-2">Give certain groups exclusive viewing access to new job postings for a certain number of days. This is used in conjuction with the "View Posting during Exclusive Access Period" permission.</div>
			</div>
		</div>
		<div class="form-group">
			<div class="form-label-group">	
				<input type="text" name="exclusiveAccessDays" id="exclusiveAccessDays" value="#this.jbData.exclusiveAccessDays#" class="form-control"  maxlength="3">					
				<label for="exclusiveAccessDays">Exclusive Access Period (Days)</label>
			</div>
		</div>
		<div class="form-group row my-3">
			<div class="col font-weight-bold">Optional Jobbank Features</div>
		</div>
		<div class="form-group row">
			<label for="allowDocumentsAttached" class="col-sm-3 col-form-label-sm font-size-md">Allow Document Attachments</label>
			<div class="col-sm mt-1">				
				<div class="form-check">
					<input type="radio" name="allowDocumentsAttached" id="allowDocumentsAttached_1" class="form-check-input" value="1" <cfif this.jbData.allowDocumentsAttached>checked</cfif>>
					<label class="form-check-label" for="allowDocumentsAttached_1">Yes</label>
				</div>
				<div class="form-check">
					<input type="radio" name="allowDocumentsAttached" id="allowDocumentsAttached_0" class="form-check-input" value="0" <cfif NOT this.jbData.allowDocumentsAttached>checked</cfif>>
					<label class="form-check-label" for="allowDocumentsAttached_0">No</label>
				</div>			
			</div>
		</div>
		<div class="form-group row">
			<label for="allowCandidateToHideIdentity" class="col-sm-3 col-form-label-sm font-size-md">Allow Hide Candidate Identity</label>
			<div class="col-sm mt-1">				
				<div class="form-check">
					<input type="radio" name="allowCandidateToHideIdentity" id="allowCandidateToHideIdentity_1" class="form-check-input" value="1" <cfif this.jbData.allowCandidateToHideIdentity>checked</cfif>>
					<label class="form-check-label" for="allowCandidateToHideIdentity_1">Yes</label>
				</div>
				<div class="form-check">
					<input type="radio" name="allowCandidateToHideIdentity" id="allowCandidateToHideIdentity_0" class="form-check-input" value="0" <cfif NOT this.jbData.allowCandidateToHideIdentity>checked</cfif>>
					<label class="form-check-label" for="allowCandidateToHideIdentity_0">No</label>
				</div>			
			</div>
		</div>
		<div class="form-group row">
			<label for="allowEmployerToHideIdentity" class="col-sm-3 col-form-label-sm font-size-md">Allow Hide Employer Identity</label>
			<div class="col-sm mt-1">				
				<div class="form-check">
					<input type="radio" name="allowEmployerToHideIdentity" id="allowEmployerToHideIdentity_1" class="form-check-input" value="1" <cfif this.jbData.allowEmployerToHideIdentity>checked</cfif>>
					<label class="form-check-label" for="allowEmployerToHideIdentity_1">Yes</label>
				</div>
				<div class="form-check">
					<input type="radio" name="allowEmployerToHideIdentity" id="allowEmployerToHideIdentity_0" class="form-check-input" value="0" <cfif NOT this.jbData.allowEmployerToHideIdentity>checked</cfif>>
					<label class="form-check-label" for="allowEmployerToHideIdentity_0">No</label>
				</div>			
			</div>
		</div>
		<div class="form-group row">
			<label for="allowSearchEmployer" class="col-sm-3 col-form-label-sm font-size-md">Allow Search By Employer</label>
			<div class="col-sm mt-1">				
				<div class="form-check">
					<input type="radio" name="allowSearchEmployer" id="allowSearchEmployer_1" class="form-check-input" value="1" <cfif this.jbData.allowSearchEmployer>checked</cfif>>
					<label class="form-check-label" for="allowSearchEmployer_1">Yes</label>
				</div>
				<div class="form-check">
					<input type="radio" name="allowSearchEmployer" id="allowSearchEmployer_0" class="form-check-input" value="0" <cfif NOT this.jbData.allowSearchEmployer>checked</cfif>>
					<label class="form-check-label" for="allowSearchEmployer_0">No</label>
				</div>			
			</div>
		</div>
		<div class="form-group row">
			<label for="allowSearchCity" class="col-sm-3 col-form-label-sm font-size-md">Allow Search By City</label>
			<div class="col-sm mt-1">				
				<div class="form-check">
					<input type="radio" name="allowSearchCity" id="allowSearchEmployer_1" class="form-check-input" value="1" <cfif this.jbData.allowSearchCity>checked</cfif>>
					<label class="form-check-label" for="allowSearchEmployer_1">Yes</label>
				</div>
				<div class="form-check">
					<input type="radio" name="allowSearchCity" id="allowSearchEmployer_0" class="form-check-input" value="0" <cfif NOT this.jbData.allowSearchCity>checked</cfif>>
					<label class="form-check-label" for="allowSearchEmployer_0">No</label>
				</div>			
			</div>
		</div>
		<div class="form-group row">
			<label for="allowSearchState" class="col-sm-3 col-form-label-sm font-size-md">Allow Search By State</label>
			<div class="col-sm mt-1">				
				<div class="form-check">
					<input type="radio" name="allowSearchState" id="allowSearchState_1" class="form-check-input" value="1" <cfif this.jbData.allowSearchState>checked</cfif>>
					<label class="form-check-label" for="allowSearchState_1">Yes</label>
				</div>
				<div class="form-check">
					<input type="radio" name="allowSearchState" id="allowSearchState_0" class="form-check-input" value="0" <cfif NOT this.jbData.allowSearchState>checked</cfif>>
					<label class="form-check-label" for="allowSearchState_0">No</label>
				</div>			
			</div>
		</div>
		<div class="form-group row">
			<label for="allowSearchJobTitle" class="col-sm-3 col-form-label-sm font-size-md">Allow Search By Job Title</label>
			<div class="col-sm mt-1">				
				<div class="form-check">
					<input type="radio" name="allowSearchJobTitle" id="allowSearchJobTitle_1" class="form-check-input" value="1" <cfif this.jbData.allowSearchJobTitle>checked</cfif>>
					<label class="form-check-label" for="allowSearchJobTitle_1">Yes</label>
				</div>
				<div class="form-check">
					<input type="radio" name="allowSearchJobTitle" id="allowSearchJobTitle_0" class="form-check-input" value="0" <cfif NOT this.jbData.allowSearchJobTitle>checked</cfif>>
					<label class="form-check-label" for="allowSearchJobTitle_0">No</label>
				</div>			
			</div>
		</div>
		<div class="form-group row">
			<label for="allowSearchPositionDescription" class="col-sm-3 col-form-label-sm font-size-md">Allow Search By Position Description</label>
			<div class="col-sm mt-1">				
				<div class="form-check">
					<input type="radio" name="allowSearchPositionDescription" id="allowSearchPositionDescription_1" class="form-check-input" value="1" <cfif this.jbData.allowSearchPositionDescription>checked</cfif>>
					<label class="form-check-label" for="allowSearchPositionDescription_1">Yes</label>
				</div>
				<div class="form-check">
					<input type="radio" name="allowSearchPositionDescription" id="allowSearchPositionDescription_0" class="form-check-input" value="0" <cfif NOT this.jbData.allowSearchPositionDescription>checked</cfif>>
					<label class="form-check-label" for="allowSearchPositionDescription_0">No</label>
				</div>			
			</div>
		</div>
		<div class="form-group row">
			<label for="allowSearchWorkStatus" class="col-sm-3 col-form-label-sm font-size-md">Allow Search By Job Type</label>
			<div class="col-sm mt-1">				
				<div class="form-check">
					<input type="radio" name="allowSearchWorkStatus" id="allowSearchWorkStatus_1" class="form-check-input" value="1" <cfif this.jbData.allowSearchWorkStatus>checked</cfif>>
					<label class="form-check-label" for="allowSearchWorkStatus_1">Yes</label>
				</div>
				<div class="form-check">
					<input type="radio" name="allowSearchWorkStatus" id="allowSearchWorkStatus_0" class="form-check-input" value="0" <cfif NOT this.jbData.allowSearchWorkStatus>checked</cfif>>
					<label class="form-check-label" for="allowSearchWorkStatus_0">No</label>
				</div>			
			</div>
		</div>
		<input type="hidden" name="allowSearchDateOpen" value="1" />
		<div class="form-group row">
			<label for="allowSearchDatePosted" class="col-sm-3 col-form-label-sm font-size-md">Allow Search By Date Posted</label>
			<div class="col-sm mt-1">				
				<div class="form-check">
					<input type="radio" name="allowSearchDatePosted" id="allowSearchDatePosted_1" class="form-check-input" value="1" <cfif this.jbData.allowSearchDatePosted>checked</cfif>>
					<label class="form-check-label" for="allowSearchDatePosted_1">Yes</label>
				</div>
				<div class="form-check">
					<input type="radio" name="allowSearchDatePosted" id="allowSearchDatePosted_0" class="form-check-input" value="0" <cfif NOT this.jbData.allowSearchDatePosted>checked</cfif>>
					<label class="form-check-label" for="allowSearchDatePosted_0">No</label>
				</div>			
			</div>
		</div>
		<div class="form-group row">
			<label for="allowSearchObjective" class="col-sm-3 col-form-label-sm font-size-md">Allow Search By Objective</label>
			<div class="col-sm mt-1">				
				<div class="form-check">
					<input type="radio" name="allowSearchObjective" id="allowSearchObjective_1" class="form-check-input" value="1" <cfif this.jbData.allowSearchObjective>checked</cfif>>
					<label class="form-check-label" for="allowSearchObjective_1">Yes</label>
				</div>
				<div class="form-check">
					<input type="radio" name="allowSearchObjective" id="allowSearchObjective_0" class="form-check-input" value="0" <cfif NOT this.jbData.allowSearchObjective>checked</cfif>>
					<label class="form-check-label" for="allowSearchObjective_0">No</label>
				</div>			
			</div>
		</div>
		<div class="form-group mt-3">
			#local.strGLAcctWidget.html#
		</div>
		<div class="form-group">
			<div class="form-label-group">
				<div class="input-group input-group">
					<input type="text" name="justview1" id="justview1" value="/?pg=#this.jbData.pageName#" class="form-control" readonly="readonly" onclick="this.select();"/>
					<div class="input-group-append">
						<span class="input-group-text"><a target="_blank" href="/?pg=#this.jbData.pageName#"><i class="fa-solid fa-up-right-from-square"></i></a></span>
					</div>
					<label for="justview1">Internal URL</label>
				</div>
			</div>
		</div>
		<div class="form-group">
			<div class="form-label-group">
				<div class="input-group input-group">
					<input type="text" name="justview2" id="justview2" value="#arguments.event.getValue('mc_siteInfo.scheme','http')#://#arguments.event.getValue('mc_siteInfo.mainhostname')#/?pg=#this.jbData.pageName#" class="form-control" readonly="readonly" onclick="this.select();"/>
					<div class="input-group-append">
						<span class="input-group-text"><a target="_blank" href="/?pg=#this.jbData.pageName#"><i class="fa-solid fa-up-right-from-square"></i></a></span>
					</div>
					<label for="justview2">External URL</label>
				</div>
			</div>
		</div>
	</div>
	
</form>
</cfoutput>
<cfsavecontent variable="local.gridJS">
	<cfoutput>
	<script type="text/javascript">
		let cardsOnFileTable, cardsOnFileTableLoadingState = 'init';

		function filterCardsOnFile(){
			clearCOFErrors();
			cardsOnFileTable.draw();
		}
		function getCardsOnFileFilterParams(obj){
			var objParams = obj || {};
			
			objParams.fSuccessPayDateFrom = $('##fSuccessPayDateFrom').val() || '';
			objParams.fSuccessPayDateTo = $('##fSuccessPayDateTo').val() || '';
			objParams.fExpirationDateFrom = $('##fExpirationDateFrom').val() || '';
			objParams.fExpirationDateTo = $('##fExpirationDateTo').val() || '';
			objParams.fCardStatus = $('##fCardStatus').val() === null ? '' : $('##fCardStatus').val().toString();
			objParams.fCardAssocTo = $('##fCardAssocTo').val() === null ? '' : $('##fCardAssocTo').val().join(',');

			return objParams;
		}
		function showCardsOnFileGrid(reload) {
			$('##divCardsOnFileGridActionsArea').removeClass('d-none');
			$('##confirmRemoveCardsOnFile').addClass('d-none'); 
			$('##divGridContainer').removeClass('d-none'); 
			$('##divFilterForm').hide();
			if(reload){
				filterCardsOnFile();
				mca_showAlert('err_cardsOnFile', 'The filtered #lCase(local.COFTypeDesc)# will be deleted shortly.');
			} else {
				$('##confirmRemoveCardsOnFile').addClass('d-none'); 
				$('##divCardsOnFileGridActionsArea').removeClass('d-none');
			}
		}
		function cardsOnFileDelayFunc(t) {
			return new Promise(resolve => setTimeout(resolve, t));
		}
		function confirmRemoveCOF() {
			// ensure we filter on no association
			$('##fCardAssocTo').val('NotAssoc').trigger('change.select2');
			filterCardsOnFile();

			// loading confirm screen
			return new Promise(async function(resolve,reject) {
				clearCOFErrors();
				$('##confirmDelete').val("");
				$('##divFilterForm').hide();
				$('##divCardsOnFileGridActionsArea,##divGridContainer,##confirmCOFAlert').addClass('d-none');
				$('##confirmCOFAlertLoading').html(mca_getLoadingHTML());
				$('##confirmRemoveCardsOnFile,##confirmCOFAlertLoading').removeClass('d-none');
				
				let maxCounter = 50, counter = 0;
				while(true) {
					counter++;
					await cardsOnFileDelayFunc(400);
					if (cardsOnFileTableLoadingState == 'drawcomplete' || counter == maxCounter) break;
				}
				resolve();
			}).then(function() {
				if (cardsOnFileTable.page.info().recordsTotal == 0) {
					mca_showAlert('err_cardsOnFile', 'There are no filtered #lCase(local.COFTypeDesc)#.');
					showCardsOnFileGrid();
					return false;
				} else {
					$('##totalCount').text(cardsOnFileTable.page.info().recordsTotal);
					$('##confirmCOFAlertLoading').addClass('d-none');
					$('##confirmCOFAlert').removeClass('d-none');	
				}
			}).catch(function(err) {
				console.log(err);
			});
		}
		function doConfirmRemoveCOF() {
			if($('##confirmDelete').val().toUpperCase() !== "DELETE") {
				mca_showAlert('err_removeCOF', 'Enter "DELETE" in the box to continue.');
			} else {
				mca_hideAlert('err_removeCOF');

				var deleteResult = function(r) {
					if (r.success && r.success.toLowerCase() == 'true') {
						showCardsOnFileGrid(true);
					}
					else if (r.errmsg && r.errmsg != '') {
						mca_showAlert('err_removeCOF', r.errmsg);
					}
					else {
						mca_showAlert('err_removeCOF', 'We ran into an issue removing the #lCase(local.COFTypePlural)#. Try again.');
					}
					$('##btnRemoveCardsOnFile').prop('disabled',false);
				};

				$('##btnRemoveCardsOnFile').prop('disabled',true);

				var objParams = { profileID: #local.profileID# };
				objParams = getCardsOnFileFilterParams(objParams);
				TS_AJX('ADMINMP','removeFilteredCardsOnFile',objParams,deleteResult,deleteResult,20000,deleteResult);
			}
		}
		function clearCOFErrors() {
			$('div.err_COF').html('').addClass('d-none');
		}
		function initCardsOnFileTable(){
			let domString = "<'row'<'col-6'<'float-left mt-2'l><'float-left p-1 m-2 selCountDisp'>><'col-6'p>>" + "<'row'<'col-12'tr>>" + "<'row'<'col-5'i><'col-7'p>>";
			cardsOnFileTable = $('##cardsOnFileTable').DataTable({
				"processing": true,
				"serverSide": true,
				"pageLength": 25,
				"lengthMenu": [ 10, 25, 50, 100 ],
				"dom": domString,
				"language": {
					"lengthMenu": "_MENU_",
					"emptyTable": "No #local.COFTypeDesc# Found"
				},
				"ajax": { 
					"url": "#local.cardsOnFileLink#",
					"type": "post",
					"data": function(d) {
						$.each($('##frmFilterCards').serializeArray(),function() {
							d[this.name] = (typeof d[this.name] != "undefined" ? d[this.name] + ',' : '') + this.value || '';
						});
					}
				},
				"autoWidth": false,
				"columns": [
					{ 	
						"data": null,
						"render": function ( data, type, row, meta ) {
							let renderData = '<div>'+data.membername+'</div>';
							if (data.company.length) renderData += '<div class="text-dim small">' + data.company + '</div>';
							return type === 'display' ? renderData : data;
						},
						"width": "38%",
						"className": "align-top"
					},
					{ 	
						"data": null,
						"render": function ( data, type, row, meta ) {
							let renderData = (data.cardtype.length ? data.cardtype + ' ' : '') + (data.accttype.length ? data.accttype + ' Checking ' : '') + data.carddetail + (data.cardexp.length ? ' Exp ' + data.cardexp : '');
							renderData += '<div class="text-dim small">added ' + data.dateadded + '</div>';
							return type === 'display' ? renderData : data;
						},
						"width": "42%",
						"className": "align-top"
					},
					{ 	
						"data": null,
						"render": function ( data, type, row, meta ) {
							let renderData = '';
							if(data.cardassoctocontrib > 0) renderData += '<i class="fas fa-hand-holding-usd px-2 m-1 text-success" title="Contributions"></i>';
							if(data.cardassoctosubs > 0) renderData += '<i class="fa fa-rocket px-2 m-1 text-primary" title="Subscriptions"></i>';
							if(data.cardassoctoinv > 0) renderData += '<i class="fas fa-file-invoice-dollar px-2 m-1 text-info" title="Invoices"></i>';
							return type === 'display' ? renderData : data;
						},
						"width": "20%"
					}
				],
				"ordering": false,
				preDrawCallback: function (settings) {
					cardsOnFileTableLoadingState = 'predraw';
				},
				drawCallback: function (settings) {
					cardsOnFileTableLoadingState = 'drawcomplete';
				}
			});
		}
		function filterCards() {
			if (!$('##divFilterForm').is(':visible')) {
				$('##divFilterForm').show();
			}
		}
		function resetCardFilters() {
			$('##frmFilterCards')[0].reset();
			$('##fCardAssocTo').trigger('change.select2');
			filterCardsOnFile();
		}
		function exportCOF() {
			mca_hideAlert('err_cardsOnFile');

			if (cardsOnFileTable.page.info().recordsTotal == 0) {
				mca_showAlert('err_cardsOnFile', 'No #local.COFTypeDesc# Found.');
				return false;
			} else {
				$('##exportCOFAction').html('<i class="fa-regular fa-spinner fa-spin"></i> Exporting Filtered #local.COFTypePlural#</a>');
				setTimeout(() => {
					$('##exportCOFAction').html('<i class="fa-regular fa-download"></i> Export Filtered #local.COFTypePlural#</a>');
				}, 4000);
				self.location.href = '#local.exportCOFLink#&' + $('##frmFilterCards').serialize();
			}
		}
		function cardAssocFilterChange() {
			let fCardAssocTo = $('##fCardAssocTo').val() === null ? '' : $('##fCardAssocTo').val().join(',');
			if (fCardAssocTo.length) {
				let arrCardAssocTo = fCardAssocTo.split(',');
				if (arrCardAssocTo.indexOf('NotAssoc') != -1 && arrCardAssocTo.length > 1) {
					$('##fCardAssocTo').val('NotAssoc').trigger('change.select2');
				}
			}
		}

		$(function() {
			initCardsOnFileTable();
			mca_setupSelect2($('##divFilterForm'));
			mca_setupDatePickerRangeFields('fSuccessPayDateFrom','fSuccessPayDateTo');
			<cfif local.merchantProfile.gatewayType EQ 'AuthorizeCCCIM'>
				mca_setupDatePickerRangeFields('fExpirationDateFrom','fExpirationDateTo');
			</cfif>
			mca_setupCalendarIcons('frmFilterCards');
		});
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.gridJS#">

<cfoutput>
<div class="px-3">
	<div id="divCardsOnFileGridActionsArea">
		<div id="err_cardsOnFile" class="alert alert-danger err_COF mb-2 d-none"></div>
		<div class="toolButtonBar">
			<div><a href="##" onclick="filterCards();return false;" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to filter #lCase(local.COFTypePlural)#."><i class="fa-regular fa-filter"></i> Filter #local.COFTypePlural#</a></div>
			<div><a href="##" onclick="exportCOF();return false;" id="exportCOFAction" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to export filtered #lCase(local.COFTypePlural)#."><i class="fa-regular fa-download"></i> Export Filtered #local.COFTypePlural#</a></div>
			<div><a href="##" onclick="confirmRemoveCOF();return false;" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Remove filtered #lCase(local.COFTypePlural)#."><i class="fa-regular fa-circle-minus"></i> Delete Filtered #local.COFTypePlural#</a></div>
		</div>
	</div>
	<div id="confirmRemoveCardsOnFile" class="d-none">
		<div class="card card-box">
			<div class="card-header bg-light py-2">
				<div class="card-header--title font-weight-bold font-size-md">Delete #local.COFTypeDesc#</div>
			</div>
			<div class="card-body p-3">
				<div id="confirmCOFAlert" class="d-none">
					<div id="err_removeCOF" class="alert alert-danger err_COF mb-2 d-none"></div>
					<div class="alert alert-warning">
						<div class="font-weight-bold mb-2">Confirmation Needed</div>
						<div>
							Are you sure you want to delete the <span id="totalCount"></span> filtered #lCase(local.COFTypeDesc)#?<br/>
							Any filtered #lCase(local.COFTypePlural)# associated to Invoices, Subscriptions, or Contributions have been removed from the results and <b>will not</b> be mass deleted.<br/> 
							This action cannot be reversed.<br/>
							To continue, enter "DELETE" in the textbox below.<br/>
							<div class="col-sm-6 mt-2"><input type="text" class="form-control form-control-sm" placeholder="Type DELETE" name="confirmDelete" id="confirmDelete" value=""></div>
						</div>
					</div>
					<div class="form-group row no-gutters mt-2">
						<div class="col-sm-12 text-right">
							<button name="btnRemoveCardsOnFile" id="btnRemoveCardsOnFile" onclick="doConfirmRemoveCOF();return false;" class="btn btn-sm btn-danger">Delete</button>
							<button name="btnCancelRemove" type="button" class="btn btn-sm btn-secondary" onClick="showCardsOnFileGrid();">Cancel</button>
						</div>
					</div>
				</div>
				<div id="confirmCOFAlertLoading" class="d-none"></div>
			</div>
		</div>
	</div>
	<div class="row my-3" id="divFilterForm" style="display:none;">
		<div class="col">
			<div class="card card-box mb-1">
				<div class="card-header py-1 bg-light">
					<div class="card-header--title font-weight-bold font-size-md">Filter #local.COFTypeDesc#</div>
				</div>
				<div class="card-body">	
					<form name="frmFilterCards" id="frmFilterCards">
					<div class="card-body p-0 pb-3">
						<div class="row">
							<div class="col-xl-6 col-lg-12">
								<div class="form-row">
									<div class="col-sm-6 col-xs-12">
										<div class="form-group">
											<div class="form-label-group mb-2">
												<div class="input-group dateFieldHolder">
													<input type="text" name="fSuccessPayDateFrom" id="fSuccessPayDateFrom" value="" class="form-control dateControl">
													<div class="input-group-append">
														<span class="input-group-text cursor-pointer calendar-button" data-target="fSuccessPayDateFrom"><i class="fa-solid fa-calendar"></i></span>
														<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('fSuccessPayDateFrom');"><i class="fa-solid fa-circle-xmark"></i></a></span>
													</div>
													<label for="fSuccessPayDateFrom">Successful payment on/after</label>
												</div>
											</div>
										</div>
									</div>
									<div class="col-sm-6 col-xs-12">
										<div class="form-group">
											<div class="form-label-group mb-2">
												<div class="input-group dateFieldHolder">
													<input type="text" name="fSuccessPayDateTo" id="fSuccessPayDateTo" value="" class="form-control dateControl">
													<div class="input-group-append">
														<span class="input-group-text cursor-pointer calendar-button" data-target="fSuccessPayDateTo"><i class="fa-solid fa-calendar"></i></span>
														<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('fSuccessPayDateTo');"><i class="fa-solid fa-circle-xmark"></i></a></span>
													</div>
													<label for="fSuccessPayDateTo">Successful payment on/before</label>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
						<cfif local.merchantProfile.gatewayType EQ 'AuthorizeCCCIM'>
							<div class="row">
								<div class="col-xl-6 col-lg-12">
									<div class="form-row">
										<div class="col-sm-6 col-xs-12">
											<div class="form-group">
												<div class="form-label-group mb-2">
													<div class="input-group dateFieldHolder">
														<input type="text" name="fExpirationDateFrom" id="fExpirationDateFrom" value="" class="form-control dateControl">
														<div class="input-group-append">
															<span class="input-group-text cursor-pointer calendar-button" data-target="fExpirationDateFrom"><i class="fa-solid fa-calendar"></i></span>
															<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('fExpirationDateFrom');"><i class="fa-solid fa-circle-xmark"></i></a></span>
														</div>
														<label for="fExpirationDateFrom">Expiration on/after</label>
													</div>
												</div>
											</div>
										</div>
										<div class="col-sm-6 col-xs-12">
											<div class="form-group">
												<div class="form-label-group mb-2">
													<div class="input-group dateFieldHolder">
														<input type="text" name="fExpirationDateTo" id="fExpirationDateTo" value="" class="form-control dateControl">
														<div class="input-group-append">
															<span class="input-group-text cursor-pointer calendar-button" data-target="fExpirationDateTo"><i class="fa-solid fa-calendar"></i></span>
															<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('fExpirationDateTo');"><i class="fa-solid fa-circle-xmark"></i></a></span>
														</div>
														<label for="fExpirationDateTo">Expiration on/before</label>
													</div>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
						<cfelse>
							<input type="hidden" name="fExpirationDateFrom" id="fExpirationDateFrom" value="">
							<input type="hidden" name="fExpirationDateTo" id="fExpirationDateTo" value="">
						</cfif>
						<div class="row">
							<div class="col-xl-6 col-lg-12">
								<div class="form-row">
									<div class="col-sm-6 col-xs-12">
										<div class="form-group">
											<div class="form-label-group mb-2">
												<select name="fCardStatus" id="fCardStatus" class="form-control">
													<option value="">Good or Failed #local.COFTypePlural#</option>
													<option value="1">Good #local.COFTypePlural#</option>
													<option value="0">Failed #local.COFTypePlural#</option>
												</select>
												<label for="fCardStatus">#local.COFType# Status</label>
											</div>
										</div>
									</div>
									<div class="col-sm-6 col-xs-12">
										<div class="form-group">
											<div class="form-label-group mb-2">
												<select name="fCardAssocTo" id="fCardAssocTo" class="form-control form-control-sm" multiple="yes" data-toggle="custom-select2" placeholder="Associated To" onchange="cardAssocFilterChange();">
													<option value="Subscriptions">Subscriptions</option>
													<option value="Contributions">Contributions</option>
													<option value="Invoices">Invoices</option>
													<option value="NotAssoc">Not associated with Anything</option>
												</select>	
												<label for="fCardAssocTo">#local.COFType# Associated With</label>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>	
						<div class="col-auto text-right">
							<button type="button" name="btnResetCOF" class="btn btn-sm btn-secondary" onclick="resetCardFilters();">Clear Filters</button>
							<button type="button" id="btnFilterCOF" class="btn btn-sm btn-primary" onclick="filterCardsOnFile();">Filter #local.COFTypePlural#</button>
						</div>
					</div>
					</form>
				</div>
			</div>
		</div>
	</div>

	<div id="divGridContainer">
		<table id="cardsOnFileTable" class="table table-sm table-striped table-bordered" style="width:100%">
			<thead>
				<tr>
					<th>Member Details</th>
					<th>#local.COFType# Details</th>
					<th>#local.COFType# Association</th>
				</tr>
			</thead>
		</table>
	</div>
</div>
</cfoutput>
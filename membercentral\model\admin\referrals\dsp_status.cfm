<cfoutput>
<cfsavecontent variable="local.pageJS">
	<script language="javascript">
		let refStatusListTable;

		function refreshStatusGrid() { 
			refStatusListTable.draw();
			loadRefStatusMissMsg();
		}
		function editStatus(sID){
			MCModalUtils.showModal({
				isslideout: true,
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				size: 'lg',
				title: sID == 0 ? 'Add Status' : 'Edit Status',
				iframe: true,
				contenturl: '#this.link.editStatus#&statusID=' + sID,
				strmodalfooter: {
					classlist: 'd-flex',
					showclose: true,
					showextrabutton: true,
					extrabuttonclass: 'btn-primary ml-auto',
					extrabuttononclickhandler: '$("##MCModalBodyIframe")[0].contentWindow.validateStatus',
					extrabuttonlabel: 'Save'
				}
			});
		}
		function toggleStatus(sID) {
			var toggleResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true'){
					refreshStatusGrid();
				} else {
					alert('We ran into an error while toggling the status. Try again.');
				}
			};
			var objParams = { statusID:sID };
			TS_AJX('ADMINREFERRALS','toggleStatus',objParams,toggleResult,toggleResult,10000,toggleResult);
		}
		function initRefStatusTable(){
			refStatusListTable = $('##refStatusListTable').DataTable({
				"processing": true,
				"serverSide": true,
				"info": false,
				"pageLength": 50,
				"language": {
					"emptyTable": "No Referral Statuses Found."
				},
				"ajax": { 
					"url": "#local.referralStatusList#",
					"type": "post"
				},
				"autoWidth": false,
				"columns": [
					{
						"data": null,
						"render": function ( data, type, row, meta ) {
							let renderData = '';
							if (type === 'display') {
								renderData += '<div class="d-flex">' ;
								renderData += '<div>' ;
								renderData += '<div>' + data.name ;
								if(data.isActive == 0)
									renderData += '<span class="badge badge-warning ml-1">Inactive</span>';
								renderData += '</div>';
								renderData += '<div class="text-muted">' + data.primaryStatusName +'</div>';
								renderData += '</div>' ;
								renderData += '<div class="d-flex ml-auto">' ;
								
								if(data.isAgency)
									renderData += '<span class="badge badge-dark ml-1">Agency</span>';
								if(data.dspFrontEnd)
									renderData += '<span class="badge badge-info ml-1">Display in Referral Center</span>';
								if(data.isReferred)
									renderData += '<span class="badge badge-info ml-1">Referral Sent</span>';
								if(data.isPending)
									renderData += '<span class="badge badge-info ml-1">Is Pending</span>';
								if(data.isClosedByLawyer)
									renderData += '<span class="badge badge-info ml-1">Is Closed by lawyer</span>';
								if(data.isRetainedCase)
									renderData += '<span class="badge badge-info ml-1">Retained Case</span>';
								if(data.isConsultation)
									renderData += '<span class="badge badge-info ml-1">Is Consultation</span>';
								if(data.isDeleted)
									renderData += '<span class="badge badge-danger ml-1">Deleted</span>';
								
								renderData += '</div>' ;
								renderData += '</div>' ;
							}
							return type === 'display' ? renderData : data;
						},
						"className": "align-top",
					},
					{
						"data": null,
						"render": function ( data, type, row, meta ) {
							let renderData = '';
							if (type === 'display') {
								renderData += '<a href="##" class="btn btn-xs text-primary p-1 m-1" title="Edit Status" onclick="editStatus('+data.statusID+');return false;"><i class="fa-solid fa-pencil"></i></a>';
								renderData += '<a href="##" class="btn btn-xs text-primary p-1 m-1" onclick="toggleStatus('+data.statusID+');return false;" title="'+(data.isActive ? 'Deactivate' : 'Activate')+' Status"><i class="fa-solid fa-shuffle"></i></a>';
							}
							return type === 'display' ? renderData : data;
						},
						"orderable": false,
						"className": "text-center",
						"width": "15%"
					}
				],
				"searching": true
			});
		}
		function loadRefStatusMissMsg(){
			var showRefMissMsg	= function(r) {
				if (r.success && r.success.toLowerCase() == 'true'){
					$('##refStatusMissAlertMsg').html(r.msg);
					$('##refStatusMissAlert').removeClass('d-none');
				} else {
					alert('We were unable to fetch this statuses.');
				}
			};

			var objParams = { referralID:#local.referralID# };
			TS_AJX('ADMINREFERRALS','checkReferralStatus',objParams,showRefMissMsg,showRefMissMsg,10000,showRefMissMsg);
		}
		$(function() {
			initRefStatusTable();
			loadRefStatusMissMsg();
		});
	</script>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.pageJS)#">

<h4>Statuses</h4>
<div class="toolButtonBar">
	<div><a href="javascript:editStatus(0);" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="" data-original-title="Click to add a new status."><i class="fa-regular fa-circle-plus"></i> Add Status</a></div>
</div>

<div class="d-none mt-3" id="refStatusMissAlert">
	<div class="alert alert-warning">
		<h5>Below Referral Status(es) are required.</h5>
		<div id="refStatusMissAlertMsg">
		</div>
	</div>
</div>

<div class="row">
	<div class="col">
		<table id="refStatusListTable" class="table table-sm table-striped table-bordered" style="width:100%">
			<thead>
				<tr>
					<th>Status</th>
					<th>Tools</th>
				</tr>
			</thead>
		</table>
	</div>
</div>
</cfoutput>
﻿<cfsavecontent variable="local.statusFormJS">
	<cfoutput>
	<script type="text/javascript">
		function primaryStatusSelection() {
			var primaryStatus = $('##primaryStatus').val();
			
			if(primaryStatus == 'open' || primaryStatus == 'closed') {
				$('##sectionDisplayInReferralCenter,##sectionisConsultation').show();
			} else {
				$('##sectionDisplayInReferralCenter,##sectionisConsultation').hide();
			}
			
			if(primaryStatus == 'closed') {
				$('##sectionCaseRetained,##sectionHasReferralSent,##sectionClosedByMember,##sectionReopenByMember').show();
			} else {
				$('##sectionCaseRetained,##sectionHasReferralSent,##sectionClosedByMember,##sectionReopenByMember').hide();
			}
		}

		function validateStatus(){
			var errorMsg = "";
			mca_hideAlert('err_status');
			
			top.$('##btnMCModalSave').prop('disabled',true);
			
			var chkResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true') {
					if (r.html && r.html.toString().toLowerCase() == 'true') {	
						<cfif not val(local.statusID)>
							if( $.trim($('##primaryStatus option:selected').val()).length == 0) {
								errorMsg += 'Select a primary status.<br>';
							}
						</cfif>
						if( $.trim($('##statusName').val()).length == 0) {
							errorMsg += 'Enter a valid status name.<br>';
						}
						if( $.trim($('##statusCode').val()).length > 0) {
							var importCode = $.trim($('##statusCode').val());
							var statusID = $('##statusID').val();
							
							if(/^[a-zA-Z0-9]*$/.test(importCode) == false){
								errorMsg += 'Status Code should contain only letters and numbers.<br>';
							}
						} else {
							errorMsg += 'Enter a valid status code.<br>';
						}
						<cfif not val(local.statusID)>
							primaryStatusSelection();
						</cfif>
						if (errorMsg.length > 0) {
							mca_showAlert('err_status', errorMsg);
							top.$('##btnMCModalSave').prop('disabled',false);
						} else {
							$('##statusFrm').submit();
						}
					} else {
						<cfif not val(local.statusID)>
							primaryStatusSelection();
						</cfif>
						mca_showAlert('err_status', r.msg);
						top.$('##btnMCModalSave').prop('disabled',false);
					}
				}
			};
			var objParams = { referralID:$('##referralID').val(), statusName:$('##statusName').val(), statusID:$('##statusID').val(), statusCode:$('##statusCode').val() };
			TS_AJX('ADMINREFERRALS','checkStatusNameStatusCode',objParams,chkResult,chkResult,10000,chkResult);
		}

		$(function() {
			primaryStatusSelection();
		});
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.statusFormJS)#" />

<cfoutput>
<form name="statusFrm" id="statusFrm" class="p-3" method="post" action="#local.saveFormLink#">
	<input type="hidden" name="statusID" id="statusID" value="#local.statusID#">
	<input type="hidden" name="referralID" id="referralID" value="#local.referralID#">
	
	<div id="err_status" class="alert alert-danger d-none"></div>

	<div class="form-group">
		<div class="form-label-group">
			<select id="primaryStatus" name="primaryStatus" class="form-control" <cfif val(local.statusID)>disabled<cfelse>onchange="primaryStatusSelection();"</cfif>>
				<option value="">Select Primary Status</option>
				<option value="pendingreferralsent" <cfif local.primaryStatus eq "pendingreferralsent">selected</cfif>>Pending - Referral Sent</option>
				<option value="pendingreferralnotsent"<cfif local.primaryStatus eq "pendingreferralnotsent">selected</cfif>>Pending - Referral NOT Sent</option>
				<option value="open"<cfif local.primaryStatus eq "open">selected</cfif>>Open</option>
				<option value="closed"<cfif local.primaryStatus eq "closed">selected</cfif>>Closed</option>
				<option value="closedreferraltoagency"<cfif local.primaryStatus eq "closedreferraltoagency">selected</cfif>>Closed - Referral to Agency</option>
				<option value="deleted"<cfif local.primaryStatus eq "deleted">selected</cfif>>Deleted</option>
			</select>
			<label for="primaryStatus">Primary Status *</label>
		</div>
	</div>
	<div class="form-group">
		<div class="form-label-group">
			<input type="text" name="statusName" id="statusName" class="form-control" value="#htmleditformat(local.statusName)#" maxlength="255">
			<label for="statusName">Status Name *</label>
		</div>
	</div>
	<div class="form-group">
		<div class="form-label-group">
			<input type="text" name="statusCode" id="statusCode" class="form-control" value="#htmleditformat(local.statusCode)#" maxlength="25">
			<label for="statusCode">Status Code *</label>
		</div>
	</div>
	<div id="sectionDisplayInReferralCenter" class="form-group">
		<div class="custom-control custom-switch">
			<input type="checkbox" name="dspFrontEnd" id="dspFrontEnd" value="1" <cfif val(local.dspFrontEnd)>checked="checked"</cfif> class="custom-control-input"/>
			<label class="custom-control-label" for="dspFrontEnd">Display in Referral Center?</label>
		</div>
	</div>
	<div id="sectionCaseRetained" class="form-group">
		<div class="custom-control custom-switch">
			<input type="checkbox" name="isRetainedcase" id="isRetainedcase" value="1" <cfif val(local.isRetainedcase)>checked="checked"</cfif> class="custom-control-input"/>
			<label class="custom-control-label" for="isRetainedcase">Case has been retained?</label>
		</div>
	</div>
	<div id="sectionClosedByMember" class="form-group">
		<div class="custom-control custom-switch">
			<input type="checkbox" name="isClosedByLawyer" id="isClosedByLawyer" value="1" <cfif val(local.isClosedByLawyer)>checked="checked"</cfif> class="custom-control-input"/>
			<label class="custom-control-label" for="isClosedByLawyer">Can be closed by member in Referral Center?</label>
		</div>
	</div>
	<div id="sectionReopenByMember" class="form-group">
		<div class="custom-control custom-switch">
			<input type="checkbox" name="cannotReopen" id="cannotReopen" value="1" <cfif val(local.qryGetStatus.cannotReopen)>checked="checked"</cfif> class="custom-control-input">
			<label class="custom-control-label" for="cannotReopen">Cannot be reopened by member in referral center?</label>
		</div>
	</div>
	<div id="sectionHasReferralSent" class="form-group">
		<div class="custom-control custom-switch">
			<input type="checkbox" name="isReferred" id="isReferred" value="1" <cfif val(local.isReferred)>checked="checked"</cfif> class="custom-control-input"/>
			<label class="custom-control-label" for="isReferred">Has referral been sent?</label>
		</div>
	</div>
	<div class="form-group">
		<div class="custom-control custom-switch">
			<input type="checkbox" name="isActive" id="isActive" value="1" <cfif val(local.isActive)>checked="checked"</cfif> class="custom-control-input"/>
			<label class="custom-control-label" for="isActive">Is Active?</label>
		</div>
	</div>
	<div id="sectionisConsultation" class="form-group">
		<div class="custom-control custom-switch">
			<input type="checkbox" name="isConsultation" id="isConsultation" value="1" <cfif val(local.isConsultation)>checked="checked"</cfif> class="custom-control-input"/>
			<label class="custom-control-label" for="isConsultation">Is this Consultation?</label>
		</div>
	</div>
	<button type="submit" class="d-none">Save</button>
</form>
</cfoutput>	
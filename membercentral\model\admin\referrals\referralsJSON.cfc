<cfcomponent extends="model.admin.admin" output="false">
	<cfset variables.defaultEvent = 'controller'>
	
	<cffunction name="controller" access="public" output="false" returntype="string" hint="controller for this app">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			// RUN ASSIGNED METHOD --------------------------------------------------------------------- ::
			local.methodToRun = this[arguments.event.getValue('meth')];

			// PASS THE ARGUMENT COLLECTION TO THE CURRENT METHOD AND EXECUTE IT. ----------------------- ::
			return local.methodToRun(arguments.event);
		</cfscript>
	</cffunction>

	<cffunction name="getPanelList" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfset local = structNew()/>
		<cfset local.referralID = arguments.event.getValue('referralID',0)>

		<cfquery name="local.arrPanels" datasource="#application.dsn.memberCentral.dsn#" returntype="array">
			SET NOCOUNT ON;
			IF OBJECT_ID('tempdb..##tmpPanel') IS NOT NULL 
				DROP TABLE ##tmpPanel;
			IF OBJECT_ID('tempdb..##tmpPanelNotLinked') IS NOT NULL 
				DROP TABLE ##tmpPanelNotLinked;
			CREATE TABLE ##tmpPanel (panelID int PRIMARY KEY, name varchar(255), panelParentID int, siteResourceID int, statusName varchar(255), shortDesc varchar(255), thePath varchar(1000), thePathExpanded varchar(max));
			CREATE TABLE ##tmpPanelNotLinked (panelID int PRIMARY KEY, name varchar(255), panelParentID int, siteResourceID int, statusName varchar(255), shortDesc varchar(255), thePath varchar(1000), thePathExpanded varchar(max));
			
			DECLARE @referralID int;
			SET @referralID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.referralID#">;
			
			;WITH Panels AS (
				SELECT panelID, name, panelParentID, siteResourceID, statusName, shortDesc,
					CAST(RIGHT('100000'+theRow,4) as varchar(max)) AS thePath,
					CAST(name as varchar(max)) as thePathExpanded
				FROM (
					SELECT p.panelID, p.name, p.panelParentID, p.siteResourceID, ps.statusName, p.shortDesc,
						ROW_NUMBER() OVER (ORDER BY p.name) AS theRow
					FROM dbo.ref_panels p
					INNER JOIN dbo.ref_panelStatus ps on ps.panelStatusID = p.statusID 
						AND ps.referralID = @referralID
						AND ps.statusName <> 'Deleted'
					WHERE p.referralID = @referralID
					AND p.panelParentID IS NULL
				) AS x

				UNION ALL

				SELECT panelID, name, panelParentID, siteResourceID, statusName, shortDesc,
					thePath + '.' + CAST(RIGHT('100000'+theRow,4) as varchar(max)) AS thePath,
					thePathExpanded + ' \ ' + name as thePathExpanded
				FROM (
					SELECT p.panelID, p.name, p.panelParentID ,p.siteResourceID, ps.statusName, p.shortDesc,
						pan.thePath, pan.thePathExpanded, ROW_NUMBER() OVER (ORDER BY p.name) AS theRow
					FROM dbo.ref_panels p
					INNER JOIN dbo.ref_panelStatus ps on ps.panelStatusID = p.statusID 
						AND ps.referralID = @referralID
						AND ps.statusName <> 'Deleted'
					INNER JOIN Panels as pan ON p.panelParentID = pan.panelID
					WHERE p.referralID = @referralID
				) AS y
			)

			INSERT INTO ##tmpPanel (panelID, name, panelParentID,siteResourceID,statusName,shortDesc,thePath,thePathExpanded)
			SELECT panelID, name, panelParentID, siteResourceID, statusName, shortDesc, thePath, thePathExpanded
			FROM Panels
			ORDER BY name;

			INSERT INTO ##tmpPanelNotLinked (panelID, name, panelParentID,siteResourceID,statusName,shortDesc,thePath,thePathExpanded)
			SELECT DISTINCT p.panelID, p.name, p.panelParentID,p.siteResourceID,p.statusName,p.shortDesc,p.thePath,p.thePathExpanded
			FROM ##tmpPanel p
			LEFT JOIN ref_panelMembers pm ON pm.panelID= p.panelID
			WHERE pm.panelID is null;

			SELECT DISTINCT *, (select count(pnl.panelID) FROM ##tmpPanelNotLinked pnl where panelParentID=tmp.panelID) as subPanelCount ,'panelRow_' + CAST(panelID AS varchar(10)) AS DT_RowId FROM  (
				SELECT DISTINCT p.panelID, p.name, p.panelParentID,p.statusName,p.shortDesc,p.thePath,p.thePathExpanded,1 as isdelete
				FROM ##tmpPanelNotLinked p
				UNION
				SELECT DISTINCT p.panelID, p.name, p.panelParentID,p.statusName,p.shortDesc,p.thePath,p.thePathExpanded,0 as isdelete
				FROM ##tmpPanel p 
				INNER JOIN 
				(
					SELECT DISTINCT p.panelParentID
					FROM ##tmpPanelNotLinked p
					WHERE p.panelParentID IS NOT NULL and p.panelParentID not in (SELECT DISTINCT p.panelID
					FROM ##tmpPanelNotLinked p)
				) tmpInner on tmpInner.panelParentID = p.panelID
			) tmp 
			ORDER BY tmp.thePath;

			IF OBJECT_ID('tempdb..##tmpPanel') IS NOT NULL 
				DROP TABLE ##tmpPanel;
			IF OBJECT_ID('tempdb..##tmpPanelNotLinked') IS NOT NULL 
				DROP TABLE ##tmpPanelNotLinked;
		</cfquery>

		<cfset local.returnStruct = {
			"success": true,
			"data": local.arrPanels
		}>

		<cfreturn serializeJSON(local.returnStruct)>
	</cffunction>

	<cffunction name="getCases" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			local.objAdminReferrals	= CreateObject("component","model.admin.referrals.referrals");
			local.objReferrals	= CreateObject("component","model.referrals.referrals");

			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'asc');
			arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 1)));
			arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
			arguments.event.setValue('count',int(val(arguments.event.getValue('length',10))));
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));

			// Set the request timeout to 300 seconds
			setting requesttimeout = 300;

			local.retainedCaseFilter = StructNew();
			if(arguments.event.getTrimValue('isSearch','') neq ''){
				local.retainedCaseFilter.firstName = arguments.event.getTrimValue('firstName','');
				local.retainedCaseFilter.lastName = arguments.event.getTrimValue('lastName','');
				local.retainedCaseFilter.email = arguments.event.getTrimValue('email','');
				local.retainedCaseFilter.repFirstName = arguments.event.getTrimValue('repFirstName','');
				local.retainedCaseFilter.repLastName = arguments.event.getTrimValue('repLastName','');
				local.retainedCaseFilter.memberFirstName = arguments.event.getTrimValue('memberFirstName','');
				local.retainedCaseFilter.memberLastName = arguments.event.getTrimValue('memberLastName','');
				local.retainedCaseFilter.referralDateFrom = arguments.event.getTrimValue('referralDateFrom','');
				local.retainedCaseFilter.referralDateTo = arguments.event.getTrimValue('referralDateTo','');
				local.retainedCaseFilter.caseDateFrom = arguments.event.getTrimValue('caseDateFrom','');
				local.retainedCaseFilter.caseDateTo = arguments.event.getTrimValue('caseDateTo','');
				local.retainedCaseFilter.noteFollowUpDateFrom = arguments.event.getTrimValue('noteFollowUpDateFrom','');
				local.retainedCaseFilter.noteFollowUpDateTo = arguments.event.getTrimValue('noteFollowUpDateTo','');
				local.retainedCaseFilter.statusID = arguments.event.getTrimValue('statusID','');
				local.retainedCaseFilter.followUpStatus = arguments.event.getTrimValue('followUpStatus','');
				local.retainedCaseFilter.hasDocuments =arguments.event.getTrimValue('hasDocuments',0);
				local.retainedCaseFilter.feeDiscrepancyStatusID =arguments.event.getTrimValue('feeDiscrepancyStatusID',0);
				local.retainedCaseFilter.counselorID = arguments.event.getTrimValue('counselorID','');
				application.mcCacheManager.sessionSetValue(keyname='retainedCaseFilter', value=local.retainedCaseFilter);
			}
		</cfscript>

		<cfset local.arrCols = arrayNew(1)>
		<cfset arrayAppend(local.arrCols,"cr.clientReferralID")>
		<cfset arrayAppend(local.arrCols,"c.lastName + ', ' + c.firstName")>
		<cfset arrayAppend(local.arrCols,"m.firstName + isnull(' ' + left(nullif(m.middleName,''),1),'') + ' ' + m.lastName")>
		<cfset arrayAppend(local.arrCols,"crs.statusName")>
		<cfset local.orderby = local.arrcols[arguments.event.getValue('orderby')+1]>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryGetClientCases" result="local.qryCasesResult">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				DECLARE @posStart int, @posStartAndCount int, @totalCount int, @midnight datetime = dateAdd(dd, dateDiff(dd, 0, getDate()), 0),
					@orgID int, @referralID int;
				SET @posStart = <cfqueryparam value="#arguments.event.getValue('posStart')#" cfsqltype="CF_SQL_INTEGER">;
				SET @posStartAndCount = @posStart + <cfqueryparam value="#arguments.event.getValue('count')#" cfsqltype="CF_SQL_INTEGER">;
				SET @orgID =  <cfqueryparam value="#arguments.event.getValue('mc_siteInfo.orgID')#" cfsqltype="cf_sql_integer">;
				SET @referralID = <cfqueryparam value="#arguments.event.getValue('referralID')#" cfsqltype="cf_sql_integer">;

				IF OBJECT_ID('tempdb..##tmpCases') IS NOT NULL 
					DROP TABLE ##tmpCases;
				CREATE TABLE ##tmpCases (caseID int PRIMARY KEY, clientReferralID int, clientName varchar(152), memberName varchar(177), memberID int, 
				statusName varchar(255), row int);

				INSERT INTO ##tmpCases (caseID, clientReferralID, clientName, memberName, memberID, statusName, row)
				select rc.caseID, cr.clientReferralID, c.lastName + ', ' + c.firstName as clientName, 
					m.firstName + isnull(' ' + left(nullif(m.middleName,''),1),'') + ' ' + m.lastName as memberName,
					cr.memberID, crs.statusName,
					ROW_NUMBER() OVER (ORDER BY #preserveSingleQuotes(local.orderby)# #arguments.event.getValue('orderDir')#,cr.clientReferralID) as row
				from dbo.ref_clients as c
				inner join dbo.ref_clientTypes as ct on ct.clientTypeID = c.typeID and ct.clientType = 'Client'				
				inner join dbo.ref_clientReferrals as cr on cr.referralID = @referralID and cr.clientID = c.clientID
				inner join dbo.ref_clientReferralStatus as crs on crs.clientReferralStatusID = cr.statusID
				<cfif arguments.event.valueExists('counselorID') AND len(arguments.event.getValue('counselorID'))>
					inner join dbo.ams_members m2 on m2.memberID = cr.enteredByMemberID and m2.orgID = @orgID
					inner join dbo.ams_members counselor on counselor.memberID = m2.activeMemberID	and counselor.activeMemberID = <cfqueryparam value="#arguments.event.getValue('counselorID')#" cfsqltype="cf_sql_integer" /> and counselor.orgID = @orgID
				<cfelse>
					inner join dbo.ams_members counselor on counselor.memberID = cr.enteredByMemberID
					<cfif not application.objUser.isSuperUser(cfcuser=session.cfcuser)>
						and counselor.orgID = @orgID
					</cfif>
				</cfif>
				<cfif len(arguments.event.getTrimValue('repFirstName','') & arguments.event.getTrimValue('repLastName',''))>
					inner join dbo.ref_clients as rep on rep.referralID = @referralID and rep.clientID = cr.representativeID
					<cfif len(arguments.event.getTrimValue('repFirstName',''))>
						and rep.firstName like <cfqueryparam value="%#arguments.event.getValue('repFirstName')#%" cfsqltype="cf_sql_varchar">
					</cfif>
					<cfif len(arguments.event.getTrimValue('repLastName',''))>
						and rep.lastName like <cfqueryparam value="%#arguments.event.getValue('repLastName')#%" cfsqltype="cf_sql_varchar">
					</cfif>
				</cfif>
				outer apply (
					select m3.firstName, m3.middleName, m3.lastName 
					from dbo.ams_members as m2
					inner join dbo.ams_members as m3 on m3.memberID = m2.activeMemberID
					where m2.memberID = cr.memberID	
					) as m					
				inner join dbo.ref_cases as rc on rc.referralID = @referralID and rc.clientReferralID = cr.clientReferralID
				where c.referralID = @referralID
				<cfif len(arguments.event.getTrimValue('firstName',''))>
					and c.firstName like <cfqueryparam value="%#arguments.event.getValue('firstName')#%" cfsqltype="cf_sql_varchar">
				</cfif>
				<cfif len(arguments.event.getTrimValue('lastName',''))>
					and c.lastName like <cfqueryparam value="%#arguments.event.getValue('lastName')#%" cfsqltype="cf_sql_varchar">
				</cfif>
				<cfif len(arguments.event.getTrimValue('email',''))>
					and c.email like <cfqueryparam value="%#arguments.event.getValue('email')#%" cfsqltype="cf_sql_varchar">
				</cfif>
				<cfif len(arguments.event.getTrimValue('memberFirstName',''))>
					and m.firstName like <cfqueryparam value="%#arguments.event.getValue('memberFirstName')#%" cfsqltype="cf_sql_varchar">
				</cfif>
				<cfif len(arguments.event.getTrimValue('memberLastName',''))>
					and m.lastName like <cfqueryparam value="%#arguments.event.getValue('memberLastName')#%" cfsqltype="cf_sql_varchar">
				</cfif>	
				<cfif arguments.event.valueExists('statusID') AND len(arguments.event.getValue('statusID'))>
					and cr.statusID = <cfqueryparam value="#arguments.event.getValue('statusID')#" cfsqltype="cf_sql_integer">
				</cfif>	
				<cfif arguments.event.valueExists('feeDiscrepancyStatusID') AND len(arguments.event.getValue('feeDiscrepancyStatusID'))>
					and cr.feeDiscrepancyStatusID = <cfqueryparam value="#arguments.event.getValue('feeDiscrepancyStatusID')#" cfsqltype="cf_sql_integer" />
				</cfif>				
				<cfif len(arguments.event.getValue('referralDateFrom',''))>
					and cr.clientReferralDate >= <cfqueryparam value="#arguments.event.getValue('referralDateFrom')#" cfsqltype="cf_sql_date">
				<cfelse>
					<cfif not len(arguments.event.getTrimValue('firstName',''))
						and not len(arguments.event.getTrimValue('lastName',''))
						and not len(arguments.event.getTrimValue('email',''))
						and not len(arguments.event.getTrimValue('repFirstName',''))
						and not len(arguments.event.getTrimValue('repLastName',''))
						and not len(arguments.event.getTrimValue('memberFirstName',''))
						and not len(arguments.event.getTrimValue('memberLastName',''))
						and not len(arguments.event.getTrimValue('statusID',''))
						and not len(arguments.event.getTrimValue('referralDateFrom',''))
						and not len(arguments.event.getTrimValue('referralDateTo',''))
						and not len(arguments.event.getTrimValue('caseDateFrom',''))
						and not len(arguments.event.getTrimValue('caseDateTo',''))
						and not len(arguments.event.getTrimValue('noteFollowUpDateFrom',''))
						and not len(arguments.event.getTrimValue('noteFollowUpDateTo',''))
						and not len(arguments.event.getTrimValue('followUpStatus',''))
						and not len(arguments.event.getValue('counselorID',''))
						and not len(arguments.event.getValue('feeDiscrepancyStatusID',''))>
							and dateAdd(dd, dateDiff(dd, 0, cr.clientReferralDate), 0) = @midnight
					</cfif>
				</cfif>
				<cfif arguments.event.valueExists('referralDateTo') AND len(arguments.event.getValue('referralDateTo'))>
					and cr.clientReferralDate < <cfqueryparam value="#dateAdd("d", 1, arguments.event.getValue('referralDateTo'))#" cfsqltype="cf_sql_date">
				</cfif>				
				<cfif arguments.event.valueExists('caseDateFrom') AND len(arguments.event.getValue('caseDateFrom'))>
					and rc.dateCaseOpened >= <cfqueryparam value="#arguments.event.getValue('caseDateFrom')#" cfsqltype="cf_sql_date">
				</cfif>
				<cfif arguments.event.valueExists('caseDateTo') AND len(arguments.event.getValue('caseDateTo'))>
					and rc.dateCaseOpened < <cfqueryparam value="#dateAdd("d", 1, arguments.event.getValue('caseDateTo'))#" cfsqltype="cf_sql_date">
				</cfif>
				<cfif (arguments.event.valueExists('noteFollowUpDateFrom') AND len(arguments.event.getValue('noteFollowUpDateFrom'))) OR (arguments.event.valueExists('noteFollowUpDateTo') AND len(arguments.event.getValue('noteFollowUpDateTo'))) OR  (arguments.event.valueExists('followUpStatus') AND len(arguments.event.getValue('followUpStatus')))>
					AND cr.clientreferralID IN (SELECT distinct clientreferralID FROM dbo.ref_notes as rn WHERE rn.clientreferralID = cr.clientreferralID and rn.noteType = 'C' 
					<cfif arguments.event.valueExists('followUpStatus') AND len(arguments.event.getValue('followUpStatus'))>
						and rn.followUpStatus = <cfqueryparam value="#arguments.event.getValue('followUpStatus')#" cfsqltype="cf_sql_char">
					</cfif>
					<cfif arguments.event.valueExists('noteFollowUpDateFrom') AND len(arguments.event.getValue('noteFollowUpDateFrom'))>
						and rn.followUpdate >= <cfqueryparam value="#arguments.event.getValue('noteFollowUpDateFrom')#" cfsqltype="cf_sql_date">
					</cfif>
					<cfif arguments.event.valueExists('noteFollowUpDateTo') AND len(arguments.event.getValue('noteFollowUpDateTo'))>
						and rn.followUpdate < <cfqueryparam value="#dateAdd("d", 1, arguments.event.getValue('noteFollowUpDateTo'))#" cfsqltype="cf_sql_date">
					</cfif>
					)
				</cfif>
				<cfif arguments.event.valueExists('hasDocuments') and val(arguments.event.getValue('hasDocuments',0))>
					and exists (select caseDocumentID from dbo.ref_caseDocuments where clientReferralID = cr.clientReferralID)
				</cfif>;

				SELECT @totalCount = @@ROWCOUNT;

				SELECT caseID, clientReferralID, clientName, memberName, memberID, statusName, row, @totalCount as totalCount
				FROM ##tmpCases
				WHERE row > @posStart AND row <= @posStartAndCount
				ORDER BY row;

				IF OBJECT_ID('tempdb..##tmpCases') IS NOT NULL
					DROP TABLE ##tmpCases;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfset local.data = []>
		<cfloop query="local.qryGetClientCases">
			<cfset local.qryGetCaseFees = local.objAdminReferrals.getCaseFees(caseID=local.qryGetClientCases.caseID)>
			<cfset local.qryGetFeesTotals = local.objReferrals.getFeesTotals(qryItems=local.qryGetCaseFees)>		

			<cfset local.tmpStr = {
				"clientreferralid": local.qryGetClientCases.clientReferralID,
				"clientname": local.qryGetClientCases.clientName,
				"membername":  local.qryGetClientCases.memberName,
				"memberid": local.qryGetClientCases.memberID,
				"statusname": local.qryGetClientCases.statusName,
				"collectedfees": dollarFormat(local.qryGetFeesTotals.collectedFeeTotal),
				"referraldues": dollarFormat(local.qryGetFeesTotals.referralDuesTotal),
				"totalamttobepaid": dollarFormat(local.qryGetFeesTotals.amtToBePaidTotal),
				"DT_RowId": "clientRefRow_#local.qryGetClientCases.clientReferralID#"
			}>

			<cfset arrayAppend(local.data, local.tmpStr)>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": val(local.qryGetClientCases.totalCount),
			"recordsFiltered": val(local.qryGetClientCases.totalCount),
			"data": local.data
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getReferrals" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			local.objAdminReferrals	= CreateObject("component","model.admin.referrals.referrals");
			local.objReferrals	= CreateObject("component","model.referrals.referrals");

			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'asc');
			arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 1)));
			arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
			arguments.event.setValue('count',int(val(arguments.event.getValue('length',10))));
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));

			setting requesttimeout = 300;
			local.referralFilter = StructNew();
			if(arguments.event.getTrimValue('isSearch','') neq ''){
				local.referralFilter.firstName = arguments.event.getTrimValue('firstName','');
				local.referralFilter.lastName = arguments.event.getTrimValue('lastName','');
				local.referralFilter.email = arguments.event.getTrimValue('email','');
				local.referralFilter.repFirstName = arguments.event.getTrimValue('repFirstName','');
				local.referralFilter.repLastName = arguments.event.getTrimValue('repLastName','');
				local.referralFilter.memberFirstName = arguments.event.getTrimValue('memberFirstName','');
				local.referralFilter.memberLastName = arguments.event.getTrimValue('memberLastName','');
				local.referralFilter.phoneNumber = arguments.event.getTrimValue('phoneNumber','');
				local.referralFilter.referralNum = arguments.event.getTrimValue('referralNum','');
				local.referralFilter.statusID = arguments.event.getTrimValue('statusID','');
				local.referralFilter.followUpStatus = arguments.event.getTrimValue('followUpStatus','');
				local.referralFilter.counselorID = arguments.event.getTrimValue('counselorID','');
				local.referralFilter.importClientReferralID = arguments.event.getTrimValue('importClientReferralID','');
				local.referralFilter.referralDateFrom = arguments.event.getTrimValue('referralDateFrom','');
				local.referralFilter.referralDateTo = arguments.event.getTrimValue('referralDateTo','');
				local.referralFilter.callDateFrom = arguments.event.getTrimValue('callDateFrom','');
				local.referralFilter.callDateTo = arguments.event.getTrimValue('callDateTo','');
				local.referralFilter.callUID = arguments.event.getTrimValue('callUID','');
				local.referralFilter.isTransferred =arguments.event.getTrimValue('isTransferred',0);
				local.referralFilter.transferDateFrom =arguments.event.getTrimValue('transferDateFrom','');
				local.referralFilter.transferDateTo =arguments.event.getTrimValue('transferDateTo','');
				local.referralFilter.hasDocuments =arguments.event.getTrimValue('hasDocuments',0);
				local.referralFilter.noteFollowUpDateFrom = arguments.event.getTrimValue('noteFollowUpDateFrom','');
				local.referralFilter.noteFollowUpDateTo = arguments.event.getTrimValue('noteFollowUpDateTo','');
				local.referralFilter.feeDiscrepancyStatusID =arguments.event.getTrimValue('feeDiscrepancyStatusID',0);
				application.mcCacheManager.sessionSetValue(keyname='referralFilter', value=local.referralFilter);
			}
			local.qryGetClientReferrals = CreateObject("component","model.admin.referrals.referrals").getClientReferralsFromFilters(event=arguments.event);	
		</cfscript>

		<cfset local.data = []>
		<cfloop query="local.qryGetClientReferrals">
			<cfset local.tmpStr = {
				"clientreferralid": local.qryGetClientReferrals.clientreferralid,
				"clientname": local.qryGetClientReferrals.clientname,
				"lawyername": local.qryGetClientReferrals.lawyername,
				"memberid": local.qryGetClientReferrals.memberid,
				"isdeleted": local.qryGetClientReferrals.isdeleted,
				"islawyerreferral": local.qryGetClientReferrals.islawyerreferral,
				"statusname": local.qryGetClientReferrals.statusname,
				"clientreferraldate": DateFormat(local.qryGetClientReferrals.clientreferraldate,"m/d/yyyy"),
				"DT_RowId": "clientRefRow_#local.qryGetClientReferrals.clientReferralID#"
			}>
			<cfset arrayAppend(local.data, local.tmpStr)>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": val(local.qryGetClientReferrals.totalCount),
			"recordsFiltered": val(local.qryGetClientReferrals.totalCount),
			"data": local.data
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getClients" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();

			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'desc');
			arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 3)));
			arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
			arguments.event.setValue('count',int(val(arguments.event.getValue('length',10))));
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));

			setting requesttimeout = 300;
			local.clientFilter = StructNew();
			local.clientFilter.firstName = arguments.event.getTrimValue('firstName','');
			local.clientFilter.lastName = arguments.event.getTrimValue('lastName','');
			local.clientFilter.email = arguments.event.getTrimValue('email','');
			local.clientFilter.repFirstName = arguments.event.getTrimValue('repFirstName','');
			local.clientFilter.repLastName = arguments.event.getTrimValue('repLastName','');
			local.clientFilter.dateCreated = arguments.event.getTrimValue('dateCreated','');
			local.clientFilter.dateCreatedTo = arguments.event.getTrimValue('dateCreatedTo','');
			local.clientFilter.phoneNumber = arguments.event.getTrimValue('phoneNumber','');
			local.clientFilter.callUID = arguments.event.getTrimValue('callUID','');		
			application.mcCacheManager.sessionSetValue(keyname='clientFilter', value=local.clientFilter);
			local.qryGetClients = CreateObject("component","model.admin.referrals.referrals").getClientsFromFilter(event=arguments.event);	
		</cfscript>

		<cfset local.data = []>
		<cfloop query="local.qryGetClients">
			<cfset local.tmpStr = {
				"clientid": local.qryGetClients.clientid,
				"clientname": local.qryGetClients.clientname,
				"email": local.qryGetClients.email,
				"phoneno": local.qryGetClients.phoneno,
				"calldate": DateFormat(local.qryGetClients.calldate,"m/d/yyyy"),
				"DT_RowId": "clientRow_#local.qryGetClients.clientid#"
			}>
			<cfset arrayAppend(local.data, local.tmpStr)>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": val(local.qryGetClients.totalCount),
			"recordsFiltered": val(local.qryGetClients.totalCount),
			"data": local.data
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getClientLogs" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();

			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'desc');
			arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 1)));
			arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
			arguments.event.setValue('count',int(val(arguments.event.getValue('length',10))));
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));

			local.qryGetClientLogs = CreateObject("component","model.admin.referrals.referrals").getBlockedClientLog(event=arguments.event);	
		</cfscript>

		<cfset local.data = []>
		<cfloop query="local.qryGetClientLogs">
			<cfset local.tmpStr = {
				"clientlogid": local.qryGetClientLogs.clientlogid,
				"clientid": local.qryGetClientLogs.clientid,
				"clientname": local.qryGetClientLogs.clientname,
				"logdate": DateTimeFormat(local.qryGetClientLogs.logdate,"m/d/yyyy h:nn tt"),
				"DT_RowId": "clientLogRow_#local.qryGetClientLogs.clientlogid#"
			}>
			<cfset arrayAppend(local.data, local.tmpStr)>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": val(local.qryGetClientLogs.totalCount),
			"recordsFiltered": val(local.qryGetClientLogs.totalCount),
			"data": local.data
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getScheduleJobList" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'asc');
			arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 0)));
			arguments.event.setValue('fSchedJobName',form['search[value]'] ?: '');
			local.qryScheduleJobs = CreateObject("component","model.admin.referrals.referrals").getScheduleJobs(event=arguments.event, mode="schedJobTable");
		</cfscript>
		
		<cfset local.data = []>
		<cfloop query="local.qryScheduleJobs">
			<cfset arrayAppend(local.data, {
				"name": local.qryScheduleJobs.name,
				"templateName": local.qryScheduleJobs.templateName,
				"frequencyName": local.qryScheduleJobs.frequencyName,
				"nextRunDate": dateTimeFormat(local.qryScheduleJobs.nextRunDate,"m/d/yyyy hh:nn tt"),
				"scheduleReportID": local.qryScheduleJobs.scheduleReportID,
				"isActive": local.qryScheduleJobs.isActive,
				"DT_RowId": "scheduleJobsRow_#local.qryScheduleJobs.scheduledReportTypeID#_#local.qryScheduleJobs.scheduleReportID#"
			})>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": val(local.qryScheduleJobs.recordcount),
			"recordsFiltered": val(local.qryScheduleJobs.recordcount),
			"data": local.data
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getReferralCaseDocuments" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'desc');
			arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 1)));
			arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
			arguments.event.setValue('count',int(val(arguments.event.getValue('length',10))));
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));
			arguments.event.setValue('search',form['search[value]'] ?: '');
			local.qryGetReferralCaseDocuments = CreateObject("component","model.admin.referrals.referrals").getReferralCaseDocuments(event=arguments.event);	
		</cfscript>
		
		<cfset local.data = []>
		<cfloop query="local.qryGetReferralCaseDocuments">
			<cfswitch expression="#local.qryGetReferralCaseDocuments.fileExt#">
				<cfcase value="doc,docx" delimiters=","><cfset local.fileIcon = "fa-file-word"></cfcase>
				<cfcase value="xls,xlsx" delimiters=","><cfset local.fileIcon = "fa-file-excel"></cfcase>
				<cfcase value="ppt,pptx" delimiters=","><cfset local.fileIcon = "fa-file-powerpoint"></cfcase>
				<cfcase value="gif,png,jpg,jpeg" delimiters=","><cfset local.fileIcon = "fa-file-image"></cfcase>
				<cfcase value="html"><cfset local.fileIcon = "fa-file-code"></cfcase>
				<cfcase value="pdf"><cfset local.fileIcon = "fa-file-pdf"></cfcase>
				<cfcase value="txt"><cfset local.fileIcon = "fa-file-lines"></cfcase>
				<cfdefaultcase><cfset local.fileIcon = "fa-file"></cfdefaultcase>
			</cfswitch>
			<cfset local.tmpStr = {
				"itemID": local.qryGetReferralCaseDocuments.itemID,
				"docSection": local.qryGetReferralCaseDocuments.docSection,
				"documentID": local.qryGetReferralCaseDocuments.documentID,
				"documentVersionID": local.qryGetReferralCaseDocuments.documentVersionID,
				"languageCode": local.qryGetReferralCaseDocuments.languageCode,
				"docTitle": local.qryGetReferralCaseDocuments.docTitle,
				"fileName": local.qryGetReferralCaseDocuments.fileName,
				"icon": local.fileIcon,
				"dateModified": dateTimeFormat(local.qryGetReferralCaseDocuments.dateModified,"m/d/yyyy h:nn tt")
			}>
			<cfset arrayAppend(local.data, local.tmpStr)>
		</cfloop>
		
		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": val(local.qryGetReferralCaseDocuments.totalCount),
			"recordsFiltered": val(local.qryGetReferralCaseDocuments.totalCount),
			"data": local.data
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getSelectedReferralPanelsOfReport" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));
		</cfscript>
		
		<cfquery name="local.qryReferralPanels" datasource="#application.dsn.membercentral.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				DECLARE @siteID int, @referralID int, @itemID int, @controllingSRID int, @extraNodeName varchar(30), @panelIDList varchar(max);

				set @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteid')#">;
				set @itemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('_refitemid')#">;
				set @controllingSRID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('_refcsrid')#">;
				set @extraNodeName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('_refextnodename')#">;

				SELECT TOP 1 @referralID = r.referralID
				FROM dbo.ref_referrals r
				INNER JOIN dbo.cms_applicationInstances ai on ai.siteID = @siteID
					AND ai.applicationInstanceID = r.applicationInstanceID
				INNER JOIN dbo.cms_siteResources sr on sr.siteID = @siteID
					AND sr.siteResourceID = ai.siteResourceID 
					AND sr.siteResourceStatusID = 1;

				<cfif arguments.event.getValue('_refitemid') GT 0>
					<cfswitch expression="#arguments.event.getValue('_refmode','report')#">
						<cfcase value="dashboard">
							select @panelIDList = objectDataXML.value('(/obj/*[local-name()=sql:variable("@extraNodeName")]/text())[1]','varchar(max)')
							from dbo.rpt_dashboardObjects
							where objectID = @itemID;
						</cfcase>
					</cfswitch>
				<cfelse>
					SET @panelIDList = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getTrimValue('newPanelIDs','')#">;
				</cfif>

				select p.panelID, p.thePathExpanded
				from dbo.fn_getRecursiveReferralPanels(@referralID,null) as p
				inner join dbo.fn_intListToTable(isnull(@panelIDList,'0'),',') dg on dg.listitem = p.panelID
				order by p.thePathExpanded;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;		
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfset local.arrData = []>
		<cfloop query="local.qryReferralPanels">
			<cfset local.arrData.append({
				"panelID": local.qryReferralPanels.panelID,
				"paneltreepath": encodeForHTML(local.qryReferralPanels.thePathExpanded),
				"DT_RowId": "panelRow_#local.qryReferralPanels.panelID#"
			})>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": local.qryReferralPanels.recordCount,
			"recordsFiltered": local.qryReferralPanels.recordCount,
			"data": local.arrData
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getReferralPanelsForReports" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'asc');
			arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 1)));
			arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
			arguments.event.setValue('count',int(val(arguments.event.getValue('length',10))));
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));
			local.searchValue = form['search[value]'] ?: '';
		</cfscript>
		
		<cfquery name="local.qryReferralPanels"  datasource="#application.dsn.membercentral.dsn#" >
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			IF OBJECT_ID('tempdb..##tmpReferralPanels') IS NOT NULL
				DROP TABLE ##tmpReferralPanels;
			CREATE TABLE ##tmpReferralPanels (panelID int, thePathExpanded varchar(max), selectedItem int, row int);

			DECLARE @siteID int, @posStart int, @referralID int, @panelidlist varchar(max), 
				@posStartAndCount int, @totalCount int, @searchValue varchar(300);

			SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.siteID')#">;
			SET @panelidlist = <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="0#arguments.event.getValue('_refidlist','')#">;
			SET @posStart = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('posStart')#">;
			SET @posStartAndCount = @posStart + <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('count')#">;
			<cfif len(local.searchValue)>
				SET @searchValue = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="%#local.searchValue#%">;
			</cfif>

			SELECT TOP 1 @referralID = r.referralID
			FROM dbo.ref_referrals r
			INNER JOIN dbo.cms_applicationInstances ai on ai.siteID = @siteID
				AND ai.applicationInstanceID = r.applicationInstanceID
			INNER JOIN dbo.cms_siteResources sr on sr.siteID = @siteID
				AND sr.siteResourceID = ai.siteResourceID 
				AND sr.siteResourceStatusID = 1;
			
			INSERT INTO ##tmpReferralPanels (panelID, thePathExpanded, selectedItem, row)
			SELECT tmp.panelID, tmp.thePathExpanded, isnull(dg.listitem,0), 
				ROW_NUMBER() OVER (ORDER BY tmp.thePathExpanded #arguments.event.getValue('orderDir')#)
			FROM (	
				SELECT panelID, thePathExpanded
				FROM dbo.fn_getRecursiveReferralPanels(@referralID,NULL)
				<cfif len(local.searchValue)>
					WHERE thePathExpanded LIKE @searchValue
				</cfif>
			) AS tmp
			LEFT OUTER JOIN dbo.fn_intListToTable(@panelidlist,',') dg on dg.listitem = tmp.panelID;

			SET @totalCount = @@ROWCOUNT;

			SELECT panelID, thePathExpanded, selectedItem, @totalCount AS totalCount
			FROM ##tmpReferralPanels
			WHERE row > @posStart
			AND row <= @posStartAndCount
			ORDER BY row;
		
			IF OBJECT_ID('tempdb..##tmpReferralPanels') IS NOT NULL 
				DROP TABLE ##tmpReferralPanels;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.arrData = []>
		<cfloop query="local.qryReferralPanels">
			<cfset local.arrData.append({
				"panelID": local.qryReferralPanels.panelID,
				"isSelected": local.qryReferralPanels.selectedItem GT 0,
				"paneltreepath": encodeForHTML(local.qryReferralPanels.thePathExpanded),
				"DT_RowId": "panelRow_#local.qryReferralPanels.panelID#"
			})>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal":  val(local.qryReferralPanels.totalCount),
			"recordsFiltered":  val(local.qryReferralPanels.totalCount),
			"data": local.arrData
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>
	
	<cffunction name="getReferralNotes" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();

			local.objAdminReferrals	= CreateObject("component","model.admin.referrals.referrals");
			local.objReferrals	= CreateObject("component","model.referrals.referrals");
			local.referralID = local.objAdminReferrals.getReferralSettings(arguments.event.getValue('mc_siteInfo.siteID')).referralID;

			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'desc');
			arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 2)));
			arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
			arguments.event.setValue('count',int(val(arguments.event.getValue('length',10))));
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));
			arguments.event.setValue('search',form['search[value]'] ?: '');
			// Set the request timeout to 300 seconds
			setting requesttimeout = 300;
		</cfscript>

		<cfset local.arrCols = arrayNew(1)>

		<cfset arrayAppend(local.arrCols,"n.referralNote")>
		<cfset arrayAppend(local.arrCols,"m.firstName + isnull(' ' + left(nullif(m.middleName,''),1),'') + ' ' + m.lastName")>
		<cfset arrayAppend(local.arrCols,"n.createdDate")>
		<cfset arrayAppend(local.arrCols,"n.followUpDate")>
		<cfset arrayAppend(local.arrCols,"n.followUpStatus")>
		<cfset local.orderby = local.arrcols[arguments.event.getValue('orderby')+1]>
		<cfset local.searchValue = arguments.event.getValue('search','')>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryGetReferralNotes" result="local.qryCasesResult">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				DECLARE @posStart int, @posStartAndCount int, @totalCount int, @midnight datetime = dateAdd(dd, dateDiff(dd, 0, getDate()), 0),
					@searchValue varchar(max), @referralID int;
				SET @posStart = <cfqueryparam value="#arguments.event.getValue('posStart')#" cfsqltype="CF_SQL_INTEGER">;
				SET @posStartAndCount = @posStart + <cfqueryparam value="#arguments.event.getValue('count')#" cfsqltype="CF_SQL_INTEGER">;
				<cfif len(local.searchValue)>
					SET @searchValue = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="%#local.searchValue#%">;
				</cfif>
				SET @referralID = <cfqueryparam value="#local.referralID#" cfsqltype="CF_SQL_INTEGER">;

				IF OBJECT_ID('tempdb..##tmpNotes') IS NOT NULL 
					DROP TABLE ##tmpNotes;
				CREATE TABLE ##tmpNotes (referralNoteID int PRIMARY KEY, referralNote varchar(max), clientReferralID int, noteTypeName varchar(25), 
					noteType varchar(15), createdDate datetime, enteredBy varchar(255), followUpDate datetime, followUpStatus varchar(15), row int);

				INSERT INTO ##tmpNotes (referralNoteID, clientReferralID, referralNote, noteTypeName, noteType, enteredBy, createdDate, followUpDate, followUpStatus, row)
				SELECT n.referralNoteID, n.clientReferralID, n.referralNote, 
					case when n.noteType = 'A' then 'Attorney' else 'Counselor' end as noteTypeName,n.noteType,					
					m.firstName + isnull(' ' + left(nullif(m.middleName,''),1),'') + ' ' + m.lastName as enteredBy,
					n.createdDate, n.followUpDate, 
					case when n.followUpStatus = 'P' then 'Pending' when n.followUpStatus = 'C' then 'Completed' else '' end as followUpStatus,
					ROW_NUMBER() OVER (ORDER BY #preserveSingleQuotes(local.orderby)# #arguments.event.getValue('orderDir')#) as row
				FROM dbo.ref_notes as n
				INNER JOIN dbo.ref_clientReferrals as cr on cr.referralID = @referralID and cr.clientReferralID = n.clientReferralID
				outer apply (
					select m3.firstName, m3.middleName, m3.lastName 
					from dbo.ams_members as m2
					inner join dbo.ams_members as m3 on m3.memberID = m2.activeMemberID
					where m2.memberID = n.createdBy	
					) as m	
				WHERE n.clientReferralID = <cfqueryparam value="#arguments.event.getValue('cid')#" cfsqltype="cf_sql_integer">
				AND n.noteType = <cfqueryparam value="#arguments.event.getValue('type')#" cfsqltype="cf_sql_varchar">
				<cfif len(local.searchValue)>
					AND (n.referralNote LIKE @searchValue)
				</cfif>;
				SELECT @totalCount = @@ROWCOUNT;

				SELECT referralNoteID, clientReferralID, noteTypeName, noteType, enteredBy, referralNote, createdDate, followUpDate, followUpStatus, row, @totalCount as totalCount
				FROM ##tmpNotes
				WHERE row > @posStart AND row <= @posStartAndCount
				ORDER BY row;

				IF OBJECT_ID('tempdb..##tmpNotes') IS NOT NULL
					DROP TABLE ##tmpNotes;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfset local.data = []>
		<cfloop query="local.qryGetReferralNotes">
			<cfset local.tmpStr = {
				"referralNoteID" : local.qryGetReferralNotes.referralNoteID,
				"clientReferralID": local.qryGetReferralNotes.clientReferralID,
				"noteTypeName": local.qryGetReferralNotes.noteTypeName,
				"noteType": local.qryGetReferralNotes.noteType,
				"enteredBy":  local.qryGetReferralNotes.enteredBy,
				"referralNote": local.qryGetReferralNotes.referralNote,
				"createdDate": dateTimeFormat(local.qryGetReferralNotes.createdDate,"mm/dd/yyyy h:nn tt"),
				"followUpDate": dateFormat(local.qryGetReferralNotes.followUpDate,"mm/dd/yyyy"),
				"followUpStatus": local.qryGetReferralNotes.followUpStatus,
				"DT_RowId": "refNoteRow_#local.qryGetReferralNotes.referralNoteID#"
			}>

			<cfset arrayAppend(local.data, local.tmpStr)>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": val(local.qryGetReferralNotes.totalCount),
			"recordsFiltered": val(local.qryGetReferralNotes.totalCount),
			"data": local.data
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getReferralChangeHistory" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">
	
		<cfscript>
			var local = structNew();
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));
			local.referralAuditTrail = CreateObject("component","model.system.platform.history").getReferralUpdateHistory(
				orgID=arguments.event.getValue('mc_siteInfo.orgID'),
				clientReferralID=val(arguments.event.getValue('clientReferralID'))
			);
		</cfscript>
		<cfset local.startDate = arguments.event.getValue('ms','')>
		<cfset local.endDate = arguments.event.getValue('me','')>

		<cfset local.memberIDList = "">
		<cfset local.arrData = []>
		<cfloop from="1" to="#ArrayLen(local.referralAuditTrail.arrValue)#" index="local.atNdx">
			<cfset local.currATItem = local.referralAuditTrail.arrValue[local.atNdx]>
			<cfif NOT listFind(local.memberIDList, local.currATItem.ACTORMEMBERID)>
				<cfset local.memberIDList = listAppend(local.memberIDList, local.currATItem.ACTORMEMBERID)>
			</cfif>
			<cfset local.description = "#replace(local.currATItem.MAINMESSAGE,"#chr(13)##chr(10)#","<br/>","ALL")#<br>">
			
			<cfloop array="#local.currATItem.CHANGES#" index="local.thisChange">
				<cfif NOT IsArray(local.thisChange)>
					<cfset local.atMsgLen = len(local.thisChange.OLDVALUE) + len(local.thisChange.NEWVALUE) + 16>
					<cfset local.changeOld = len(local.thisChange.OLDVALUE) eq 0 ? "[blank]" : "[#local.thisChange.OLDVALUE#]">
					<cfset local.changeNew = len(local.thisChange.NEWVALUE) eq 0 ? "[blank]" : "[#local.thisChange.NEWVALUE#]">
					<cfsavecontent variable="local.description">
						<cfoutput>
							#local.description# #encodeForHTML(local.thisChange.ITEM)# changed from 
							<cfif local.atMsgLen gt 100><br>&nbsp;&nbsp;</cfif>#encodeForHTML(local.changeOld)# to 
							<cfif local.atMsgLen gt 100><br>&nbsp;&nbsp;</cfif>#encodeForHTML(local.changeNew)#<br>
						</cfoutput>
					</cfsavecontent>
				</cfif>
			</cfloop>
			<cfset local.canAppendData = 0>
			<cfset local.historyDate = DateTimeFormat(parsedatetime(local.currATItem["_id__timestamp"]), "m/d/yyyy")>
			<cfif local.startDate neq '' AND local.endDate neq ''>
				<cfif DateDiff("d",local.historyDate,parsedatetime(local.endDate)) gte 0 AND  DateDiff("d",local.historyDate,parsedatetime(local.startDate)) lte 0>
					<cfset local.canAppendData = 1>
				</cfif>
			<cfelseif local.startDate neq '' AND local.endDate eq ''>
				<cfif DateDiff("d",local.historyDate,parsedatetime(local.startDate)) lte 0>
					<cfset local.canAppendData = 1>
				</cfif>
			<cfelseif local.startDate eq '' AND local.endDate neq ''>
				<cfif DateDiff("d",local.historyDate,parsedatetime(local.endDate)) gte 0>
					<cfset local.canAppendData = 1>
				</cfif>
			<cfelse>
				<cfset local.canAppendData = 1>
			</cfif>
			<cfif local.canAppendData eq 1>
				<cfset arrayAppend(local.arrData, {
					"dateRecorded": DateTimeFormat(parsedatetime(local.currATItem["_id__timestamp"]), "m/d/yyyy hh:nn tt"),
					"description": local.description,
					"actormemberid": local.currATItem.ACTORMEMBERID
				})>
			</cfif>
		</cfloop>

		<cfif arrayLen(local.arrData)>
			<cfquery name="local.qryMembers" datasource="#application.dsn.membercentral.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				SELECT m.memberID, m2.firstName + ' ' + m2.lastName AS membername
				FROM dbo.ams_members AS m
				INNER JOIN dbo.ams_members AS m2 ON m2.memberID = m.activeMemberID
				WHERE m.memberID IN (<cfqueryparam cfsqltype="cf_sql_integer" value="#local.memberIDList#" list="true">);

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>

			<cfset var strMemberName = structNew()>
			<cfloop query="local.qryMembers">
				<cfset strMemberName["#local.qryMembers.memberID#"] = local.qryMembers.memberName>
			</cfloop>
			
			<cfset local.arrData = arrayMap(local.arrData, (thisData) => { 
				thisData["updatedby"] = structKeyExists(strMemberName, thisData.actormemberid) ? strMemberName["#thisData.actormemberid#"] : "";
				return thisData;
			})>
		</cfif>
		
		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": arrayLen(local.arrData),
			"recordsFiltered": arrayLen(local.arrData),
			"data": local.arrData
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getMemberReferrals" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			local.referralID = CreateObject("component","model.admin.referrals.referrals").getReferralSettings(arguments.event.getValue('mc_siteInfo.siteID')).referralID;

			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'asc');
			arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 1)));
			arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
			arguments.event.setValue('count',int(val(arguments.event.getValue('length',10))));
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));
			local.searchValue = form['search[value]'] ?: '';
			
			// Set the request timeout to 300 seconds
			setting requesttimeout = 300;
		</cfscript>

		<cfset local.arrCols = arrayNew(1)>
		<cfset arrayAppend(local.arrCols,"cr.clientReferralID")>
		<cfset arrayAppend(local.arrCols,"c.lastName + c.firstName")>
		<cfset arrayAppend(local.arrCols,"cr.clientReferralDate")>
		<cfset arrayAppend(local.arrCols,"crs.statusName")>
		<cfset local.orderby = local.arrcols[arguments.event.getValue('orderby')+1]>
		
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryGetMemberReferrals">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				IF OBJECT_ID('tempdb..##tmpMemberReferrals') IS NOT NULL 
					DROP TABLE ##tmpMemberReferrals;
				CREATE TABLE ##tmpMemberReferrals (clientreferralid int PRIMARY KEY, clientname varchar(255), issuedesc varchar(max), statusname varchar(255), 
					referraldate datetime, row int);

				DECLARE @siteID int, @orgID int, @memberID int, @referralID int, @totalCount int, @searchValue varchar(300), @posStart int, @posStartAndCount int;
				DECLARE @clientReferrals TABLE (clientReferralID int PRIMARY KEY);

				SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteid')#">;
				SET @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.orgID')#">;
				SET @memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mID')#">;
				SET @posStart = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('posStart')#">;
				SET @posStartAndCount = @posStart + <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('count')#">;
				<cfif len(local.searchValue)>
					SET @searchValue = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="%#local.searchValue#%">;
				</cfif>
				<cfif val(arguments.event.getValue('refDateRange',0))>
					declare @dateCutoff datetime, @daysToLookBack int;
					set @daysToLookBack = <cfqueryparam value="#arguments.event.getValue('refDateRange',0)#" cfsqltype="cf_sql_integer" />;
					set @dateCutoff = dateadd(dd,-@daysToLookBack,getdate());
				</cfif>
				SET @referralID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.referralID#">;

				INSERT INTO @clientReferrals (clientReferralID)
				SELECT cr.clientReferralID
				FROM dbo.ref_clientReferrals cr
				INNER JOIN dbo.ref_clients c on c.referralID = @referralID and c.clientID = cr.clientID 
				INNER JOIN dbo.ams_members m on m.orgID = @orgID AND m.memberID = cr.memberID
				INNER JOIN dbo.ams_members activeM on activeM.orgID = @orgID AND activeM.memberID = m.activeMemberID
					and activeM.memberID = @memberID
				INNER JOIN dbo.ref_clientReferralStatus crs on crs.clientReferralStatusID = cr.statusID
					and crs.isDeleted = 0 and crs.isOpen = 1
				INNER JOIN dbo.ref_clientTypes ct on ct.clientTypeID = c.typeID	and ct.clientType = 'Client'
				WHERE cr.referralID = @referralID
				<cfif val(arguments.event.getValue('refDateRange',0))>
					and cr.clientReferralDate > @dateCutoff
				</cfif>
					EXCEPT
				SELECT clientReferralID
				FROM dbo.ref_cases
				WHERE referralID = @referralID;

				INSERT INTO ##tmpMemberReferrals (clientreferralid, clientname, issuedesc, statusname, referraldate, row)
				select cr.clientReferralID, c.lastName + ', ' + c.firstName as clientName, cr.issueDesc,  crs.statusName, cr.clientReferralDate,
					ROW_NUMBER() OVER (ORDER BY #preserveSingleQuotes(local.orderBy)# #arguments.event.getValue('orderDir')#)
				from dbo.ref_clients c
				INNER JOIN dbo.ref_clientReferrals cr on cr.referralID = @referralID and cr.clientID = c.clientID
				INNER JOIN @clientReferrals temp on temp.clientReferralID = cr.clientReferralID
				INNER JOIN dbo.ref_clientReferralStatus crs on crs.clientReferralStatusID = cr.statusID
				INNER JOIN dbo.ref_clientReferralTypes crt on crt.clientReferralTypeID = cr.typeID
				<cfif len(local.searchValue)>
					AND (c.lastName + ', ' + c.firstName LIKE @searchValue OR cr.issueDesc LIKE @searchValue)
				</cfif>
				WHERE c.referralID = @referralID;
				
				SELECT @totalCount = @@ROWCOUNT;

				SELECT clientreferralid, clientname, issuedesc, statusname, referraldate, row, @totalCount as totalCount
				FROM ##tmpMemberReferrals
				WHERE row > @posStart AND row <= @posStartAndCount
				ORDER BY row;

				IF OBJECT_ID('tempdb..##tmpMemberReferrals') IS NOT NULL 
					DROP TABLE ##tmpMemberReferrals;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfset local.data = []>
		<cfloop query="local.qryGetMemberReferrals">
			<cfset arrayAppend(local.data, {
				"clientreferralid": local.qryGetMemberReferrals.clientreferralid,
				"clientname": local.qryGetMemberReferrals.clientname,
				"description": local.qryGetMemberReferrals.issuedesc,
				"referraldate":  dateFormat(local.qryGetMemberReferrals.referraldate,"m/d/yyyy"),
				"statusname": local.qryGetMemberReferrals.statusname,
				"DT_RowId": "refMemberRow_#local.qryGetMemberReferrals.clientreferralid#"
			})>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": val(local.qryGetMemberReferrals.totalCount),
			"recordsFiltered": val(local.qryGetMemberReferrals.totalCount),
			"data": local.data
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getMemberReferralHistory" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			local.referralID = CreateObject("component","model.admin.referrals.referrals").getReferralSettings(arguments.event.getValue('mc_siteInfo.siteID')).referralID;

			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'asc');
			arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 0)));
			arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
			arguments.event.setValue('count',int(val(arguments.event.getValue('length',10))));
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));
			local.searchValue = form['search[value]'] ?: '';

			// Set the request timeout to 300 seconds
			setting requesttimeout = 300;
		</cfscript>

		<cfset local.arrCols = arrayNew(1)>
		
		<cfset arrayAppend(local.arrCols,"cr.clientReferralID")>
		<cfset arrayAppend(local.arrCols,"c.lastName + ', ' + c.firstName")>
		<cfset arrayAppend(local.arrCols,"cr.clientReferralDate")>
		<cfset arrayAppend(local.arrCols,"crs.statusName")>
		<cfset local.orderby = local.arrcols[arguments.event.getValue('orderby')+1]>
		
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryGetMemberReferrals">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				IF OBJECT_ID('tempdb..##tmpMemberReferrals') IS NOT NULL 
					DROP TABLE ##tmpMemberReferrals;
				CREATE TABLE ##tmpMemberReferrals (clientreferralid int PRIMARY KEY, clientname varchar(255), iscase bit, issuedesc varchar(max), statusname varchar(255), 
					referraldate datetime, duepending decimal, row int);

				DECLARE @siteID int, @orgID int, @memberID int, @referralID int, @totalCount int, @searchValue varchar(300), @posStart int, @posStartAndCount int;
				SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteid')#">;
				SET @orgID =  dbo.fn_getOrgIDFromSiteID(@siteID);
				SET @memberID = <cfqueryparam value="#arguments.event.getValue('mID')#" cfsqltype="CF_SQL_INTEGER" />;
				SET @posStart = <cfqueryparam value="#arguments.event.getValue('posStart')#" cfsqltype="CF_SQL_INTEGER">;
				SET @posStartAndCount = @posStart + <cfqueryparam value="#arguments.event.getValue('count')#" cfsqltype="CF_SQL_INTEGER">;
				<cfif len(local.searchValue)>
					SET @searchValue = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="%#local.searchValue#%">;
				</cfif>
				<cfif val(arguments.event.getValue('refHistoryDateRange',0))>
					declare @dateCutoff datetime, @daysToLookBack int;
					set @daysToLookBack = <cfqueryparam value="#arguments.event.getValue('refHistoryDateRange',0)#" cfsqltype="cf_sql_integer" />;
					set @dateCutoff = dateadd(dd,-@daysToLookBack,getdate());
				</cfif>
				SET @referralID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.referralID#">;
				
				INSERT INTO ##tmpMemberReferrals (clientreferralid, clientname, iscase, issuedesc, statusname, referraldate, duepending, row)
				select cr.clientReferralID, c.lastName + ', ' + c.firstName as clientName, 
					isCase = case when rc.caseID is not null then 1 else 0 end,	
					cr.issueDesc, crs.statusName, cr.clientReferralDate,
					(
						select sum(ts.cache_amountAfterAdjustment-ts.cache_activePaymentAllocatedAmount-ts.cache_pendingPaymentAllocatedAmount)
						from dbo.tr_applications tra 
						inner join dbo.tr_transactionSales ts on ts.orgID = @orgID 
							and ts.transactionID = tra.transactionID 
						inner join dbo.tr_transactions t on t.transactionID = ts.transactionID					
						inner join dbo.ref_collectedFees cf on cf.referralID = @referralID and cf.collectedFeeID = tra.itemID
						where tra.orgID = @orgID 
						and cf.caseID = rc.caseID
					) as duePending,
					ROW_NUMBER() OVER (ORDER BY #preserveSingleQuotes(local.orderBy)# #arguments.event.getValue('orderDir')#) as row
				FROM dbo.ref_clients c
				INNER JOIN dbo.ref_clientTypes ct on ct.clientTypeID = c.typeID	and ct.clientType = 'Client'
				LEFT OUTER JOIN dbo.ref_clientReferrals cr on cr.referralID = @referralID and cr.clientID = c.clientID
				LEFT OUTER JOIN dbo.ref_clientReferralStatus crs on	crs.clientReferralStatusID = cr.statusID
				LEFT OUTER JOIN dbo.ref_clients rep on rep.referralID = @referralID and rep.clientID = cr.representativeID					
				LEFT OUTER JOIN dbo.ams_members m on m.orgID = @orgID and m.memberid = cr.memberID
				INNER JOIN dbo.ams_members m2 on m2.orgID = @orgID and m2.memberid = m.activeMemberID and m2.memberID = @memberID
				LEFT OUTER JOIN dbo.ref_cases rc on rc.referralID = @referralID and rc.clientReferralID = cr.clientReferralID
				WHERE c.referralID = @referralID
				<cfif val(arguments.event.getValue('refHistoryDateRange',0))>
					and cr.clientReferralDate >= @dateCutoff
				</cfif>
				<cfif len(local.searchValue)>
					AND (c.lastName + ', ' + c.firstName LIKE @searchValue OR cr.issueDesc LIKE @searchValue)
				</cfif>;
			
				SELECT @totalCount = @@ROWCOUNT;

				SELECT clientreferralid, clientname, iscase, issuedesc, statusname, referraldate, duepending, row, @totalCount as totalCount
				FROM ##tmpMemberReferrals
				WHERE row > @posStart AND row <= @posStartAndCount
				ORDER BY row;

				IF OBJECT_ID('tempdb..##tmpMemberReferrals') IS NOT NULL 
					DROP TABLE ##tmpMemberReferrals;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfset local.data = []>
		<cfloop query="local.qryGetMemberReferrals">
			<cfset local.tmpStr = {
				"clientreferralid": local.qryGetMemberReferrals.clientreferralid,
				"clientname": local.qryGetMemberReferrals.clientname,
				"description": local.qryGetMemberReferrals.issuedesc,
				"referraldate":  dateFormat(local.qryGetMemberReferrals.referraldate,"m/d/yyyy"),
				"isretainedcase" : yesNoFormat(local.qryGetMemberReferrals.isCase),
				"statusname": local.qryGetMemberReferrals.statusname,
				"isamountdue": yesNoFormat(local.qryGetMemberReferrals.duepending neq 'null' AND local.qryGetMemberReferrals.duepending neq ''	AND local.qryGetMemberReferrals.duepending gt 0),
				"DT_RowId": "refHistMemberRow_#local.qryGetMemberReferrals.clientreferralid#"
			}>
			<cfset arrayAppend(local.data, local.tmpStr)>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": val(local.qryGetMemberReferrals.totalCount),
			"recordsFiltered": val(local.qryGetMemberReferrals.totalCount),
			"data": local.data
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getAdminMemberCases" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			local.objAdminReferrals	= CreateObject("component","model.admin.referrals.referrals");
			local.objReferrals	= CreateObject("component","model.referrals.referrals");
			local.objTransAdmin = CreateObject('component','model.admin.transactions.transactionAdmin');
			local.referralID = local.objAdminReferrals.getReferralSettings(arguments.event.getValue('mc_siteInfo.siteID')).referralID;
			
			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'asc');
			arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 1)));
			arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
			arguments.event.setValue('count',int(val(arguments.event.getValue('length',10))));
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));
			local.searchValue = form['search[value]'] ?: '';

			setting requesttimeout = 300;
		</cfscript>

		<cfset local.arrCols = arrayNew(1)>
		<cfset arrayAppend(local.arrCols,"cr.clientReferralID")>
		<cfset arrayAppend(local.arrCols,"c.lastName + ', ' + c.firstName")>
		<cfset arrayAppend(local.arrCols,"cr.clientReferralDate")>
		<cfset local.orderby = local.arrcols[arguments.event.getValue('orderby')+1]>

		<cfquery name="local.qryGetMemberCases" datasource="#application.dsn.membercentral.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				IF OBJECT_ID('tempdb..##tmpMemberCases') IS NOT NULL
					DROP TABLE ##tmpMemberCases;
				CREATE TABLE ##tmpMemberCases (clientreferralid int PRIMARY KEY, caseid int, clientname varchar(155), memberid int, membername varchar(255), 
					referraldate datetime, row int);
	
				DECLARE @siteID int, @orgID int, @memberID int, @referralID int, @totalCount int, @searchValue varchar(300), @posStart int, @posStartAndCount int;
				SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteid')#">;
				SET @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.orgID')#">;
				SET @memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mID')#">;
				<cfif len(local.searchValue)>
					SET @searchValue = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="%#local.searchValue#%">;
				</cfif>
				SET @posStart = <cfqueryparam value="#arguments.event.getValue('posStart')#" cfsqltype="CF_SQL_INTEGER">;
				SET @posStartAndCount = @posStart + <cfqueryparam value="#arguments.event.getValue('count')#" cfsqltype="CF_SQL_INTEGER">;
				SET @referralID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.referralID#">;
				
				<cfif val(arguments.event.getValue('caseDateRange',0))>
					declare @dateCutoff datetime, @daysToLookBack int;
					set @daysToLookBack = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('caseDateRange',0)#">;
					set @dateCutoff = dateadd(dd,-@daysToLookBack,getdate());
				</cfif>

				INSERT INTO ##tmpMemberCases (clientreferralid, caseid, clientname, memberid, membername, referraldate, row)
				SELECT cr.clientReferralID, rc.caseID, c.lastName + ', ' + c.firstName as clientName, cr.memberID, 
					(m.firstName  + 
					case
						when m.middlename is not null and len(m.middlename) > 0  then
							' ' + left(m.middleName, 1) + ' '
						else
							' '
					end  + m.lastName) as memberName,
					cr.clientReferralDate,
					ROW_NUMBER() OVER (ORDER BY #preserveSingleQuotes(local.orderBy)# #arguments.event.getValue('orderDir')#) as row				
				FROM dbo.ref_clients c
				INNER JOIN dbo.ref_clientReferrals cr on cr.referralID = @referralID and cr.clientID = c.clientID
				INNER JOIN dbo.ref_clientReferralStatus crs on crs.clientReferralStatusID = cr.statusID
					and crs.isDeleted <> 1 and crs.isClosed <> 1
				INNER JOIN dbo.ref_clientTypes ct on ct.clientTypeID = c.typeID and ct.clientType = 'Client'
				INNER JOIN dbo.ref_cases rc on rc.referralID = @referralID and rc.clientReferralID = cr.clientReferralID
				LEFT OUTER JOIN dbo.ams_members m on m.orgID = @orgID AND m.memberid = cr.memberid
				INNER JOIN dbo.ams_members m2 on m2.orgID = @orgID AND m2.memberid = m.activeMemberID 
					and m2.memberID = @memberID
				WHERE c.referralID = @referralID
				<cfif val(arguments.event.getValue('caseDateRange',0))>
					and rc.dateCaseOpened >= @dateCutoff
				</cfif>
				<cfif len(local.searchValue)>
					AND (c.lastName + ', ' + c.firstName LIKE @searchValue)
				</cfif>
				
				SELECT @totalCount = @@ROWCOUNT;

				SELECT clientreferralid, caseid, clientname, memberid, membername, referraldate, row, @totalCount as totalcount
				FROM ##tmpMemberCases as tmp 
				WHERE row > @posStart AND row <= @posStartAndCount
				ORDER BY row;

				IF OBJECT_ID('tempdb..##tmpMemberCases') IS NOT NULL
					DROP TABLE ##tmpMemberCases;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfset local.data = []>
		<cfloop query="local.qryGetMemberCases">
			<cfset local.qryGetCaseFees = local.objAdminReferrals.getCaseFees(caseID=local.qryGetMemberCases.caseID)>
			<cfset local.qryGetFeesTotals = local.objReferrals.getFeesTotals(qryItems=local.qryGetCaseFees)>
			<cfset local.tmpStr = {
				"clientreferralid": local.qryGetMemberCases.clientreferralid,
				"clientname": local.qryGetMemberCases.clientname,
				"referraldate": DateFormat(local.qryGetMemberCases.referraldate,"m/d/yyyy"),
				"collectedfeetotal": numberFormat(local.qryGetFeesTotals.collectedFeeTotal,"_$_9,999.99"),
				"referralduestotal": numberFormat(local.qryGetFeesTotals.referralDuesTotal,"_$_9,999.99"),
				"amttobepaidtotal": numberFormat(local.qryGetFeesTotals.amtToBePaidTotal,"_$_9,999.99"),
				"paidtodatetotal": numberFormat(local.qryGetFeesTotals.paidToDateTotal,"_$_9,999.99"),
				"hasCaseFees" : local.qryGetCaseFees.recordCount GT 0,
				"iscasefee" : false,
				"DT_RowId": "memberCaseRow_#local.qryGetMemberCases.clientReferralID#"
			}>
			<cfset arrayAppend(local.data, local.tmpStr)>
			<cfoutput query="local.qryGetCaseFees" group="collectedFeeID">
				<cfquery name="local.qryThisCaseFees" dbtype="query">
					select collectedFeeID, collectedFee as collectedFeeTotal, sum(ReferralDues) as ReferralDuesTotal, 
						sum(amtToBePaid) as amtToBePaidTotal, sum(paidToDate) as paidToDateTotal
					from [local].qryGetCaseFees
					where collectedFeeID = #val(local.qryGetCaseFees.collectedFeeID)#
					group by collectedFeeID, collectedFee
				</cfquery>
				<cfquery name="local.qryThisCaseFeeSales" dbtype="query">
					select saleTID
					from [local].qryGetCaseFees
					where collectedFeeID = #val(local.qryGetCaseFees.collectedFeeID)#
				</cfquery>
				<cfset local.addPaymentEncString = ''>
				<cfif local.qryThisCaseFees.amtToBePaidTotal gt 0>
					<cfset local.addPaymentEncString = local.objTransAdmin.generatePOForAddPayment(pmid=local.qryGetMemberCases.memberid, t="Referral #arguments.event.getValue('mc_siteinfo.sitecode')#-#local.qryGetMemberCases.clientReferralID# of #local.qryGetMemberCases.memberName#", ta=local.qryThisCaseFees.amtToBePaidTotal, tmid=local.qryGetMemberCases.memberid, ad="s|#valueList(local.qryThisCaseFeeSales.saleTID)#")>
				</cfif>
				<cfset local.tmpStr = {
					"clientreferralid": local.qryGetMemberCases.clientreferralid,
					"clientname": local.qryGetMemberCases.clientname,
					"referraldate": DateFormat(local.qryGetMemberCases.referraldate,"m/d/yyyy"),
					"collectedfeetotal": numberFormat(local.qryThisCaseFees.collectedFeeTotal,"_$_9,999.99"),
					"referralduestotal": numberFormat(local.qryThisCaseFees.ReferralDuesTotal,"_$_9,999.99"),
					"amttobepaidtotal": numberFormat(local.qryThisCaseFees.amtToBePaidTotal,"_$_9,999.99"),
					"paidtodatetotal": numberFormat(local.qryThisCaseFees.paidToDateTotal,"_$_9,999.99"),
					"addpaymentstring":local.addPaymentEncString,
					"hasCaseFees" : 0,
					"iscasefee" : 1,
					"DT_RowId": "memberCaseRow_#local.qryGetMemberCases.clientReferralID#_#local.qryGetCaseFees.collectedFeeID#",
					"DT_RowClass": "refCaseFeeOf#local.qryGetMemberCases.clientReferralID# d-none"
				}>
				<cfset arrayAppend(local.data, local.tmpStr)>
			</cfoutput>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": val(local.qryGetMemberCases.totalcount),
			"recordsFiltered": val(local.qryGetMemberCases.totalcount),
			"data": local.data
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getReferralPanelsList" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfset var local = structNew() />
		<cfset arguments.event.paramValue('panelName','')>
		<cfset arguments.event.paramValue('subPanelName','')>
		<cfset arguments.event.paramValue('panelStartDate','')>
		<cfset arguments.event.paramValue('subStartDate','')>
		<cfset arguments.event.paramValue('panelEndDate','')>
		<cfset arguments.event.paramValue('subEndDate','')>
		<cfset arguments.event.paramValue('panelStatusID','')>
		<cfset arguments.event.paramValue('subStatusID','')>
		<cfset arguments.event.paramValue('hasPanelPermission','')>
		<cfset arguments.event.paramValue('hasSubPermission','')>
		<cfset local.referralID = arguments.event.getValue('referralID',0)>

		<cfquery name="local.getPanels" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;

			IF OBJECT_ID('tempdb..##tmpPanel') IS NOT NULL
				DROP TABLE ##tmpPanel;
			IF OBJECT_ID('tempdb..##tmpSubPanel') IS NOT NULL
				DROP TABLE ##tmpSubPanel;
			CREATE TABLE ##tmpPanel (panelID int PRIMARY KEY, name varchar(255), panelParentID int, siteResourceID int, statusName varchar(255), shortDesc varchar(255), thePath varchar(1000), thePathExpanded varchar(max), hasRights int);
			
			DECLARE @referralID int, @panelXML xml, @xsl xml, @siteID int;
			SET @referralID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.referralID#">;
			SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.siteID')#">;

			<cfif len(arguments.event.getTrimValue('subPanelName')) OR len(arguments.event.getTrimValue('subStartDate')) OR len(arguments.event.getTrimValue('subEndDate'))>
				SELECT panelID, name, panelParentID
				INTO ##tmpSubPanel
				FROM ref_panels
				WHERE panelParentID IS NOT NULL
				<cfif len(arguments.event.getTrimValue('subPanelName'))>
					AND lower(isNull(name,'')) like <cfqueryparam cfsqltype="cf_sql_varchar" value="%#arguments.event.getTrimValue('subPanelName')#%">
				</cfif>
				<cfif len(arguments.event.getTrimValue('subStartDate'))>
					AND dateCreated >= <cfqueryparam cfsqltype="cf_sql_date" value="#arguments.event.getTrimValue('subStartDate')#">
				</cfif>
				<cfif len(arguments.event.getTrimValue('subEndDate'))>
					AND dateCreated <= <cfqueryparam cfsqltype="cf_sql_timestamp" value="#arguments.event.getTrimValue('subEndDate')# 23:59:59.997">
				</cfif>
				<cfif len(arguments.event.getTrimValue('subStatusID'))>
					and statusID IN (<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getTrimValue('subStatusID')#" list="true">)
				</cfif>
			</cfif>

			;WITH Panels AS (
				SELECT panelID, name, panelParentID, siteResourceID, statusName, shortDesc,
					CAST(RIGHT('100000'+theRow,4) as varchar(max)) AS thePath,
					CAST(name as varchar(max)) as thePathExpanded, hasRights
				FROM (
					SELECT p.panelID, p.name, p.panelParentID, p.siteResourceID, ps.statusName, p.shortDesc,
						ROW_NUMBER() OVER (ORDER BY p.name) AS theRow, 
						CASE WHEN exists (SELECT resourceID from dbo.cms_siteResourceRightsCache WHERE resourceID = p.siteResourceID and siteID = @siteID) then 1 else 0 end as hasRights
					FROM ref_panels p
						INNER JOIN ref_panelStatus ps on ps.panelStatusID = p.statusID AND ps.referralID = @referralID
							AND ps.statusName <> 'Deleted'
						<cfif len(arguments.event.getTrimValue('subPanelName')) OR len(arguments.event.getTrimValue('subStartDate')) OR len(arguments.event.getTrimValue('subEndDate'))>
							INNER JOIN (SELECT DISTINCT panelParentID from ##tmpSubPanel) tsp on tsp.panelParentID = p.panelID
						</cfif>
					WHERE p.referralID = @referralID
						AND p.panelParentID IS NULL
						<cfif len(arguments.event.getTrimValue('panelName'))>
							AND lower(isNull(p.name,'')) like <cfqueryparam cfsqltype="cf_sql_varchar" value="%#arguments.event.getTrimValue('panelName')#%">
						</cfif>
						<cfif len(arguments.event.getTrimValue('panelStartDate'))>
							and p.dateCreated >= <cfqueryparam cfsqltype="cf_sql_date" value="#arguments.event.getTrimValue('panelStartDate')#">
						</cfif>
						<cfif len(arguments.event.getTrimValue('panelEndDate'))>
							and p.dateCreated <= <cfqueryparam cfsqltype="cf_sql_timestamp" value="#arguments.event.getTrimValue('panelEndDate')# 23:59:59.997">
						</cfif>
						<cfif len(arguments.event.getTrimValue('panelStatusID'))>
							and p.statusID IN (<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getTrimValue('panelStatusID')#" list="true">)
						</cfif>
				) AS x
				<cfif len(arguments.event.getTrimValue('hasPanelPermission'))>
					WHERE hasRights = <cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.event.getTrimValue('hasPanelPermission')#">
				</cfif>

				UNION ALL

				SELECT panelID, name, panelParentID, siteResourceID, statusName, shortDesc,
					thePath + '.' + CAST(RIGHT('100000'+theRow,4) as varchar(max)) AS thePath,
					thePathExpanded + ' \ ' + name as thePathExpanded, hasRights
				FROM (
					SELECT p.panelID, p.name, p.panelParentID ,p.siteResourceID, ps.statusName, p.shortDesc,
						pan.thePath, pan.thePathExpanded, ROW_NUMBER() OVER (ORDER BY p.name) AS theRow, 
						CASE WHEN exists (SELECT resourceID from dbo.cms_siteResourceRightsCache WHERE resourceID = p.siteResourceID and siteID = @siteID) then 1 else 0 end as hasRights
					FROM ref_panels p
						INNER JOIN ref_panelStatus ps on ps.panelStatusID = p.statusID AND ps.referralID = @referralID
							AND ps.statusName <> 'Deleted'
						INNER JOIN Panels as pan ON p.panelParentID = pan.panelID
						<cfif len(arguments.event.getTrimValue('subPanelName')) OR len(arguments.event.getTrimValue('subStartDate')) OR len(arguments.event.getTrimValue('subEndDate'))>
							INNER JOIN ##tmpSubPanel tsp on tsp.panelID = p.panelID
						</cfif>
					WHERE p.referralID = @referralID
						<cfif len(arguments.event.getTrimValue('subPanelName'))>
							AND lower(isNull(p.name,'')) like <cfqueryparam cfsqltype="cf_sql_varchar" value="%#arguments.event.getTrimValue('subPanelName')#%">
						</cfif>
						<cfif len(arguments.event.getTrimValue('subStartDate'))>
							and p.dateCreated >= <cfqueryparam cfsqltype="cf_sql_date" value="#arguments.event.getTrimValue('subStartDate')#">
						</cfif>
						<cfif len(arguments.event.getTrimValue('subEndDate'))>
							and p.dateCreated <= <cfqueryparam cfsqltype="cf_sql_timestamp" value="#arguments.event.getTrimValue('subEndDate')# 23:59:59.997">
						</cfif>
						<cfif len(arguments.event.getTrimValue('subStatusID'))>
							and p.statusID IN (<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getTrimValue('subStatusID')#" list="true">)
						</cfif>
				) AS y
				<cfif len(arguments.event.getTrimValue('hasSubPermission'))>
					WHERE hasRights = <cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.event.getTrimValue('hasSubPermission')#">
				</cfif>
			)

			INSERT INTO ##tmpPanel (panelID, name, panelParentID,siteResourceID,statusName,shortDesc,thePath,thePathExpanded,hasRights)
			SELECT panelID, name, panelParentID, siteResourceID, statusName, shortDesc, thePath, thePathExpanded, hasRights
			FROM Panels
			ORDER BY panelID;
			
			SELECT @referralID AS referralID, tmp.panelID, tmp.name, tmp.panelParentID, tmp.siteResourceID,
				tmp.statusName, tmp.shortDesc, tmp.thePath, tmp.thePathExpanded, tmp.hasRights
			FROM (	
				SELECT tmp.panelID, tmp.name, isnull(tmp.panelParentID,0) AS panelParentID, tmp.siteResourceID, 
					tmp.statusName, tmp.shortDesc, tmp.thePath, tmp.thePathExpanded, tmp.hasRights
				FROM ##tmpPanel AS tmp
				LEFT OUTER JOIN dbo.cms_categorySiteResources AS csr 
					INNER JOIN dbo.cms_siteResources AS sr ON sr.siteResourceID = csr.siteResourceID 
					INNER JOIN dbo.cms_siteResourceStatuses AS srs ON srs.siteResourceStatusID = sr.siteResourceStatusID AND srs.siteResourceStatusDesc = 'Active'
					ON csr.siteResourceID = tmp.siteResourceID
				GROUP BY tmp.panelID, tmp.name, tmp.panelParentID, tmp.statusName, tmp.siteResourceID, tmp.shortDesc, tmp.thePath, tmp.thePathExpanded, tmp.hasRights
			) AS tmp
			ORDER BY tmp.thePath;
			
			IF OBJECT_ID('tempdb..##tmpPanel') IS NOT NULL 
				DROP TABLE ##tmpPanel;
			IF OBJECT_ID('tempdb..##tmpSubPanel') IS NOT NULL 
				DROP TABLE ##tmpSubPanel;
		</cfquery>

		<cfquery name="local.qryPanelsOnly" dbtype="query">
			SELECT count(panelID) as totalCount
			FROM [local].getPanels
			WHERE panelParentID = 0
		</cfquery>
		
		<cfset local.arrPanels = []>
		<cfloop query="local.getPanels">
			<cfset local.thisRowID = "panelRow#local.getPanels.panelID#">
			<cfset local.classList = "child-of-panelRow#val(local.getPanels.panelParentID)#" & (local.getPanels.panelParentID ? " d-none" : "")>

			<cfquery name="local.qryChildPanel" dbtype="query" maxrows="1">
				SELECT panelID
				FROM [local].getPanels
				WHERE panelParentID = #val(local.getPanels.panelID)#
			</cfquery>

			<cfset local.arrPanels.append({
				"level": ListLen(local.getPanels.thePath,"."),
				"panelID": local.getPanels.panelID,
				"name": local.getPanels.name,
				"nameEncoded": encodeForJavascript(local.getPanels.name),
				"shortDesc": local.getPanels.shortDesc,
				"statusName": local.getPanels.statusName,
				"hasRights": local.getPanels.hasRights,
				"siteResourceID": local.getPanels.siteResourceID,
				"parentPanelID": local.getPanels.panelParentID,
				"hasChildren": local.qryChildPanel.recordCount gt 0,
				"canAddSubPanel": local.getPanels.panelParentID eq 0 and local.getPanels.statusName neq "Deleted",
				"canDelete": local.getPanels.statusName neq "Deleted",
				"DT_RowId": local.thisRowID,
				"DT_RowClass": local.classList
			})>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": arrayLen(local.arrPanels),
			"recordsFiltered": arrayLen(local.arrPanels),
			"data": local.arrPanels,
			"panelsCount": local.qryPanelsOnly.totalCount
		}>
		
		<cfreturn serializeJSON(local.returnStruct)>
	</cffunction>

	<cffunction name="getReferralPanelMembersList" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			local.orgID = arguments.event.getValue('orgID',0);
			local.selectedPanelID = arguments.event.getValue('panelID',0);
			local.panelID = arguments.event.getValue('panelID',0);
			local.subPanelID = 0;
			if(arguments.event.getValue('subpanelID',0)){
				local.selectedPanelID = arguments.event.getValue('subpanelID',0);
			}
			local.objAdminReferrals	= CreateObject("component","model.admin.referrals.referrals");
			local.qryPanelMembers = local.objAdminReferrals.getPanelMembers(orgID=local.orgID, referralID=arguments.event.getValue('referralID',0), panelID=local.selectedPanelID);	
			local.panelRotationGroup = local.objAdminReferrals.getPanelRotationGroup(orgID = local.orgId, panelid = local.panelID, subpanelid = local.subPanelID);
		</cfscript>			
		
		<cfset local.arrData = []>
		<cfloop query="local.qryPanelMembers">
			<cfset local.arrData.append({
				"memberID": local.qryPanelMembers.memberID,
				"canShow": (local.panelRotationGroup.success and listFindNoCase(local.panelRotationGroup.members,local.qryPanelMembers.memberID)),
				"memberName": local.qryPanelMembers.memberName,
				"memberEmail": local.qryPanelMembers.memberEmail,
				"memberPhone": local.qryPanelMembers.memberPhone,
				"memberStatusName": local.qryPanelMembers.memberStatusName,
				"DT_RowId": "panelMember_#local.qryPanelMembers.panelMemberID#"
			})>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": arrayLen(local.arrData),
			"recordsFiltered": arrayLen(local.arrData),
			"data": local.arrData
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getSources" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'asc');
			arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 1)));
			arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
			arguments.event.setValue('count',int(val(arguments.event.getValue('length',10))));
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));
			local.searchValue = form['search[value]'] ?: '';

			local.qrySources = CreateObject("component","model.admin.referrals.referrals").getSources(referralID=arguments.event.getValue('referralID'), posStart=arguments.event.getValue('posStart'),
				count=arguments.event.getValue('count'), orderBy=arguments.event.getValue('orderBy'), orderDir=arguments.event.getValue('orderDir'), keyword=local.searchValue, mode="datatable");
		</cfscript>

		<cfset local.data = []>
		<cfloop query="local.qrySources">
			<cfset arrayAppend(local.data, {
				"clientReferralSourceID": local.qrySources.clientReferralSourceID,
				"sourceName": local.qrySources.sourceName,
				"isActive": local.qrySources.isActive,
				"isCopyDefault": local.qrySources.isCopyDefault,
				"isDefaultSource": local.qrySources.isDefaultSource,
				"isFrontEndDefault": local.qrySources.isFrontEndDefault,
				"DT_RowId": "row_#local.qrySources.clientReferralSourceID#"
			})>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": val(local.qrySources.totalCount),
			"recordsFiltered": val(local.qrySources.totalCount),
			"data": local.data
		}>

		<cfreturn serializeJSON(local.returnStruct)>
	</cffunction>

	<cffunction name="getSurveyTypes" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any" />

		<cfscript>
			var local = structNew();
			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'asc');

			local.qryGetSurveyTypes = CreateObject("component","model.admin.referrals.referrals").getSurveyTypes(referralID=arguments.event.getValue('referralID'));
		</cfscript>

		<cfquery name="local.qrySurveyTypes" dbtype="query">
			select surveyTypeID, name, usageCount
			from [local].qryGetSurveyTypes
			order by name #arguments.event.getValue('orderDir')#
		</cfquery>

		<cfset local.arrData = []>
		<cfloop query="local.qrySurveyTypes">
			<cfset local.arrData.append({
				"surveyTypeID": local.qrySurveyTypes.surveyTypeID,
				"name": local.qrySurveyTypes.name,
				"usagecount": local.qrySurveyTypes.usageCount,
				"DT_RowId": "surveyType_#local.qrySurveyTypes.surveyTypeID#"
			})>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": arrayLen(local.arrData),
			"recordsFiltered": arrayLen(local.arrData),
			"data": local.arrData
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getPanelDocuments" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();

			arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 1)));
			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'asc');
			arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
			arguments.event.setValue('count',int(val(arguments.event.getValue('length',10))));
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));
			local.searchValue = form['search[value]'] ?: '';
		</cfscript>

		<cfset local.qryPanelDocuments = createObject("component","referrals").getPanelDocuments(panelID=arguments.event.getValue('panelID'), posStart=arguments.event.getValue('posStart'),
			count=arguments.event.getValue('count'), orderBy=arguments.event.getValue('orderBy'), orderDir=arguments.event.getValue('orderDir'), keyword=local.searchValue, mode="datatable")>

		<cfset local.data = []>
		<cfloop query="local.qryPanelDocuments">
			<cfset arrayAppend(local.data, {
				"docTitle": local.qryPanelDocuments.docTitle,
				"documentID": local.qryPanelDocuments.documentID,
				"panelDocumentID": local.qryPanelDocuments.panelDocumentID,
				"panelID": arguments.event.getValue('panelID'),
				"documentVersionID": local.qryPanelDocuments.documentVersionID,
				"siteID": local.qryPanelDocuments.siteID,
				"dateCreated": dateFormat(local.qryPanelDocuments.dateCreated,'m/d/yyyy'),
				"dateModified": dateFormat(local.qryPanelDocuments.dateModified,'m/d/yyyy'),
				"DT_RowId": "docEntriesRow_#local.qryPanelDocuments.documentID#"
			})>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": val(local.qryPanelDocuments.totalCount),
			"recordsFiltered": val(local.qryPanelDocuments.totalCount),
			"data": local.data
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>
	
	<cffunction name="getReferralLanguages" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'asc');
		</cfscript>

		<cfquery name="local.qryLanguages" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT languageID, languageName, languageCode, isDefault, ISNULL(isActive,1) AS isActive, isDefault
			FROM dbo.ref_languages
			WHERE referralID =  <cfqueryparam cfsqltype="cf_sql_integer" value="#val(arguments.event.getValue('referralID',0))#">
			ORDER BY languageName #arguments.event.getValue('orderDir')#;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.arrData = []>
		<cfloop query="local.qryLanguages">
			<cfset local.arrData.append({
				"languageID": local.qryLanguages.languageID,
				"languageName": local.qryLanguages.languageName,
				"languageCode": local.qryLanguages.languageCode,
				"isDefault": local.qryLanguages.isDefault,
				"isActive": val(local.qryLanguages.isActive),
				"DT_RowId": "language_#local.qryLanguages.languageID#"
			})>
		</cfloop>
		
		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": local.qryLanguages.recordCount,
			"recordsFiltered": local.qryLanguages.recordCount,
			"data": local.arrData
		}>
		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getPanelSurveys" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 1)));
			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'asc');
			arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
			arguments.event.setValue('count',int(val(arguments.event.getValue('length',10))));
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));
			local.keyword = form['search[value]'] ?: '';
		</cfscript>

		<cfquery name="local.qrySurveys" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			IF OBJECT_ID('tempdb..##tmpPanelSurveys') IS NOT NULL 
				DROP TABLE ##tmpPanelSurveys;
			CREATE TABLE ##tmpPanelSurveys (surveyID int, panelID int, surveyName varchar(255), isActive bit, row int);
			
			DECLARE @panelID int, @posStart int, @posStartAndCount int, @totalCount int;
			SET @panelID =  <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('panelID')#">;
			SET @posStart = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('posStart')#">;
			SET @posStartAndCount = @posStart + <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('count')#">;

			INSERT INTO ##tmpPanelSurveys (surveyID, panelID, surveyName, isActive, row)
			SELECT ps.surveyID, ps.panelID, ps.name, ISNULL(ps.isActive,0),
				ROW_NUMBER() OVER (ORDER BY ps.name #arguments.event.getValue('orderDir')#)
			FROM dbo.ref_panelSurveys ps
			INNER JOIN dbo.ref_panels p ON p.panelID = ps.panelID
				AND p.referralID =  <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('referralID')#" />
			WHERE ps.panelID = @panelID
			<cfif len(local.keyword)>
				AND ps.name LIKE <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="%#local.keyword#%">
			</cfif>;
			
			SET @totalCount = @@ROWCOUNT;

			SELECT surveyID, panelID, surveyName, isActive, @totalCount AS totalCount
			FROM ##tmpPanelSurveys
			WHERE row > @posStart 
			AND row <= @posStartAndCount
			ORDER BY row;

			IF OBJECT_ID('tempdb..##tmpPanelSurveys') IS NOT NULL 
				DROP TABLE ##tmpPanelSurveys;
			
			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.data = []>
		<cfloop query="local.qrySurveys">
			<cfset arrayAppend(local.data, {
				"surveyID": local.qrySurveys.surveyID,
				"panelID": local.qrySurveys.panelID,
				"surveyName": local.qrySurveys.surveyName,
				"isActive": local.qrySurveys.isActive,
				"DT_RowId": "surveyRow_#local.qrySurveys.surveyID#"
			})>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": val(local.qrySurveys.totalCount),
			"recordsFiltered": val(local.qrySurveys.totalCount),
			"data": local.data
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getReferralClassifications" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			local.qryClassifications = createObject("component","referrals").getClassifications(referralID=arguments.event.getValue('referralID'));
		</cfscript>

		<cfset local.arrData = []>
		<cfloop query="local.qryClassifications">
			<cfset local.arrData.append({
				"classificationid": local.qryClassifications.classificationID,
				"classificationname": len(trim(local.qryClassifications.name))? local.qryClassifications.name : local.qryClassifications.groupSetName,
				"canmoveup":local.qryClassifications.currentRow GT 1,
				"canmovedown":local.qryClassifications.currentRow NEQ local.qryClassifications.recordCount,
				"DT_RowId": "classification_#local.qryClassifications.classificationID#"
			})>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": local.qryClassifications.recordCount,
			"recordsFiltered": local.qryClassifications.recordCount,
			"data": local.arrData
		}>
		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getReferralAgencies" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'desc');
			local.qryGetAgencies = createObject("component","referrals").getAgencies(arguments.event.getValue('referralID'));
		</cfscript>

		<cfquery name="local.qryAgencies" dbtype="query">
			select agencyID, agencyName, isActive
			from [local].qryGetAgencies
			order by agencyName #arguments.event.getValue('orderDir')#
		</cfquery>

		<cfset local.arrData = []>
		<cfloop query="local.qryAgencies">
			<cfset local.arrData.append({
				"agencyID": local.qryAgencies.agencyID,
				"agencyName": local.qryAgencies.agencyName,
				"isActive": local.qryAgencies.isActive,
				"DT_RowId": "agency_#local.qryAgencies.agencyID#"
			})>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": local.qryAgencies.recordCount,
			"recordsFiltered": local.qryAgencies.recordCount,
			"data": local.arrData
		}>
		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getFeeTypes" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));
			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'asc');

			local.qryGetClientReferralFeeTypes =  CreateObject("component","referrals").getFeeTypes(referralID=arguments.event.getValue('referralID'), orderDir=arguments.event.getValue('orderDir'));
		</cfscript>

		<cfset local.arrData = []>
		<cfloop query="local.qryGetClientReferralFeeTypes">
			<cfset local.arrData.append({
				"feeTypeID": local.qryGetClientReferralFeeTypes.feeTypeID,
				"feeTypeName": local.qryGetClientReferralFeeTypes.feeTypeName,
				"isActive": local.qryGetClientReferralFeeTypes.isActive,
				"isDefault": local.qryGetClientReferralFeeTypes.isDefault,
				"DT_RowId": "refFeeTypeRow_#local.qryGetClientReferralFeeTypes.feeTypeID#"
			})>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": arrayLen(local.arrData),
			"recordsFiltered": arrayLen(local.arrData),
			"data": local.arrData
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getReferralStatus" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any" />

		<cfscript>
			var local = structNew();
			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'asc');
			arguments.event.setValue('search',form['search[value]'] ?: '');
			local.qryGetStatuses = CreateObject("component","model.admin.referrals.referrals").getStatuses(referralID=arguments.event.getValue('referralID'));
			local.searchValue = arguments.event.getValue('search','');
		</cfscript>

		<cfquery name="local.qryStatuses" dbtype="query">
			SELECT statusID, statusName, canEditClient, canRefer,
			canEditFilter, canEditLawyer, isReferred, isAgency,
			isPending, isClosed, isClosedByLawyer, isRetainedCase,
			isDeleted, dspFrontEnd, IsActive, primaryStatusName
			FROM [local].qryGetStatuses
			<cfif len(local.searchValue)>
				WHERE (statusName LIKE <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="%#local.searchValue#%"> or primaryStatusName like <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="%#local.searchValue#%">)
			</cfif>
			ORDER BY statusName #arguments.event.getValue('orderDir')#
		</cfquery>

		<cfset local.arrData = []>
		<cfloop query="local.qryStatuses">
			<cfset local.arrData.append({
				"statusID": local.qryStatuses.statusID,
				"name": local.qryStatuses.statusName,
				"canEditClient":local.qryStatuses.canEditClient,
				"canRefer":local.qryStatuses.canRefer,
				"canEditFilter":local.qryStatuses.canEditFilter,
				"canEditLawyer":local.qryStatuses.canEditLawyer,
				"isReferred":local.qryStatuses.isReferred,
				"isAgency":local.qryStatuses.isAgency,
				"isPending":local.qryStatuses.isPending,
				"isClosed":local.qryStatuses.isClosed,
				"isClosedByLawyer":local.qryStatuses.isClosedByLawyer,
				"isRetainedCase":local.qryStatuses.isRetainedCase,
				"isDeleted":local.qryStatuses.isDeleted,
				"dspFrontEnd":local.qryStatuses.dspFrontEnd,
				"isActive":local.qryStatuses.IsActive,
				"primaryStatusName" : local.qryStatuses.primaryStatusName,
				"DT_RowId": "statusRow_#local.qryStatuses.statusID#"
			})>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": arrayLen(local.arrData),
			"recordsFiltered": arrayLen(local.arrData),
			"data": local.arrData
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>
	
	<cffunction name="getReferralsAuditLog" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.arrAudit = CreateObject('component', 'model.system.platform.auditLog').getReferralsAuditLog(
			siteID=arguments.event.getValue('mc_siteinfo.siteID'),
			area=arguments.event.getValue('fArea',''),
			keywords=arguments.event.getValue('fDescription',''),
			dateFrom=arguments.event.getValue('fDateFrom',''),
			dateTo=arguments.event.getValue('fDateTo',''),
			limit=50
		).arrValue>

		<cfset local.memberIDList = "">
		<cfset local.arrData = []>
		<cfloop array="#local.arrAudit#" item="local.thisAuditEntry" index="local.index">
			<cfif NOT listFind(local.memberIDList, local.thisAuditEntry["ACTORMEMBERID"])>
				<cfset local.memberIDList = listAppend(local.memberIDList, local.thisAuditEntry["ACTORMEMBERID"])>
			</cfif>
			<cfset local.actionDate = parseDateTime(local.thisAuditEntry["ACTIONDATE"])>

			<cfset local.arrData.append({
				"date": "#DateFormat(local.actionDate, "m/d/yy")# #TimeFormat(local.actionDate, "h:mm tt")#",
				"memberid": local.thisAuditEntry["ACTORMEMBERID"],
				"description": replace(local.thisAuditEntry["MESSAGE"],"#chr(13)##chr(10)#","<br/>","ALL"),
				"DT_RowId": "auditlogrow_#local.index#"
			})>
		</cfloop>

		<cfif arrayLen(local.arrData)>
			<cfquery name="local.qryMembers" datasource="#application.dsn.membercentral.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				SELECT m.memberID, m2.firstName + ' ' + m2.lastName AS membername
				FROM dbo.ams_members AS m
				INNER JOIN dbo.ams_members AS m2 ON m2.memberID = m.activeMemberID
				WHERE m.memberID IN (<cfqueryparam cfsqltype="cf_sql_integer" value="#local.memberIDList#" list="true">);

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>

			<cfset var strMemberName = structNew()>
			<cfloop query="local.qryMembers">
				<cfset strMemberName["#local.qryMembers.memberID#"] = local.qryMembers.memberName>
			</cfloop>

			<cfset local.arrData = arrayMap(local.arrData, (thisData) => { 
				thisData["actor"] = structKeyExists(strMemberName, thisData.memberid) ? strMemberName["#thisData.memberid#"] : "";
				return thisData;
			})>
		</cfif>

		<cfset local.returnStruct = {
			"success": true,
			"draw": int(val(arguments.event.getValue('draw',1))),
			"recordsTotal":  arrayLen(local.arrData),
			"recordsFiltered":  arrayLen(local.arrData),
			"data": local.arrData
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>
</cfcomponent>
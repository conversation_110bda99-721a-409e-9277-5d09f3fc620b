<cfsavecontent variable="local.js">
	<cfoutput>
	<script language="javascript">
	function hideAlert() { $('##sub_assocpmt_err').html('').hide(); };
	function showAlert(msg) { $('##sub_assocpmt_err').html(msg).show(); };

	function useProfile() {
		hideAlert();
		$('##paymentTabs').children().hide();
		
		var pid = $('##paySource').val();
		$('##profileid').val(pid);
		$('##paymentTab' + pid).show();

		if ($('##profileid').val() == 0) disableSaveBtn(pid);
		else enableSaveBtn(pid);
	}
	function disableSaveBtn(pid) {
		$('##btnSavePayment'+pid).attr('disabled',true);
	}
	function enableSaveBtn(pid) {
		$('##btnSavePayment'+pid).attr('disabled',false);
	}
	<cfif val(local.qrySubscriberInfo.MPProfileID)>
		function removeCCPayMethod() {
			$('input[name="p_#val(local.qrySubscriberInfo.MPProfileID)#_mppid"][value="0"]').prop('checked',true);
			$('##frmPayment button[type="submit"]').trigger('click');
		}
	</cfif>

	$(function() {
		useProfile();
	});
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.js)#">

<cfoutput>
<div id="selectCCDIV" class="p-3">
	<cfform name="frmPayment" id="frmPayment" action="#local.formlink#" method="post" onsubmit="return checkPayForm();">
	<cfinput type="hidden" name="pmid" id="pmid" value="#local.qryPayee.memberid#">
	<cfinput type="hidden" name="sid" id="sid" value="#local.subscriberID#">
	<cfinput type="hidden" name="profileid" id="profileid" value="0">

	<div class="mb-3">
		#encodeForHTML("#local.qryPayee.firstname# #local.qryPayee.lastname# (#local.qryPayee.membernumber#)")#<br/>
		#local.qrySubscriberInfo.subscriptionName# (#dateFormat(local.qrySubscriberInfo.subStartDate, "m/d/yyyy")# - #dateFormat(local.qrySubscriberInfo.subEndDate, "m/d/yyyy")#)
	</div>

	<div class="form-label-group">
		<cfselect id="paySource" name="paySource" class="form-control" query="local.qryMerchantProfiles" display="profileName" value="profileID" onChange="useProfile();" queryPosition="below">
		</cfselect>
		<label for="paySource">Choose Payment Method</label>
	</div>
	
	<div id="sub_assocpmt_err" class="alert alert-danger my-2" style="display:none;"></div>
	
	<div id="paymentTabs">
	<cfset local.strPaymentFeatures = {}>
	<cfloop query="local.qryMerchantProfiles">
		<cfset local.thisProfileID = local.qryMerchantProfiles.profileID>
		
		<cfif val(local.qrySubscriberInfo.MPProfileID) EQ local.thisProfileID AND local.qryMerchantProfiles.status EQ 'I'>
			<div class="alert d-flex align-items-center pl-2 align-content-center alert-danger py-1 font-size-sm" role="alert">
				<span class="font-size-lg d-block d-40 mr-2 text-center">
					<i class="fa-solid fa-circle-info"></i>
				</span>
				<span>The Payment Profile associated to this saved payment method has been disabled. No payments can be made using this payment method until a valid payment method is associated to this Subscription.</span>
			</div>
		</cfif>
		
		<cfset local.strPaymentFeatures[local.thisProfileID] = application.objPayments.setDefaultPayFeaturesStruct()>
		<cfif local.qryMerchantProfiles.enableProcessingFeeDonation AND val(local.qryMerchantProfiles.processFeeDonationFeePercent) GT 0>
			<cfset local.strPaymentFeatures[local.thisProfileID].processingFee = { 
				"enable":1, 
				"select":val(local.qrySubscriberInfo.MPProfileID) EQ local.thisProfileID AND val(local.qrySubscriberInfo.payProcessFee) EQ 1, 
				"label":replaceNoCase(local.qryMerchantProfiles.processFeeSubscriptionsFELabel,"{{PERCENT}}","#local.qryMerchantProfiles.processFeeDonationFeePercent#%"), 
				"denylabel":local.qryMerchantProfiles.processFeeSubscriptionsFEDenyLabel,
				"title":local.qryMerchantProfiles.processFeeDonationFETitle,
				"msg":local.qryMerchantProfiles.processFeeDonationFEMsg 
			}>
		</cfif>
		
		<cfset local.strPaymentForm = application.objPayments.showGatewayInputForm(siteid=arguments.event.getValue('mc_siteinfo.siteid'), 
			profilecode=local.qryMerchantProfiles.profileCode, pmid=arguments.event.getValue('mid'), showCOF=true, usePopup=false, 
			usePopupDIVName='pf_#local.thisProfileID#_', adminForm=1, paymentFeatures=local.strPaymentFeatures[local.thisProfileID])>

		<cfif len(local.strPaymentForm.headcode)>
			<cfhtmlhead text="#application.objCommon.minText(local.strPaymentForm.headcode)#">
		</cfif>
	
		<div id="paymentTab#local.thisProfileID#" style="display:none;">
		<div id="pf_#local.thisProfileID#_">
			<cfif len(local.strPaymentForm.inputForm)>
				<br/>
				<div>
					<div>#replaceNoCase(local.strPaymentForm.inputForm,'fld_','p_#local.thisProfileID#_fld_','ALL')#</div>
					<cfif val(local.qrySubscriberInfo.payProfileID) gt 0>
						<input type="radio" name="p_#local.thisProfileID#_mppid" id="p_#local.thisProfileID#_mppid_0" value="0" class="d-none">
						<div class="my-3">
							<button type="button" class="btn btn-xs btn-link btnSavePayment" onclick="removeCCPayMethod();"><i class="fa-regular fa-trash pr-2"></i>Remove Pay Method Associated with this Subscription</button>
						</div>
					</cfif>
				</div>
				<div id="divBtnWrapper#local.thisProfileID#" class="mt-4">
					<button type="submit" name="btnSavePayment#local.thisProfileID#" id="btnSavePayment#local.thisProfileID#" class="btn btn-sm btn-primary btnSavePayment" disabled>Associate Pay Method</button>
				</div>		
			</cfif>
		</div>
		</div>
		
		<cfsavecontent variable="local.extrapayJS">
			#local.extrapayJS#
			if ($('##profileid').val()==#local.thisProfileID#) {
				#replaceNoCase(local.strPaymentForm.jsvalidation,'fld_','p_#local.thisProfileID#_fld_','ALL')#
			}
		</cfsavecontent>
	</cfloop>

	</div>

	<cfif val(local.qrySubscriberInfo.payProfileID) gt 0 AND (NOT listLen(local.qrySubscriberInfo.eligibleProfileIDList) OR NOT listFind(local.qrySubscriberInfo.eligibleProfileIDList,local.qrySubscriberInfo.MPProfileID))>
		<cfset local.memberPayProfileDetail = application.objCustomPageUtils.getMemberPaymentProfileInfoForDisplay(siteID=arguments.event.getValue('mc_siteinfo.siteid'), orgID=arguments.event.getValue('mc_siteinfo.orgID'), memberID=local.memberID, payProfileID=local.qrySubscriberInfo.payProfileID)>
		
		<input type="radio" name="p_#local.qrySubscriberInfo.MPProfileID#_mppid" id="p_#local.qrySubscriberInfo.MPProfileID#_mppid_0" value="0" checked class="d-none">

		<div class="alert d-flex align-items-center px-2 align-content-center alert-warning py-1 font-size-sm" role="alert">
			<span class="font-size-lg d-block d-40 mr-2 text-center">
				<i class="fa-solid fa-circle-info"></i>
			</span>
			<span>
				This payment method is no longer supported in this Subscription's Rate settings.<br/>
				However, payments will still be processed with this payment method until it is removed from this Subscription.<br/>
				To prevent this error, update Subscription Rate settings to allow for this payment method to be used or seek help from <a href="mailto:<EMAIL>"><EMAIL></a>.
			</span>
		</div>
		<div class="card card-box p-3 bg-secondary border-danger" style="width:250px;">#local.memberPayProfileDetail#</div>

		<div class="my-3">
			<button type="submit" class="btn btn-xs btn-link btnSavePayment"><i class="fa-regular fa-trash pr-2"></i>Remove Pay Method Associated with this Subscription</button>
		</div>
	</cfif>
	</cfform>
</div>
</cfoutput>

<cfif val(local.qrySubscriberInfo.MPProfileID) neq 0>
	<cfoutput>
	<script>
		var thisForm = document.forms["frmPayment"];
		thisForm.paySource.value = #val(local.qrySubscriberInfo.MPProfileID)#;
		useProfile();
		
		if ((thisForm.p_#val(local.qrySubscriberInfo.MPProfileID)#_mppid) && (thisForm.p_#val(local.qrySubscriberInfo.MPProfileID)#_mppid.length)) {
			$('input[name="p_#val(local.qrySubscriberInfo.MPProfileID)#_mppid"][value="#val(local.qrySubscriberInfo.payProfileID)#"]').prop('checked',true).trigger('click');
			<cfif local.keyExists("strPaymentFeatures") AND local.strPaymentFeatures.keyExists(local.qrySubscriberInfo.MPProfileID) AND local.strPaymentFeatures[local.qrySubscriberInfo.MPProfileID].processingFee.enable AND NOT local.strPaymentFeatures[local.qrySubscriberInfo.MPProfileID].processingFee.select>
				$('##processFeeDonation#val(local.qrySubscriberInfo.MPProfileID)#_no').prop('checked',true);
			</cfif>
		}
	</script>
	</cfoutput>
</cfif>

<cfsavecontent variable="local.validationJS">
	<cfoutput>
	<script language="javascript">
	function checkPayForm() {
		hideAlert();
		var arrReq = new Array();

		let pid = $('##paySource').val();
		if (pid == '') arrReq[arrReq.length] = 'Select which payment method you are using for this payment.';
	
		<cfif len(local.extrapayJS)>
			var thisForm = document.forms["frmPayment"];
			#local.extrapayJS#
		</cfif>
		
		if (arrReq.length > 0) {
			var msg = '<b>The following requires your attention:</b><br/>';
			for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
			showAlert(msg);
			return false;
		}

		if ($('.inputForm'+pid+'StepBtn').length) {
			$('.inputForm'+pid+'StepBtn').prop('disabled',true);
		}

		$('.btnSavePayment').prop('disabled',true);

		return true;
	};
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.validationJS)#">
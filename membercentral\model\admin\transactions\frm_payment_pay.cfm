<cfsavecontent variable="local.js">
	<cfoutput>
	<style type="text/css">
		.dropdown .dropdown-menu { transform:translate3d(0px, -207px, 0px) !important; height:200px; overflow-y:auto; }
	</style>
	<script type="text/javascript">
		function hideAlert() { $('##payerr').html('').addClass('d-none'); setBatch(); };
		function showAlert(msg) { $('##payerr').html(msg).removeClass('d-none'); setBatch(); };

		var pidspecs = new Object();
		<cfloop query="local.qryMerchantProfiles">
			pidspecs[#local.qryMerchantProfiles.profileid#] = #local.qryMerchantProfiles.gatewayid#;
		</cfloop>

		function onBatchDropdownChange(batchid, batchName){
			var pid = $('##paySource').val();
			$('a.selectedBatchOption_' + pid +' .selectedBatchOptionText').html(batchName);
			$('a.selectedBatchOption_' + pid).data('selectedbatchid', batchid);
			setBatch();
		}
		function initBatchDropdown(){
			$('div[id^=batchDropdown_]').find('.dropdown-menu .dropdown-item').unbind().on('click',function(e){
				onBatchDropdownChange($(this).data('batchid'), $(this).data('batchname'));
				$(this).addClass("active").siblings().removeClass("active");
				return true;
			});

			var preSelectedBatchDropdowns = $('div[id^=batchDropdown_]').find('.dropdown-menu .dropdown-item[data-isselected="1"]');
			if(preSelectedBatchDropdowns.length > 0){
				preSelectedBatchDropdowns.each(function() {
					$(this).closest('.dropdown').find('.selectedBatchOptionText').html($(this).data('batchname'));
					$(this).closest('.dropdown').find('a[class*=selectedBatchOption_]').data('selectedbatchid', $(this).data('batchid'));
				});
				setBatch();
			}
		}
		function setBatch() {
			var pid = $('##paySource').val();

			<cfif local.useBatches is 1>
				$('##pf_' + pid + '_batchdiv1').hide();
				$('##pf_' + pid + '_batchdiv2').hide();
				$('##pf_' + pid + '_batchdiv3').hide();
		
				<!--- if gateway 1 or 2, and not pending, ask for batch. else put on batch automatically. --->
				<!--- else gateway is online and put it on batch automatically --->
				if (pidspecs[pid] == 1 || pidspecs[pid] == 2) {
					if ($('##pf_' + pid + '_acceptPending').attr('checked')) {
						var selectedBatchID = $('a.selectedBatchOption_' + pid).data('selectedbatchid');
						$('##batchid').val(selectedBatchID);
						$('##pf_' + pid + '_batchdiv2').show();
					} else {
						$('##batchid').val(-1);
						$('##pf_' + pid + '_batchdiv1').show();
					}
				} else {
					$('##batchid').val(-1);
					$('##pf_' + pid + '_batchdiv3').show();
				}
			<cfelse>
				$('##batchid').val(-1);
			</cfif>
				
			if ($('##profileid').val() == 0 || $('##batchid').val() == 0 || parseFloat($('##amount').val()) <= 0) disableSaveBtn(pid);
			else enableSaveBtn(pid);

			updatePaymentAmt(pid);
		}
		function useProfile() {
			hideAlert();
			$('##paymentTabs').children().hide();
			
			var pid = $('##paySource').val();
			$('##profileid').val(pid);
			$('##paymentTab' + pid).show();
			if (pid > 0 && $('##newBatchName' + pid).length) {
				$('##newBatchName' + pid).val(moment($('##transactionDate').val()).format('YYYYMMDD') + ' ' + $('##paySource option[value="'+pid+'"]').data('payprofilecode') + ($('##paySource option[value="'+pid+'"]').data('glaccountname').length > 0 ? (' ' + $('##paySource option[value="'+pid+'"]').data('glaccountname')) : ''));
			}
			
			setBatch();
		}
		function updatePaymentAmt(pid) {
			let amt = roundPaymentAmt($('##amount').val());
			let payNowBtnText = 'Pay Now';
			let supportPFD = $('##processFeeGL'+pid).length;
			let supportSurcharge = $('##surchargeGL'+pid).length;
			
			if (supportSurcharge || supportPFD) {
				let procFeeResult = function(r) {
					if (r.success && r.success.toLowerCase() == 'true') {
						let processingFee = Number(r.processingfees);
						let totalAmt = amt;

						if (typeof window['chargeInfo'+pid] != "undefined") {
							window['chargeInfo'+pid] = { amt:totalAmt, processingfees:0, amtincprocessingfees:roundPaymentAmt(totalAmt) };
						}

						if (supportSurcharge) {
							let selectedPayProfile = $('input[type="radio"][name="p_'+pid+'_mppid"]:checked');
							if (selectedPayProfile.length && selectedPayProfile.data('sgen') == 1) {
								totalAmt = roundPaymentAmt(totalAmt + processingFee);
							}
						} else if (supportPFD) {
							let processingFeeLabel = $('##processFeeLabelText'+pid).val();
							let amtLabelRegex =  new RegExp('{{AMOUNT}}', "ig");
							$('##processFeeLabel'+pid).html(processingFeeLabel.replace(amtLabelRegex,r.processingfeesdsplabel));

							let processingFeeBtn = $('##processFeeDonation'+pid+'_yes');
							if (typeof window['chargeInfo'+pid] != "undefined") {
								window['chargeInfo'+pid] = { amt:totalAmt, processingfees:processingFee, amtincprocessingfees:roundPaymentAmt(totalAmt + processingFee) };
							}

							if (processingFeeBtn.is(':checked')) totalAmt = roundPaymentAmt(totalAmt + processingFee);
						}
						
						payNowBtnText = 'Pay $' + formatCurrency(totalAmt).replace('.00','') + ' Now';
						$('##btnSavePayment'+pid).html(payNowBtnText);
						
						if (typeof window['p_'+pid+'_manageSurcharge'] == 'function') {
							window['p_'+pid+'_manageSurcharge']();
						} else if (typeof window['p_'+pid+'_manageProcessingFee'] == 'function') {
							window['p_'+pid+'_manageProcessingFee']();
						}
					}
				};

				var objParams = { gl: supportPFD ? $('##processFeeGL'+pid).val() : $('##surchargeGL'+pid).val(), 
									amt: amt, feepct: supportPFD ? Number($('##processFeePercent'+pid).val()) : Number($('##surchargePercent'+pid).val()),
									stateIDForTax:#val(local.qryTaxStateZIP.stateID)#, zipForTax:'#local.qryTaxStateZIP.postalcode#' };
				TS_AJX('GATEPAY','getPaymentProcessingFees',objParams,procFeeResult,procFeeResult,10000,procFeeResult);
			
			} else if (amt > 0) {
				payNowBtnText = 'Pay $' + formatCurrency(amt).replace('.00','') + ' Now';
				$('##btnSavePayment'+pid).html(payNowBtnText);
			}
		}
		function roundPaymentAmt(amt) {
			return Number(formatCurrency(amt).replace(/\$|\,/g,''));
		}
		function disableSaveBtn(pid) {
			$('##btnSavePayment'+pid).attr('disabled',true);
			if ($('.inputForm'+pid+'StepBtn').length) {
				$('.inputForm'+pid+'StepBtn').prop('disabled',true);
			}
		}
		function enableSaveBtn(pid) {
			$('##btnSavePayment'+pid).attr('disabled',false);
			if ($('.inputForm'+pid+'StepBtn').length) {
				$('.inputForm'+pid+'StepBtn').prop('disabled',false);
			}
		}
		function checkAmt() {
			var amtfld = $('##amount');
			amtfld.val(formatCurrency(amtfld.val()));
			hideAlert();
		}
		
		_FB_hasValue = function(obj, obj_type){
			if (obj_type == 'TEXT' || obj_type == 'TEXTAREA'){
				tmp = obj.value;
				tmp = tmp.replace(/^\s+/,'').replace(/\s+$/,'');
				if (tmp.length == 0) return false;
				else return true;
			} else if (obj_type == 'SELECT'){
				for (var i=0; i < obj.length; i++) {
					if (obj.options[i].selected){
						tmp = obj.options[i].value;
						tmp = tmp.replace(/^\s+/,'').replace(/\s+$/,'');
						if (tmp.length > 0) return true;
					}
				}
				return false;
			} else if (obj_type == 'SINGLE_VALUE_RADIO' || obj_type == 'SINGLE_VALUE_CHECKBOX'){
				if (obj.checked) return true;
				else return false;	
			} else if (obj_type == 'RADIO' || obj_type == 'CHECKBOX'){
				if (obj.length == undefined && obj.checked) return true;
				else{
					for (var i=0; i < obj.length; i++){
						if (obj[i].checked) return true;
					}
				}
				return false;
			}else{
				return true;
			}
		};

		<cfif len(local.strPayObj.ad)>
			<cfif arguments.event.getValue('mc_admintoolInfo.myRights.transAllocatePayment') is 1>
				<cfif local.strPayObj.lckm is not 1>
					function addPayUseCredit() {
						$('##paystep2DIV').hide();
						$('##paystep2creditloadingDIV').show();
						self.location.href='#local.addPaymentLink#&pa=selcredit&po=#arguments.event.getValue('po')#';
					}
				</cfif>
				function addPayUseMemCredit() {
					$('##paystep2DIV').hide();
					$('##paystep2creditloadingDIV').show();
					self.location.href='#local.addPaymentLink#&pa=selcreditpay&po=#arguments.event.getValue('po')#';
				}
			</cfif>
			<cfif local.strPayObj.lckm is not 1>
				function addPaySelectPayer() {
					$('##paystep2DIV').hide();
					$('##paystep2creditloadingDIV').show();
					self.location.href='#local.addPaymentLink#&pa=selmember&po=#arguments.event.getValue('po')#';
				}
			</cfif>
		</cfif>

		<cfif local.useBatches is 1>
			function createNewOpenBatch() {
				var createResult = function(r) {
					if (r.success && r.success.toLowerCase() == 'true') {
						let batchTemplate = Handlebars.compile($('##mc_openBatchDropDownTemplate').html());
						$('##divCreateOpenBatchContainer' + pid).html(batchTemplate(r));
						initBatchDropdown();
						setBatch();
					} else {
						$('##btnCreateBatch' + pid).prop('disabled',false).html('Open Batch');
						if (r.errmsg) alert(r.errmsg);
						else alert('We were unable to create a new batch. Try again.')
					}
				};

				var pid = $('##paySource').val();
				if (pid > 0) {
					var batchName = $('##newBatchName' + pid).val();
					if (!batchName.length) {
						$('##newBatchName' + pid).addClass('is-invalid');
						return false;
					} else {
						$('##newBatchName' + pid).removeClass('is-invalid');
					}

					$('##btnCreateBatch' + pid).prop('disabled',true).html('Opening Batch...');

					var objParams = {batchName:batchName, payProfileID:pid, depositDate:$('##transactionDate').val() };
					TS_AJX('ADMBATCH','createOpenBatch',objParams,createResult,createResult,10000,createResult);
				}
			}
		</cfif>

		$(function() {
			<cfif local.useBatches is 1>
				mca_setupDatePickerField('transactionDate');

				var batchDID = moment(mca_getParsedDateTime($('##transactionDate').val())).format('YYYYMD');
				$('##transactionDate').change(function() {
					batchDID = moment(mca_getParsedDateTime($('##transactionDate').val())).format('YYYYMD');
					$('.batchdateid').html(batchDID);
				});
				$('.batchdateid').html(batchDID);
			</cfif>

			mca_setupCalendarIcons('frmPayment');
			initBatchDropdown();

			<cfif local.lastSavePayment.count()>
				$('##paySource option[value="#local.lastSavePayment.profileid#"]').attr("selected","selected");
				$('##paySource').change();
			</cfif>

			var amt_timeout;
			$('##amount').keypress(function() { 
				if (amt_timeout) clearTimeout(amt_timeout);
           		amt_timeout = setTimeout("checkAmt()",1500);
        	});
        	$('##amount').blur(function() { 
        		if (amt_timeout) clearTimeout(amt_timeout);
        		checkAmt();
        	});

			top.MCModalUtils.setTitle('Add Payment#len(local.strPayObj.t) ? " for #encodeForJavaScript(local.strPayObj.t)#" : ""#');
			top.MCModalUtils.buildFooter({});
		});
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.js)#">

<cfoutput>
<div id="paystep2DIV" class="p-3">
	<form name="frmPayment" id="frmPayment" action="#local.savePaymentLink#" method="post" onsubmit="return checkPayForm();" autocomplete="off">
	<input type="hidden" name="po" id="po" value="#arguments.event.getValue('po')#">
	<input type="hidden" name="profileid" id="profileid" value="0">
	<input type="hidden" name="pdateNow" id="pdateNow" value="#dateformat(now(),'m/d/yyyy')#">
	<input type="hidden" name="batchid" id="batchid" value="0">

	<div class="d-flex">
		<div class="col border-right pl-0">
			<cfif local.strPayObj.ta gt 0>
				<h6 id="addPayAmount" class="text-primary font-weight-bold">#dollarFormat(local.strPayObj.ta)# Due</h6>
				<div id="addPayAssignedTo" class="mt-2">
					<div class="small text-dim">Due From:</div>
					<h6 class="mb-0">#encodeForHTML("#local.qryAssignedTo.firstName# #local.qryAssignedTo.lastName# (#local.qryAssignedTo.memberNumber#)")#</h6>
					<cfif len(local.qryAssignedTo.company)><div class="small">#encodeForHTML(local.qryAssignedTo.company)#</div></cfif>
				</div>
			</cfif>
			<div id="addPayPayer" class="mt-2">
				<div class="small text-dim">Payer:</div>
				<h6 class="mb-0">#encodeForHTML("#local.qryPayee.firstName# #local.qryPayee.lastName# (#local.qryPayee.memberNumber#)")#</h6>
				<cfif len(local.qryPayee.company)><div class="small">#encodeForHTML(local.qryPayee.company)#</div></cfif>
			</div>
		</div>
		<div class="col d-flex">
			<div id="addPayOtherLinks" class="mx-auto">
				<div class="font-weight-bold mb-2">Other options:</div>
				<cfif len(local.strPayObj.ad)>
					<cfif arguments.event.getValue('mc_admintoolInfo.myRights.transAllocatePayment') is 1>
						<cfif local.strMemberCredit.unallocatedamount gt 0>
							<div class="mb-2 text-nowrap"><a href="javascript:addPayUseMemCredit();"><i class="fa-solid fa-coins text-goldenrod"></i> Use payer's #dollarformat(local.strMemberCredit.unallocatedamount)# credit balance</a></div>
						</cfif>
						<cfif local.strPayObj.lckm is not 1>
							<cfif arguments.event.valueExists('ncmsg')>
								<div class="mb-2 text-nowrap"><i class="fa-solid fa-coins text-goldenrod"></i> <span class="text-muted">Use <cfif local.strMemberCredit.unallocatedamount gt 0>another member's<cfelse>a member's</cfif> credit balance<br/>(No credit balances available to use.)</span></div>
							<cfelse>
								<div class="mb-2 text-nowrap"><a href="javascript:addPayUseCredit();"><i class="fa-solid fa-coins text-goldenrod"></i> Use <cfif local.strMemberCredit.unallocatedamount gt 0>another member's<cfelse>a member's</cfif> credit balance</a></div>
							</cfif>
						</cfif>
					</cfif>
					<cfif local.strPayObj.lckm is not 1>
						<div class="mb-2 text-nowrap"><a href="javascript:addPaySelectPayer();"><i class="fa-solid fa-user-group"></i> Select a different payer</a></div>
					</cfif>
				</cfif>
				<div class="text-nowrap"><a href="javascript:top.MCModalUtils.hideModal();"><i class="fa-solid fa-circle-xmark text-danger"></i> Cancel and close window</a></div>
			</div>
		</div>
	</div>

	<div id="tblPayInfo" class="mt-5">
		<div class="form-row">
			<div class="col">
				<div class="form-label-group mb-0">
					<div class="input-group dateFieldHolder">
						<cfif local.useBatches is 1>
							<cfif local.lastSavePayment.count()>
								<cfset local.useDate = local.lastSavePayment.transDate>
							<cfelse>
								<cfset local.useDate = dateformat(now(),'m/d/yyyy')>
							</cfif>
							<input type="text" name="transactionDate" id="transactionDate" value="#local.useDate#" class="form-control dateControl">
						<cfelse>
							<input type="hidden" name="transactionDate" id="transactionDate" value="#dateformat(now(),'m/d/yyyy')#">
							<input type="text" name="transactionDateReadOnly" id="transactionDateReadOnly" value="#dateformat(now(),'m/d/yyyy')#" class="form-control" readonly>
						</cfif>
						<div class="input-group-append">
							<span class="input-group-text cursor-pointer calendar-button" data-target="transactionDateReadOnly"><i class="fa-solid fa-calendar"></i></span>
						</div>
						<label for="transactionDate">Payment Date</label>
					</div>
				</div>
				<cfif local.useBatches NEQ 1>
					<div class="form-text mt-1">
						<a href="javascript:void(0);" class="font-size-xs" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" data-html="true" title="The payment date is set to today and cannot be changed<br/>until self-managed batches are activated on your website.<br/>Contact MemberCentral for assistance.">Why can't I change this?</a>
					</div>
				</cfif>
			</div>
			<div class="col">
				<div class="input-group flex-nowrap">
					<div class="input-group-prepend">
						<span class="input-group-text px-3">$</span>
					</div>
					<div class="form-label-group flex-grow-1 mb-0">
						<input type="text" name="amount" id="amount" value="#numberformat(local.strPayObj.pa,'9.99')#" class="form-control" autocomplete="off">
						<label for="amount">Payment Amount</label>
					</div>
					<cfif local.showCurrencyType is 1>
						<div class="input-group-append">
							<span class="input-group-text px-3">#local.defaultCurrencyType#</span>
						</div>
					</cfif>
				</div>
			</div>
			<div class="col">
				<div class="form-label-group">
					<select id="paySource" name="paySource" class="custom-select" onchange="useProfile();">
						<option value="0"></option>
						<cfloop query="local.qryMerchantProfiles">
							<option value="#local.qryMerchantProfiles.profileID#" data-payprofilecode="#local.qryMerchantProfiles.profileCode#" data-glaccountname="#encodeForHTMLAttribute(local.qryMerchantProfiles.glAccountName)#">#local.qryMerchantProfiles.profileName#</option>
						</cfloop>
					</select>
					<label for="paySource">Payment Profile</label>
				</div>
			</div>
		</div>
	</div>
	
	<div id="payerr" class="alert alert-danger d-none my-2"></div>
	
	<div id="paymentTabs">

	<cfloop query="local.qryMerchantProfiles">
		<cfset local.thisProfileID = local.qryMerchantProfiles.profileID>

		<cfset local.strGIFParams = {
				siteid=local.qryMerchantProfiles.siteID,
				profilecode=local.qryMerchantProfiles.profileCode,
				pmid=local.qryPayee.memberid,
				usePopup=false,
				usePopupDIVName='pf_#local.thisProfileID#_',
				offerDelete=true,
				adminForm=1,
				editMode='controlPanelPayment',
				paymentFeatures=application.objPayments.setDefaultPayFeaturesStruct()
				}>
		
		<!--- showCOF can be set to no by the payment object --->
		<cfif local.strPayObj.xcof is 1>
			<cfset local.strGIFParams.showCOF = false>
		<cfelse>
			<cfset local.strGIFParams.showCOF = true>
		</cfif>

		<!--- customerID can be overridden by the payment object --->
		<cfif len(local.strPayObj.ocid)>
			<cfset local.strGIFParams.overrideCustomerID = local.strPayObj.ocid>
		</cfif>

		<cfif local.qryMerchantProfiles.enableProcessingFeeDonation AND val(local.qryMerchantProfiles.processFeeDonationFeePercent) GT 0>
			<cfset local.strGIFParams.paymentFeatures.processingFee = { 
				"enable":1, 
				"select":val(local.qryMerchantProfiles.processFeeDonationDefaultSelect) EQ 1 ? 1 : 0, 
				"label":replaceNoCase(local.qryMerchantProfiles.processFeeOtherPaymentsFELabel,"{{AMOUNT}}",0), 
				"denylabel":local.qryMerchantProfiles.processFeeOtherPaymentsFEDenyLabel,
				"title":local.qryMerchantProfiles.processFeeDonationFETitle,
				"msg":local.qryMerchantProfiles.processFeeDonationFEMsg 
			}>
		</cfif>

		<cfset local.strGIFParams.paymentFeatures.surcharge.enable = local.qryMerchantProfiles.enableSurcharge>

		<cfset local.strPaymentForm = application.objPayments.showGatewayInputForm(argumentcollection=local.strGIFParams)>
	
		<cfif len(local.strPaymentForm.headcode)>
			<cfhtmlhead text="#application.objCommon.minText(local.strPaymentForm.headcode)#">
		</cfif>
	
		<div id="paymentTab#local.thisProfileID#" style="display:none;">
		<div id="pf_#local.thisProfileID#_">
			<cfif len(trim(local.strPaymentForm.inputForm))>
				<div class="card card-box shadow-none p-3 my-3">
					<div>#replaceNoCase(local.strPaymentForm.inputForm,'fld_','p_#local.thisProfileID#_fld_','ALL')#</div>
				</div>
			</cfif>
			<cfif listFind("1,2",local.qryMerchantProfiles.gatewayID)>
				<div class="d-flex flex-row align-items-center mt-3">
					<div class="form-check-inline">
						<input class="form-check-input" type="checkbox" id="pf_#local.thisProfileID#_acceptPending" name="pf_#local.thisProfileID#_acceptPending" value="1" onclick="setBatch();" <cfif local.checkPendingBox is 1>checked</cfif>>
						<label class="form-check-label" for="pf_#local.thisProfileID#_acceptPending">Automatically verify this payment and bypass the pending status.</label>
					</div>
					<a class="d-inline font-size-xs" href="javascript:void(0);" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" data-html="true" title="<b>Offline Payment Verification</b><br/>This offline payment will be recorded as <i>pending</i> until receipt of payment is verified.<br/>Pending payments do not appear in reports or batches until they are verified." />What's this?</a>
				</div>
				
				<cfif local.useBatches is 1>
					<cfstoredproc datasource="#application.dsn.memberCentral.dsn#" procedure="tr_getOpenBatches">
						<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#local.qryMerchantProfiles.orgid#">
						<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#local.thisProfileID#">
						<cfprocresult name="local.qryOpenBatches">
					</cfstoredproc>

					<div id="pf_#local.thisProfileID#_batchdiv1" class="pt-3" style="display:none;">
						<span class="d-block">Batch:</span>
						<span class="d-block">This pending payment will be held on a pending payment batch until the payment is verified.</span>
					</div>
					<div id="pf_#local.thisProfileID#_batchdiv2" class="pt-3" style="display:none;">
						<cfif local.qryOpenBatches.recordcount>
							<div>
								<span class="d-block">Batch:</span>
								
								<div id="batchDropdown_#local.thisProfileID#" class="dropdown show mt-2">
									<a class="btn btn-secondary selectedBatchOption_#local.thisProfileID# w-100" href="javascript:void(0);" role="button" id="dropdownMenuLink" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" data-selectedbatchid="0">
										<div class="d-flex">
											<div class="selectedBatchOptionText mr-auto">Select an open batch for this payment</div>
											<div><i class="fa fa-chevron-down" aria-hidden="true"></i></div>
										</div>
									</a>
									<div class="dropdown-menu w-100" aria-labelledby="dropdownMenuLink">
										<a class="dropdown-item" href="javascript:void(0);" data-batchid="0" data-batchname="Select an open batch for this payment">
											Select an open batch for this payment
										</a>
										<cfloop query="local.qryOpenBatches">
											<cfset local.batchName = replace(left(local.qryOpenBatches.batchname,90),chr(34),'','ALL')>
											<cfif len(local.qryOpenBatches.batchname) gt 90>
												<cfset local.batchName &= "...">
											</cfif>
											<cfset local.optionText = "#dateformat(local.qryOpenBatches.depositDate,'m/d/yyyy')# - #local.batchName#">

											<a class="dropdown-item py-1" 
													href="javascript:void(0);" 
													data-batchid="#local.qryOpenBatches.batchid#" 
													data-batchname="#local.optionText#"
													<cfif local.lastSavePayment.count() and local.lastSavePayment.keyExists("batchid") and local.lastSavePayment.batchid eq local.qryOpenBatches.batchID>
														data-isselected="1"
													</cfif>
													>

												<table class="table table-sm table-borderless mb-0">
													<tr>
														<td width="50%" class="text-wrap font-weight-bold">#local.batchName#</td>
														<td width="50%" class="text-bottom text-wrap font-italic">Created by #local.qryOpenBatches.createdByMember#</td>
													</tr>
													<tr>
														<td>#dateformat(local.qryOpenBatches.depositDate,'mm/dd/yyyy')#</td>
														<td>
															<span>Control: #dollarformat(local.qryOpenBatches.controlAmt)# (#local.qryOpenBatches.controlCount#)</span>
															<span class="ml-2">Actual: #dollarFormat(local.qryOpenBatches.actualAmt)# (#local.qryOpenBatches.actualCount#)</span>
														</td>
													</tr>
												</table>
											</a>
										</cfloop>
									</div>
								</div>
							</div>
						<cfelse>
							<cfif local.hasBatchCreateRights>
								<div id="divCreateOpenBatchContainer#local.thisProfileID#">
									<span class="d-block">Batch:</span>
									<div class="alert alert-danger">
										There are no open batches linked to this payment method.
										<div class="my-2">
											Quickly open a batch by providing the name of the batch:
										</div>
										<div class="form-group row">
											<div class="col">
												<div class="input-group input-group-sm">
													<input type="text" name="newBatchName#local.thisProfileID#" id="newBatchName#local.thisProfileID#" value="" class="form-control form-control-sm" maxlength="400">
													<div class="input-group-append">
														<button type="button" class="btn input-group-text" name="btnCreateBatch#local.thisProfileID#" id="btnCreateBatch#local.thisProfileID#" onclick="createNewOpenBatch();">Open Batch</button>
													</div>
												</div>
											</div>
										</div>
									</div>
								</div>
							<cfelse>
								<input type="hidden" name="pf_#local.thisProfileID#_batchid" id="pf_#local.thisProfileID#_batchid" value="0">
								<span class="d-block">Batch:</span>
								<div class="alert alert-danger">
									There are no open batches linked to this payment method.<br/>
									Open a batch before adding payment.
								</div>
							</cfif>
						</cfif>
					</div>
				</cfif>

			<cfelseif local.useBatches is 1 and local.qryMerchantProfiles.gatewayID is 16>
				<div id="pf_#local.thisProfileID#_batchdiv3" class="pt-3" style="display:none;">
					<span class="d-block">Batch:</span>
					<span class="d-block">This payment will be added to an open <i>PPD or CCD #local.qryMerchantProfiles.profilecode#</i> batch based on the account chosen.</span>
				</div>			
			<cfelseif local.useBatches is 1>
				<div id="pf_#local.thisProfileID#_batchdiv3" class="pt-3" style="display:none;">
					<span class="d-block">Batch:</span>
					<span class="d-block">This payment will be added to an open <i><span class="batchdateid"></span> #local.qryMerchantProfiles.profilecode#</i> batch.</span>
				</div>
			</cfif>
			<cfif local.qryMerchantProfiles.enableSurcharge>
				<input type="hidden" name="surchargePercent#local.thisProfileID#" id="surchargePercent#local.thisProfileID#" value="#val(local.qryMerchantProfiles.surchargePercent)#">
				<input type="hidden" name="surchargeGL#local.thisProfileID#" id="surchargeGL#local.thisProfileID#" value="#val(local.qryMerchantProfiles.surchargeRevenueGLAccountID)#">
			<cfelseif local.qryMerchantProfiles.enableProcessingFeeDonation AND val(local.qryMerchantProfiles.processFeeDonationFeePercent)>
				<input type="hidden" name="processFeeLabelText#local.thisProfileID#" id="processFeeLabelText#local.thisProfileID#" value="#encodeForHTMLAttribute(local.qryMerchantProfiles.processFeeOtherPaymentsFELabel)#">
				<input type="hidden" name="processFeePercent#local.thisProfileID#" id="processFeePercent#local.thisProfileID#" value="#val(local.qryMerchantProfiles.processFeeDonationFeePercent)#">
				<input type="hidden" name="processFeeGL#local.thisProfileID#" id="processFeeGL#local.thisProfileID#" value="#val(local.qryMerchantProfiles.processFeeDonationRenevueGLAccountID)#">
			</cfif>
			<div id="divBtnWrapper#local.thisProfileID#" class="mt-3">
				<button type="submit" name="btnSavePayment#local.thisProfileID#" id="btnSavePayment#local.thisProfileID#" class="btn btn-sm btn-primary" disabled>Save Payment</button>
			</div>
		</div>
		</div>
		
		<cfif len(local.strPaymentForm.jsvalidation) or local.qryMerchantProfiles.gatewayClass eq "creditCard">
			<cfsavecontent variable="local.extrapayJS">
				<cfoutput>
				#local.extrapayJS#
				if ($('##profileid').val()==#local.thisProfileID#) {
					#replaceNoCase(local.strPaymentForm.jsvalidation,'fld_','p_#local.thisProfileID#_fld_','ALL')#
					<cfif local.qryMerchantProfiles.gatewayClass eq "creditCard">
						if ($('##transactionDate').val()!=$('##pdateNow').val()) arrReq[arrReq.length] = 'The payment date for credit card transactions must be today\'s date.';
					</cfif>
				}
				</cfoutput>
			</cfsavecontent>
		</cfif>
	</cfloop>
	
	</div>
	</form>
</div>
<cfif len(local.strPayObj.ad)>
	<div id="paystep2creditloadingDIV" class="mt-4 text-center" style="display:none;">
		<div class="spinner-border" role="status"></div>
		<div class="mt-2">Please wait while we load the information.</div>
	</div>
</cfif>

<cfif local.useBatches is 1>
	<script id="mc_openBatchDropDownTemplate" type="text/x-handlebars-template">
	<div>Batch:</span>
	<div id="batchDropdown_{{payprofileid}}" class="dropdown show mt-2">
		<a class="btn btn-secondary selectedBatchOption_{{payprofileid}} w-100" href="javascript:void(0);" role="button" id="dropdownMenuLink" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" data-selectedbatchid="0">
			<div class="d-flex">
				<div class="selectedBatchOptionText mr-auto">Select an open batch for this payment</div>
				<div><i class="fa fa-chevron-down" aria-hidden="true"></i></div>
			</div>
		</a>
		<div class="dropdown-menu w-100" aria-labelledby="dropdownMenuLink">
			<a class="dropdown-item" href="javascript:void(0);" data-batchid="0" data-batchname="Select an open batch for this payment">
				Select an open batch for this payment
			</a>
			{{##each arropenbatches}}
				<a class="dropdown-item py-1" 
						href="javascript:void(0);" 
						data-batchid="{{batchid}}" 
						data-batchname="{{batchname}}"
						{{##if isselected}}data-isselected="1"{{/if}}
						>

					<table class="table table-sm table-borderless mb-0">
						<tr>
							<td width="50%" class="text-wrap font-weight-bold">{{batchname}}</td>
							<td width="50%" class="text-bottom text-wrap font-italic">Created by {{createdbymember}}</td>
						</tr>
						<tr>
							<td>{{depositdate}}</td>
							<td>
								<span>Control: {{controlamt}} ({{controlcount}})</span>
								<span class="ml-2">Actual: {{actualamt}} ({{actualcount}})</span>
							</td>
						</tr>
					</table>
				</a>
			{{/each}}
		</div>
	</div>
	</script>
</cfif>
</cfoutput>

<cfsavecontent variable="local.validationJS">
	<cfoutput>
	<script language="javascript">
	function checkPayForm() {
		hideAlert();

		var arrReq = new Array();
		var pid = $('##paySource').val();

		$('##amount').val(formatCurrency($('##amount').val()));
		if (parseFloat($('##amount').val()) <= 0) arrReq[arrReq.length] = 'You may not add a zero dollar payment. Enter a valid payment amount.';
		if (pid == '') arrReq[arrReq.length] = 'Select which payment method you are using for this payment.';
	
		<cfif len(local.extrapayJS)>
			var thisForm = document.forms["frmPayment"];
			#local.extrapayJS#
		</cfif>
		
		if (arrReq.length > 0) {
			var msg = '<b>The following requires your attention:</b><br/>';
			for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
			showAlert(msg);
			if (pid != '') enableSaveBtn(pid);
			return false;
		} else {
			disableSaveBtn(pid);
			return true;
		}
	};
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.validationJS)#">
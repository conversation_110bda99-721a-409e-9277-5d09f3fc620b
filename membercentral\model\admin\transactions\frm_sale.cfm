<cfsavecontent variable="local.saleJS">
	<cfoutput>
	<style type="text/css">
		##invoiceDropdown .dropdown-menu { transform:translate3d(0px, -207px, 0px) !important; height:200px; overflow-y:auto; }
	</style>
	#local.strGLAcctWidget.js#
	<script type="text/javascript">
		function hideAlert() { $('##saleerr').html('').addClass('d-none'); checkBtn(); };
		function showAlert(msg) { $('##saleerr').html(msg).removeClass('d-none'); checkBtn(); };
		function disableSaveBtn() { top.$('##btnMCModalSave').attr('disabled',true); }
		function enableSaveBtn() { top.$('##btnMCModalSave').attr('disabled',false); }

		function checkAmt() {
			$('input.addSaleAmt').each(function(index) { $(this).val(formatCurrency($(this).val())); });
		}
		function checkBtn() {
			checkAmt();

			var defSchAmt = 0;
			var regDtOK = 1;
			var saleAmount = parseFloat($('##amount').val().replace(/[^0-9\.]+/g,""));
			var numRows = $('tbody##tblDefSchRows > tr').length;
			if (numRows) {
				$('tbody##tblDefSchRows input.addSaleDateInput').each(function(index) {
					var schDate = mca_getParsedDateTime($(this).val());
					var saleDate = mca_getParsedDateTime($('input##transactionDate').val());
					if(schDate < saleDate) { regDtOK=0; return false; }
				});
				defSchAmt = getTotalScheduleAmount();
			}

			var badDetail = ($('##detail').val().length == 0);
			var badAmount = (saleAmount <= 0);
			var badGL = ($('##GLAccountID').val() == 0 || $('##GLAccountID').val().length == 0);
			var badInvoice = ($('##invoiceID').val() == '');
			var badtaxState = ($('##stateIDforTax').val().length == 0);
			var badtaxZIP = ($('##zipForTax').val().length == 0);
			var isValidZip = !badtaxState && !badtaxZIP && mc_isValidBillingZip($('##zipForTax').val(),$('##stateIDforTax').val(),'');
			if (numRows==0) {
				var badDefAmt = false;
				var badDefDate = false;
			} else {
				var badDefAmt = (formatCurrency(defSchAmt) != formatCurrency(saleAmount));
				var badDefDate = (regDtOK == 0);
			}

			if (badDetail || badAmount || badGL || badInvoice || badtaxState || badtaxZIP || !isValidZip || badDefAmt || badDefDate) disableSaveBtn();
			else enableSaveBtn();
		}
		function getTotalScheduleAmount(){
			var defSchAmt = 0;
			$('tbody##tblDefSchRows input.addSaleAmt').each(function(index) { defSchAmt += parseFloat($(this).val().replace(/[^0-9\.]+/g,"")); });
			return defSchAmt;
		}
		function onInvoiceDropdownChange(invoiceID, invoiceName){
			$('a.selectedInvoiceOption .selectedInvoiceText').html(invoiceName);
			$('a.selectedInvoiceOption').data('selectedinvoiceid', invoiceID);
			$('##invoiceID').val(invoiceID);
		}
		function initInvoiceDropdown(){
			onInvoiceDropdownChange('', 'Select the invoice');
			$('##invoiceDropdown .dropdown-item').unbind().on('click',function(e){
				onInvoiceDropdownChange($(this).data('invoiceid'), $(this).data('invoicename'));
				$(this).addClass("active").siblings().removeClass("active");
				return true;
			});
			$('##invoiceDropdown .dropdown-item:first').trigger('click');
		}
		function loadInvoicesDefSched() {
			var loadInvoicesResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true' && r.arropeninvoices.length > 0) {
					var invoiceSelectOptions = '';
					$.each(r.arropeninvoices, function(key,item) {
						invoiceSelectOptions += '<a class="dropdown-item py-1" href="javascript:void(0);" data-invoiceid="'+ item.invoiceid +'" data-invoicename="'+ item.invoicenumber +'">';
						invoiceSelectOptions += '<table class="table table-sm table-borderless"><tr><td width="40%" class="font-weight-bold">'+ item.invoicenumber +'</td><td width="30%" class="text-center">'+ item.duedatestr +'</td><td width="30%" class="text-right">'+ item.invamtstr +'</td></tr></table>';
						invoiceSelectOptions += '</a>';
					});
					$('div##invoiceDropdownMenu').append(invoiceSelectOptions);
				<cfif local.myRightsInvoiceAdmin.invoiceCreate is not 1>
					} else {
						showAlert('No available invoices were found and you do not have permission to create one.');
						disableSaveBtn();
				</cfif>
				}
				
				$('##addSaleInvSelector').show();
				if (r.isdeferred == 1) {
					$('##addSaleDefSched').show();
					resetDefSchedule();
				}
				initInvoiceDropdown();
				checkBtn();
			};
			
			$('div##invoiceDropdownMenu').html('');
			<cfif local.myRightsInvoiceAdmin.invoiceCreate is 1>
				$('div##invoiceDropdownMenu').html('<a class="dropdown-item py-1" href="javascript:void(0);" data-invoiceid="0" data-invoicename="Create a new invoice for this sale"><div class="p-1 font-weight-bold">Create a new invoice</div></a>');
			</cfif>

			var objParams = { memberid:#local.qryAssignee.memberid#, revenueglaccountid:$('##GLAccountID').val() };
			TS_AJX('ADMTRANSACTIONS','getOpenInvoicesAndDeferredSchedForAddSale',objParams,loadInvoicesResult,loadInvoicesResult,10000,loadInvoicesResult);
		}
		function hideInvoicesDefSched() {
			$('##addSaleInvSelector, ##addSaleDefSched').hide();
			$('tbody##tblDefSchRows').html('');
		}
		function resetDefSchedule() {
			$('tbody##tblDefSchRows').html('');
			addMoreDates();
		}
		function addMoreDates() {
			var numRows = $('tbody##tblDefSchRows > tr').length;
			var nextRow = numRows+1;

			var dateControlStr = '<div class="input-group input-group-sm"><input type="text" name="defsch_'+nextRow+'_dt" id="defsch_'+nextRow+'_dt" class="form-control form-control-sm dateControl addSaleDateInput" value="" autocomplete="off"><div class="input-group-append"><span class="input-group-text cursor-pointer calendar-button" data-target="defsch_'+nextRow+'_dt"><i class="fa-solid fa-calendar"></i></span></div></div>';
			var amtControlStr = '<div class="input-group input-group-sm"><div class="input-group-prepend"><span class="input-group-text">$</span></div><input class="form-control form-control-sm addSaleAmt" type="text" name="defsch_'+nextRow+'_amt" id="defsch_'+nextRow+'_amt" autocomplete="off" value="0.00"></div>';
			$('tbody##tblDefSchRows').append('<tr><td>'+ dateControlStr +'</td><td width="5"></td><td>'+ amtControlStr +'</td><td>' + (nextRow > 1 ? '<a href="javascript:removeDates('+nextRow+');" title="remove"><i class="fa-solid fa-circle-xmark text-danger"></i></a> ' : '') + '</td></tr>');
			if (nextRow == 1) {
				$('input##defsch_'+nextRow+'_amt').val($('input##amount').val());
				$('input##defsch_'+nextRow+'_dt').val($('input##transactionDate').val());
				mca_setupDatePickerField('defsch_'+nextRow+'_dt');
			} else {
				for (var i=numRows; i>0; i--) { 
					if ($('input##defsch_'+i+'_dt').val().length > 0) {
						var prevDate = mca_getParsedDateTime($('input##defsch_'+i+'_dt').val());
						break;
					}
				}
				var nextDate = new Date(prevDate);
				nextDate.setMonth(nextDate.getMonth() + 1);
				$('input##defsch_'+nextRow+'_dt').val(moment(nextDate).format('M/D/YYYY'));
				mca_setupDatePickerField('defsch_'+nextRow+'_dt');
				
				var numVisibleRows = $('tbody##tblDefSchRows > tr:visible').length;
				spreadScheduleAmts(numVisibleRows);
			}
			mca_setupCalendarIcons('frmSale');
			checkBtn();
		}
		function removeDates(i) {
			$('input##defsch_'+i+'_dt').removeClass('addSaleDateInput').datetimepicker('destroy').val('');
			$('input##defsch_'+i+'_amt').removeClass('addSaleAmt').val('').closest("tr").hide();
			var numVisibleRows = $('tbody##tblDefSchRows > tr:visible').length;
			spreadScheduleAmts(numVisibleRows);
			checkBtn();
		}
		function spreadScheduleAmts(i) {
			var fullAmt = parseFloat($('##amount').val().replace(/[^0-9\.]+/g,""));
			var eachAmt = parseFloat(fullAmt / i);
			$('tbody##tblDefSchRows input.addSaleAmt').each(function(index) { $(this).val(formatCurrency(eachAmt)); });
			var defSchAmt = getTotalScheduleAmount();
			if (fullAmt != defSchAmt)
				$('tbody##tblDefSchRows input.addSaleAmt').first().val(formatCurrency(eachAmt+(fullAmt-defSchAmt)));
		}
		function validateTransactionDate() {
			const transactionDateField = document.getElementById("transactionDate");
			const dateValue = transactionDateField.value.trim();
			
			if (!dateValue) {
				showAlert("Sale Date is required.");
				return false;
			}

			const parsedDate = new Date(dateValue);
			const minDate = new Date("1753-01-01");
			const maxDate = new Date("9999-12-31");

			if (isNaN(parsedDate)) {
				showAlert("Invalid sale date.");
				return false;
			} else if (parsedDate < minDate || parsedDate > maxDate) {
				showAlert("Invalid sale date.");
				return false;
			}

			hideAlert();
			return true;
		}		
		function validateFormOnSubmit(event) {
			if (!validateTransactionDate()) {
				event.preventDefault();
				return false;
			}
			showLoading();
			return true;
		}

		function showLoading() {
			$('div##saleTitleSection, div##divSaleForm').hide();
			$('div##divSaleFormloadingDIV').show();
			top.$('##btnMCModalSave').remove();
		}

		$(function() {
			mca_setupMultipleDatePickerFields($('##frmSale'),'addSaleDateInput');
			mca_setupCalendarIcons('frmSale');

			var chk_timeout;
			$(document).on('keypress','input.addSaleAmt, ##detail, ##zipForTax', function() {
				if (chk_timeout) clearTimeout(chk_timeout);
				chk_timeout = setTimeout("checkBtn()",1500);
			});
			$(document).on('blur','input.addSaleAmt, input.addSaleDateInput, ##detail, ##zipForTax', function() {
				if (chk_timeout) clearTimeout(chk_timeout);
				checkBtn();
			});
			$(document).on('change','##stateIDforTax', function() {
				if (chk_timeout) clearTimeout(chk_timeout);
				checkBtn();
			});

			top.MCModalUtils.buildFooter({
				classlist: 'd-flex justify-content-between',
				showclose: true,
				showextrabutton: true,
				extrabuttonclass: 'btn-primary',
				extrabuttononclickhandler: '$("##MCModalBodyIframe")[0].contentWindow.$("##frmSale :submit").click',
				extrabuttonlabel: 'Save Sale'
			});
			disableSaveBtn();
		});
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.saleJS)#">

<cfoutput>
<form name="frmSale" id="frmSale" class="p-3" action="#local.saveSaleLink#" method="post" onsubmit="validateFormOnSubmit(event);">
<input type="hidden" name="mid" id="mid" value="#local.qryAssignee.memberid#">
<input type="hidden" name="GLAccountID" id="GLAccountID" value="0" />

<!--- hidden submit triggered from parent --->
<button type="submit" class="d-none"></button>

<div class="row no-gutters mb-3" id="saleTitleSection">
	<div class="col">
		<div id="addSaleAssignee">
			<span class="font-weight-bold">Assignee:</span>
			<span class="d-block">#encodeForHTML(local.qryAssignee.assigneeName)#</span>
			<cfif len(local.qryAssignee.assigneeCompany)><span class="d-block">#encodeForHTML(local.qryAssignee.assigneeCompany)#</span></cfif>
		</div>
	</div>
</div>

<div id="saleerr" class="alert alert-danger d-none mb-2"></div>
<div id="divSaleForm">
	<div class="row no-gutters mt-2">
		<div class="col">
			<label for="transactionDate" class="font-weight-bold">Sale Date:</label>
			<div class="input-group input-group-sm">
				<input type="text" name="transactionDate" id="transactionDate" value="#dateformat(now(),'m/d/yyyy')#" class="form-control form-control-sm dateControl addSaleDateInput" autocomplete="off">
				<div class="input-group-append">
					<span class="input-group-text cursor-pointer calendar-button" data-target="transactionDate"><i class="fa-solid fa-calendar"></i></span>
				</div>
			</div>
		</div>
		<div class="col mx-3">
			<label for="amount" class="font-weight-bold">Sale Amount:</label>
			<div class="d-flex align-items-center">
				<div class="input-group input-group-sm">
					<div class="input-group-prepend">
						<span class="input-group-text">$</span>
					</div>
					<input type="text" name="amount" id="amount" value="0.00" class="form-control form-control-sm addSaleAmt" autocomplete="off">
				</div>
				<cfif arguments.event.getValue('mc_siteinfo.showCurrencyType') is 1><span class="ml-1">#arguments.event.getValue('mc_siteinfo.defaultCurrencyType')#</span></cfif>
			</div>
		</div>
		<div class="col-6 font-weight-bold">
			<label for="detail">Sale Detail:</label>
			<input type="text" name="detail" id="detail" value="" class="form-control form-control-sm" autocomplete="off">
		</div>
	</div>

	<!--- prompt for missing tax information --->
	<cfif NOT local.stateIDforTax gt 0 OR NOT len(local.zipForTax)>
		<div class="row no-gutters mt-3">
			<div class="col">
				<div class="font-weight-bold">Tax Information Required:</div>
				<div class="font-size-sm">Assignee's billing address is missing this information.</div>
			</div>
			<div class="col">
				<cfset local.qryStates = application.objCommon.getStates()>
				<select id="stateIDforTax" name="stateIDforTax" class="form-control form-control-sm mb-1">
					<option value="">Select State/Province</option>
					<cfset local.currentCountryID = 0>
					<cfloop query="local.qryStates">
						<cfif local.qryStates.countryID neq local.currentCountryID>
							<cfset local.currentCountryID = local.qryStates.countryID>
							<cfoutput><optgroup label="#local.qryStates.country#"></cfoutput>
						</cfif>
						<cfoutput><option value="#local.qryStates.stateID#"<cfif local.stateIDforTax eq local.qryStates.stateID> selected</cfif>>#local.qryStates.stateName# (#local.qryStates.stateCode#)</option></cfoutput>
						<cfif local.qryStates.currentrow eq local.qryStates.recordcount or local.qryStates.countryID[local.qryStates.currentrow+1] neq local.currentCountryID>
							</optgroup>
						</cfif>
					</cfloop>
				</select>
				<input type="text" id="zipForTax" name="zipForTax" class="form-control form-control-sm" value="#local.zipForTax#" placeholder="Enter Postal Code" maxlength="25">
			</div>
		</div>
	<cfelse>
		<input type="hidden" id="stateIDforTax" name="stateIDforTax" value="#local.stateIDforTax#">
		<input type="hidden" id="zipForTax" name="zipForTax" value="#local.zipForTax#">
	</cfif>

	<table class="table table-sm table-borderless mt-3">
	<tr>
		<td width="30%" class="align-top font-weight-bold">Revenue Account:</td>
		<td width="70%" class="align-top">
			#local.strGLAcctWidget.html#
		</td>
	</tr>
	<tbody id="addSaleInvSelector" style="display:none;">
		<tr>
			<td class="align-top font-weight-bold">Invoice:</td>
			<td class="align-top">
				<input type="hidden" name="invoiceID" id="invoiceID" value="">

				<div id="invoiceDropdown" class="dropdown show mt-2">
					<a class="btn btn-secondary w-100 selectedInvoiceOption" href="javascript:void(0);" role="button" id="dropdownMenuLink" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" data-selectedinvoiceid="0">
						<div class="d-flex">
							<div class="selectedInvoiceText mr-auto">Select the invoice</div>
							<div><i class="fa fa-chevron-down" aria-hidden="true"></i></div>
						</div>
					</a>
					<div id="invoiceDropdownMenu" class="dropdown-menu w-100" aria-labelledby="dropdownMenuLink">
					</div>
				</div>
			</td>
		</tr>
		<cfif local.myRightsInvoiceAdmin.invoiceClose is 1>
			<tr>
				<td></td>
				<td>
					<div class="form-check">
						<input type="checkbox" name="invoiceAutoClose" id="invoiceAutoClose" value="1" class="form-check-input"> 
						<label for="invoiceAutoClose" class="form-check-label">Close invoice after recording sale.
					</div>
				</td>
			</tr>
		</cfif>
	</tbody>
	<tbody id="addSaleDefSched" style="display:none;">
		<tr><td colspan="3">&nbsp;</td></tr>
		<tr>
			<td class="align-top font-weight-bold pt-2"><b>Deferred Schedule:</td>
			<td class="align-top">
				<table class="table table-sm table-borderless">
				<tr>
					<td class="font-weight-bold">Recognized Date</td>
					<td width="5"></td>
					<td class="font-weight-bold">Amount</td>
				</tr>
				<tbody id="tblDefSchRows">
				</tbody>
				<tr>
					<td colspan="3"><a href="javascript:addMoreDates();" class="btn btn-sm btn-secondary btn-pill">add more dates</a></td>
				</tr>
				</table>
			</td>
		</tr>
	</tbody>
	</table>
</div>
<div id="divSaleFormloadingDIV" class="mt-4 text-center" style="display:none;">
	<div class="spinner-border" role="status"></div>
	<div class="mt-2 font-weight-bold">Please wait while we save the sale.</div>
</div>
</form>
</cfoutput>
<cfsavecontent variable="local.writeOffTransJS">
	<cfoutput>
		<cfif local.qryType.typeID is not 1>
			#local.strGLAcctWidget.js#
		</cfif>
	<script language="javascript">
	function hideAlert() { mca_hideAlert('err_writeoff_trans'); };

	function checkWOForm() {
		hideAlert();
		var arrReq = new Array();
		top.$('##btnMCModalSave').prop('disabled',true);

		$('##amount').val(formatCurrency($('##amount').val()));
		if (parseFloat($('##amount').val()) < 0) arrReq[arrReq.length] = 'You may not enter a negative write-off amount.';
		else if (parseFloat($('##amount').val()) == 0) arrReq[arrReq.length] = 'You may not enter 0 as the write-off amount.';
		<cfif local.qryType.typeID is 1>
			else if (parseFloat($('##amount').val()) > #numberformat(local.qryTransaction.maxWriteOffAmt,"0.00")#) arrReq[arrReq.length] = 'You may not enter a write-off amount greater than the amount due.';
		<cfelse>
			else if (parseFloat($('##amount').val()) > #numberformat(min(local.qryTransaction.unallocatedAmount,local.qryTransaction.refundableAmount),"0.00")#) arrReq[arrReq.length] = 'You may not enter a write-off amount greater than the amount due.';

			if ($('##GLAccountID').length && $('##GLAccountID').val() == 0) arrReq[arrReq.length] = 'Please choose a Revenue Account.';
		</cfif>

		if (arrReq.length) {
			mca_showAlert('err_writeoff_trans', arrReq.join('<br/>'));
			top.$('##btnMCModalSave').prop('disabled',false);
			return false;
		}

		window.setTimeout(() => { top.$('##btnMCModalSave').remove(); }, 1000);
		return true;
	}

	$(function() {
		mca_setupDatePickerField('transactionDate','#dateformat(local.qryTransaction.transactionDate,'m/d/yyyy')#');
		mca_setupCalendarIcons('frmWriteOff');

		top.MCModalUtils.buildFooter({
			classlist: 'd-flex justify-content-between',
			showclose: true,
			showextrabutton: true,
			extrabuttonclass: 'btn-primary',
			extrabuttononclickhandler: '$("##MCModalBodyIframe")[0].contentWindow.$("##frmWriteOff :submit").click',
			extrabuttonlabel: 'Save Write-Off'
		});
	});
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.writeOffTransJS)#">

<cfoutput>
<div id="divSaleForm" class="p-3">
	<form name="frmWriteOff" id="frmWriteOff" action="#local.formlink#" method="post" onsubmit="return checkWOForm();">
	<input type="hidden" name="tid" id="tid" value="#local.qryType.transactionID#">

	<!--- hidden submit triggered from parent --->
	<button type="submit" class="d-none"></button>
	
	<div id="err_writeoff_trans" class="alert alert-danger mb-2 d-none"></div>
		
	<cfif local.qryType.typeID is 1>
		<table class="table table-sm table-borderless table-striped table-hover border">
			<thead class="border">
				<tr>
					<th>Date</th>
					<th>Amt Due</th>
					<th>Sale to write-off</th>
				</tr>
			</thead>
			<tbody>
			<cfloop query="local.qryTransaction">
				<tr>
					<td>#dateformat(local.qryTransaction.transactionDate,'m/d/yyyy')#</td>
					<td class="pl-1">#dollarFormat(local.qryTransaction.SaleAmountDue)#</td>
					<td class="pl-1">#local.qryTransaction.detail#</td>
				</tr>
			</cfloop>
			</tbody>
		</table>
	<cfelse>
		<table class="table table-sm table-borderless table-striped table-hover border">
			<thead class="border">
				<tr>
					<th>Date</th>
					<th>Payment to write-off</th>
				</tr>
			</thead>
			<tbody>
			<cfloop query="local.qryTransaction">
				<tr>
					<td class="align-top">#dateformat(local.qryTransaction.transactionDate,'m/d/yyyy')#</td>
					<td class="pl-1">
						#dollarFormat(local.qryTransaction.amount)# #local.qryTransaction.detail#
						<cfif local.qryTransaction.amount is not min(local.qryTransaction.unallocatedAmount,local.qryTransaction.refundableAmount)>
							<br/> * up to #dollarFormat(min(local.qryTransaction.unallocatedAmount,local.qryTransaction.refundableAmount))#<cfif arguments.event.getValue('mc_siteinfo.showCurrencyType') is 1> #arguments.event.getValue('mc_siteinfo.defaultCurrencyType')#</cfif> can be written off
						</cfif>
					</td>
				</tr>
			</cfloop>
			</tbody>
		</table>
	</cfif>

	<cfif local.qryType.typeID is 1 and local.qryTransaction.maxWriteOffAmt is 0>
		<div class="alert alert-danger">
			<b>This sale is not eligible to be written-off.</b><br/>
			You may only write-off uncollected, recognized revenue.<br/>
			While #dollarFormat(local.qryTransaction.SaleAmountDue)# of this sale is uncollected, all of its recognized portion has been collected.
			To write off the remaining amount of this sale, recognize its revenue first.
		</div>
	<cfelse>
		<table class="mb-3" width="100%">
		<tr>
			<td class="align-top">
				<div class="mb-1">Date of Write-Off:</div>
				<div class="input-group input-group-sm" style="width:160px;">
					<input type="text" name="transactionDate" id="transactionDate" value="#dateformat(now(),'m/d/yyyy')#" class="form-control form-control-sm dateControl" autocomplete="off" onclick="hideAlert();">
					<div class="input-group-append">
						<span class="input-group-text cursor-pointer calendar-button" data-target="transactionDate"><i class="fa-solid fa-calendar"></i></span>
					</div>
				</div>
			</td>
			<cfif local.qryType.typeID is 1 and local.qryTransaction.maxWriteOffAmt is not local.qryTransaction.SaleAmountDue>
				<td class="align-top">
					<div class="mb-1">Maximum Write-Off Amount:</div>
					<div class="pl-2 mt-2"><b>#dollarformat(local.qryTransaction.maxWriteOffAmt)#</b></div>
				</td>
			</cfif>
			<td class="align-top">
				<div class="mb-1">Amount to Write-Off:</div>
				<cfif local.qryType.typeID is 1>
					<cfset local.txtVal = numberformat(local.qryTransaction.maxWriteOffAmt,"0.00")>
				<cfelse>
					<cfset local.txtVal = numberformat(min(local.qryTransaction.unallocatedAmount,local.qryTransaction.refundableAmount),"0.00")>
				</cfif>
				<div class="d-flex align-items-center" style="width:160px;">
					<div class="input-group input-group-sm">
						<div class="input-group-prepend"><span class="input-group-text">$</span></div>
						<input type="text" name="amount" id="amount" value="#local.txtVal#" class="form-control form-control-sm" autocomplete="off" onblur="this.value=formatCurrency(this.value);hideAlert();">
					</div>
					<cfif arguments.event.getValue('mc_siteinfo.showCurrencyType') is 1><span class="ml-1">#arguments.event.getValue('mc_siteinfo.defaultCurrencyType')#</span></cfif>
				</div>
			</td>
		</tr>
		<cfif local.qryType.typeID is not 1>
			<tr><td colspan="2">&nbsp;</td></tr>
			<tr>
				<td class="align-top" colspan="2">
					#local.strGLAcctWidget.html#
				</td>
			</tr>
		</cfif>
		</table>
		
		<cfif local.qryType.typeID is 1>
			<cfif local.qryTransaction.maxWriteOffAmt is not local.qryTransaction.SaleAmountDue>
				<div class="alert alert-warning">
					You may only write-off uncollected, recognized revenue.<br/>
					While #dollarFormat(local.qryTransaction.SaleAmountDue)# of this sale is uncollected, only #dollarformat(local.qryTransaction.maxWriteOffAmt)# has been recognized.<br/>
					To write off the remaining amount of this sale, recognize its revenue first.
				</div>
			</cfif>
			<div>
				Writing off a sale will credit the Accounts Receivable account and debit the Write-Off account.
				By continuing, you are indicating the specified amount is no longer owed to you by this member.
			</div>
		<cfelse>
			<div>
				Writing off a payment (Negative Write Off) will credit the chosen revenue account and debit the Deposits account.
				By continuing, you are indicating the specified amount will no longer be refunded to this member.
			</div>
		</cfif>
	</cfif>
	</form>
</div>
</cfoutput>
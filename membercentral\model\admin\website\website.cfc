<cfcomponent extends="model.admin.website.websiteAdmin">
	
	<cffunction name="getSettings" access="public" output="false" returntype="query" hint="Get Single Item Data">
		<cfargument name="siteCode" type="string" required="yes">

		<cfset var qrySite = "">

		<cfquery name="qrySite" datasource="#application.dsn.memberCentral.dsn#">
			SELECT 
				s.siteID, s.orgID, s.defaultLanguageID, s.defaultTimeZoneID, s.defaultCurrencyTypeID, s.siteResourceID,
				s.allowGuestAccounts, s.forceLoginPage, s.deliveryPolicyURL, s.privacyPolicyURL, s.rrPolicyURL, s.tcURL,
				s.immediateMemberUpdates, s.emailMemberUpdates, s.enforceSiteAgreement, s.siteCode, s.siteName, s.defaultPostalState,
				s.joinURL, s.alternateGuestAccountCreationLink, s.alternateForgotPasswordLink, s.GA4MeasurementID, s.GTMContainerID, 
				s.UserWayAccountCode, s.showCurrencyType, ct.currencyType as defaultCurrencyType,
				s.noRightsContentID, norights.rawContent as noRightsContent,
				s.noRightsNotLoggedInContentID, norightsnotloggedin.rawContent as noRightsNotLoggedInContent,
				s.inactiveUserContentID, inactive.rawContent as inActiveContent,
				s.siteAgreementContentID, siteagreement.rawContent as siteagreementContent,
				s.welcomeMessageContentID, welcomeMessage.rawContent as welcomeMessageContent,
				s.firstTimeLoginContentID, firstTimeLogin.rawContent as firstTimeLoginContent,
				s.showHomepageWarning, s.homePageWarningContentID, homePageWarning.rawContent as homePageWarningContent, 
				s.customHeadContentID, customHead.rawContent as customHeadContent, s.alternateUpdateMemberLink, s.defaultAdminEmails, 
				s.enableMobile, s.enableDeviceDetection, s.enableAdd2Home, o.memberPhotoWidth, o.memberPhotoHeight, 
				s.pageChangeUpdateNotificationEmails, s.showFieldSetDescription, s.loginLimitModeID, s.useRemoteLogin, 
				s.useRemoteLoginForm, s.remoteLoginFormURL, s.updMemOVNameLabel, s.updMemOVNameDesc, s.updMemOVAddrLabel, s.updMemOVAddrDesc, 
				s.updMemOVEmailLabel, s.updMemOVEmailDesc, s.updMemOVProfLicLabel, s.updMemOVProfLicDesc, s.updMemOVWebLabel, s.updMemOVWebDesc, s.defaultOrgIdentityID ,s.loginOrgIdentityID, s.dropboxAppKey, s.datePublicBetaAccessExpires
			FROM dbo.sites as s
			INNER JOIN dbo.organizations as o ON o.orgID = s.orgID
			INNER JOIN dbo.currencyTypes as ct on ct.currencyTypeID = s.defaultCurrencyTypeID
			INNER JOIN dbo.cms_siteResources AS sr ON s.siteResourceID = sr.siteResourceID
			INNER JOIN dbo.cms_siteResourceStatuses AS srs ON sr.siteResourceStatusID = srs.siteResourceStatusID
			CROSS APPLY dbo.fn_getContent(s.customHeadContentID,1) as customHead
			CROSS APPLY dbo.fn_getContent(s.noRightsContentID,1) as norights
			CROSS APPLY dbo.fn_getContent(s.noRightsNotLoggedInContentID,1) as norightsnotloggedin
			CROSS APPLY dbo.fn_getContent(s.inactiveUserContentID,1) as inactive
			CROSS APPLY dbo.fn_getContent(s.siteAgreementContentID,1) as siteagreement
			CROSS APPLY dbo.fn_getContent(s.welcomeMessageContentID,1) as welcomeMessage
			CROSS APPLY dbo.fn_getContent(s.firstTimeLoginContentID,1) as firstTimeLogin
			CROSS APPLY dbo.fn_getContent(s.homePageWarningContentID,1) as homePageWarning
			WHERE srs.siteResourceStatusDesc IN ('Active','Inactive')
			AND s.siteCode = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.siteCode#">
		</cfquery>

		<cfreturn qrySite>
	</cffunction>
	
	<cffunction name="getSiteCodeFromSiteID" access="public" output="no" returntype="string">
		<cfargument name="siteID" type="numeric" required="yes">

		<cfset var local = structNew()>
		<cfset local.siteCode = "">

		<cfloop collection="#application.objSiteInfo.mc_siteinfo#" item="local.thesite">
			<cfif application.objSiteInfo.mc_siteinfo[local.thesite].siteID is arguments.siteID>
				<cfset local.siteCode = local.thesite>
				<cfbreak>
			</cfif>
		</cfloop>

		<cfreturn local.siteCode>
	</cffunction>
	
	<cffunction name="getLanguages" access="public" output="false" returntype="query" hint="Get Languages">
		<cfset var qryLanguages = "">

		<cfquery name="qryLanguages" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT languageID, languageName
			FROM dbo.cms_languages
			order by languageName;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryLanguages>
	</cffunction>
	
	<cffunction name="getTimeZones" access="public" output="false" returntype="query" hint="Get TimeZones">
		<cfset var qryTimeZones = "">

		<cfquery name="qryTimeZones" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT TimeZoneID, timeZone
			FROM dbo.timeZones
			ORDER BY TimeZoneID;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryTimeZones>
	</cffunction>

	<cffunction name="getOrganizations" access="public" output="false" returntype="query">
		<cfset var qryOrgs = structNew()>

		<cfquery name="qryOrgs" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT o.orgID, o.orgcode, oi.organizationName as orgname, o.orgcode + ' - ' + oi.organizationName as orgcombined
			FROM dbo.organizations o
			INNER JOIN dbo.orgIdentities as oi on oi.orgID = o.orgID and oi.orgIdentityID = o.defaultOrgIdentityID
			ORDER BY orgcombined;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryOrgs>
	</cffunction>

	<cffunction name="getNetworks" access="public" output="false" returntype="query">
		<cfset var qryNetworks = structNew()>

		<cfquery name="qryNetworks" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT networkID,networkName
			FROM dbo.networks
			WHERE networkID <> 1
			ORDER BY networkName;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryNetworks>
	</cffunction>

	<cffunction name="getLoginLimitModes" access="public" output="false" returntype="query" hint="Get Languages">
		<cfset var qryLoginLimitModes = "">

		<cfquery name="qryLoginLimitModes" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT loginLimitModeID, loginLimitModeName
			FROM dbo.siteLoginLimitModes
			order by loginLimitModeID;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryLoginLimitModes>
	</cffunction>

	<cffunction name="getCurrencyTypes" access="public" output="false" returntype="query" hint="Get Currency Types">
		<cfset var qryCurrencyTypes = "">
		
		<cfquery name="qryCurrencyTypes" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT currencyTypeID, currencyType
			FROM dbo.currencyTypes
			ORDER BY currencyType;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryCurrencyTypes>
	</cffunction>
	
	<cffunction name="insertSiteLog" access="private" output="false" returntype="void">
		<cfargument name="uid" required="yes" type="uuid">
		<cfargument name="stepMsg" type="string" required="yes">

		<cfset var qryLog = "">
		
		<cfquery name="qryLog" datasource="#application.dsn.datatransfer.dsn#">
			insert into dbo.admin_siteCreationLog(uid, stepMsg)
			values (
				<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.uid#">,
				<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.stepMsg#">
			)
		</cfquery>
	</cffunction>

	<cffunction name="insertSite" access="public" output="false" returntype="void">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.objRewrites = CreateObject("component","model.system.platform.rewrites")>
		<cfset local.uid = arguments.event.getValue('uid')>
		
		<cfset insertSiteLog(uid=local.uid, stepMsg='Starting site creation process')>
		
		<!--- Force orgcode and sitecode to be uppercase --->
		<cfset arguments.event.setValue('frm_siteCode',ucase(arguments.event.getTrimValue('frm_siteCode')))>
		<cfif arguments.event.valueExists('frm_orgCode')>
			<cfset arguments.event.setValue('frm_orgCode',ucase(arguments.event.getTrimValue('frm_orgCode')))>
		</cfif>
		<cfset local.siteCode = arguments.event.getTrimValue('frm_siteCode')>
		
		<!--- do we need to create an org? --->
		<cfif arguments.event.getValue('frm_orgChoice') eq "new">
			<cfset insertSiteLog(uid=local.uid, stepMsg='Attempting to create new organization with orgcode #arguments.event.getValue("frm_orgCode")#')>
			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="createOrganization">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('frm_orgCode')#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('frm_orgName')#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('frm_orgShortName')#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('frm_identityAddress')#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('frm_identityAddress2')#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('frm_identityCity')#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('frm_identityStateID')#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('frm_identityPostalCode')#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('frm_identityPhone')#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('frm_identityFax')#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('frm_identityEmail')#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('frm_identityWebsite')#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('frm_XUserName')#">
				<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="local.orgID">	
			</cfstoredproc>
		<cfelse>
			<cfset local.orgID = arguments.event.getValue('frm_orgID')>
		</cfif>
		<cfset insertSiteLog(uid=local.uid, stepMsg='Retrieving orgcode for this site')>
		<cfquery name="local.qryOrg" datasource="#application.dsn.membercentral.dsn#">
			select orgcode
			from dbo.organizations
			where orgID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.orgID#">
		</cfquery>
		
		<!--- do we need to create a network? --->
		<cfif arguments.event.getValue('frm_netChoice') eq "new">
			<cfset insertSiteLog(uid=local.uid, stepMsg='Attempting to create new network with name #arguments.event.getValue("frm_networkName")#')>
			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="createNetwork">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('frm_networkName')#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" null="yes">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" null="yes">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('frm_supportProviderName')#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('frm_supportProviderPhone')#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('frm_supportProviderEmail')#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('frm_emailFrom')#">
				<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.event.getValue('frm_addToApprovals',0)#">
				<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="local.networkID">	
			</cfstoredproc>
			<cfset local.isMasterSite = 1>
		<cfelse>
			<cfset local.networkID = arguments.event.getValue('frm_networkID')>
			<cfset local.isMasterSite = 0>
		</cfif>

		<cfquery name="local.qryOrgIdentity" datasource="#application.dsn.membercentral.dsn#">
			SELECT TOP 1 orgIdentityID
			FROM dbo.orgIdentities
			WHERE orgID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.orgID#">
		</cfquery>

		<cfquery name="local.qryDefaultCurrencyType" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT currencyTypeID
			FROM dbo.currencyTypes
			WHERE currencyType = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('frm_defaultCurrencyType','USD')#">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset insertSiteLog(uid=local.uid, stepMsg='Attempting to create new site with sitecode #arguments.event.getValue("frm_siteCode")#')>
		<cfset local.QualifyRFID = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="LoginPolicy", functionName="Qualify")>
		<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="createSite">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.orgID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#application.MCEnvironment#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.siteCode#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('frm_siteName')#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.networkID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="1">
			<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#local.isMasterSite#">
			<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.event.getValue('frm_hasDepoTLA')#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="1">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('frm_defaultTimeZoneID')#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.qryDefaultCurrencyType.currencyTypeID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="0">
			<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="1">
			<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="0">
			<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="0">
			<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="0">
			<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="0">
			<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="1">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('frm_defaultPostalStateID')#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="">
			<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="0">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="">
			<cfprocparam type="In" cfsqltype="CF_SQL_LONGVARCHAR" value="You do not have sufficient rights to view this content.">
			<cfprocparam type="In" cfsqltype="CF_SQL_LONGVARCHAR" value="You do not have sufficient rights to view this content. Please log in.">
			<cfprocparam type="In" cfsqltype="CF_SQL_LONGVARCHAR" value="Your account is currently inactive. Please call the association to gain access to this site.">
			<cfprocparam type="In" cfsqltype="CF_SQL_LONGVARCHAR" value="">
			<cfprocparam type="In" cfsqltype="CF_SQL_LONGVARCHAR" value="">
			<cfprocparam type="In" cfsqltype="CF_SQL_LONGVARCHAR" value="">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.qryOrgIdentity.orgIdentityID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.QualifyRFID#">
			<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="local.siteID">	
		</cfstoredproc>

		<cfset insertSiteLog(uid=local.uid, stepMsg='Creating admin suite for the new site')>
		<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="createAdminSuite">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.siteID#">
		</cfstoredproc>
		
		<!--- create rewrites files --->
		<cfset insertSiteLog(uid=local.uid, stepMsg='Updating NGINX configs for this site')>
		<cfset local.objRewrites.refreshAllConfFiles(siteid=local.siteid)>
		<cfset local.objRewrites.updateOldOrgCodes()>

		<!--- create the site documents folder --->
		<cfset insertSiteLog(uid=local.uid, stepMsg='Creating siteDocuments directory for site')>
		<cfdirectory action="create" directory="#application.paths.RAIDSiteDocuments.path##local.qryOrg.orgcode#/#local.siteCode#">
		
		<cfset insertSiteLog(uid=local.uid, stepMsg='Reloading SiteInfo object for application')>
		<cfset application.objSiteInfo.triggerClusterWideReload()>

		<cfset insertSiteLog(uid=local.uid, stepMsg='Creating App Directories')>
		<cfset application.objSiteInfo.markSiteDirectoriesNotChecked(sitecode=local.siteCode)>
		<cfset application.objSiteInfo.checkSiteDirectories(sitecode=local.siteCode)>
		
		<!--- Cloudflare tunnels --->
		<cftry>
			<cfset local.cloudflareURL = "https://api.cloudflare.com/client/v4/zones/b8669b282943d3b45c778cbe5d201be6/dns_records">
			<cfset local.cloudflareEmail = "<EMAIL>">
			<cfset local.cloudflareKey = "21ac8c03b27d3513ea2dd7f1aba2fd6a87d04">

			<cfif listFindNoCase("localDevelopment,test,newtest,development,newdevelopment",application.MCEnvironment)>
				<cfhttp url="#local.cloudflareURL#" method="post" useragent="MemberCentral.com" throwonerror="true" result="local.cfapiresult1">
					<cfhttpparam type="header" name="X-Auth-Email" value="#local.cloudflareEmail#">
					<cfhttpparam type="header" name="X-Auth-Key" value="#local.cloudflareKey#">
					<cfhttpparam type="header" name="Content-Type" value="application/json">
					<cfhttpparam type="body" value="{ ""type"":""CNAME"", ""name"":""#LCASE(local.siteCode)#.dev.mcinternal.com"", ""content"":""dev-tunnel.mcinternal.com"", ""ttl"":1, ""proxied"":true }">
				</cfhttp>
				<cfset local.strCFAPIResult1 = deserializeJSON(local.cfapiresult1.filecontent)>
				<cfif local.strCFAPIResult1.success neq "true">
					<cfthrow message="Unable to create Cloudflare tunnel for DEV.">
				</cfif>

				<cfhttp url="#local.cloudflareURL#" method="post" useragent="MemberCentral.com" throwonerror="true" result="local.cfapiresult2">
					<cfhttpparam type="header" name="X-Auth-Email" value="#local.cloudflareEmail#">
					<cfhttpparam type="header" name="X-Auth-Key" value="#local.cloudflareKey#">
					<cfhttpparam type="header" name="Content-Type" value="application/json">
					<cfhttpparam type="body" value="{ ""type"":""CNAME"", ""name"":""#LCASE(local.siteCode)#.test.mcinternal.com"", ""content"":""test-tunnel.mcinternal.com"", ""ttl"":1, ""proxied"":true }">
				</cfhttp>
				<cfset local.strCFAPIResult2 = deserializeJSON(local.cfapiresult2.filecontent)>
				<cfif local.strCFAPIResult2.success neq "true">
					<cfthrow message="Unable to create Cloudflare tunnel for TEST.">
				</cfif>
			<cfelseif listFindNoCase("beta,newbeta",application.MCEnvironment)>
				<cfhttp url="#local.cloudflareURL#" method="post" useragent="MemberCentral.com" throwonerror="true" result="local.cfapiresult3">
					<cfhttpparam type="header" name="X-Auth-Email" value="#local.cloudflareEmail#">
					<cfhttpparam type="header" name="X-Auth-Key" value="#local.cloudflareKey#">
					<cfhttpparam type="header" name="Content-Type" value="application/json">
					<cfhttpparam type="body" value="{ ""type"":""CNAME"", ""name"":""#LCASE(local.siteCode)#.beta.mcinternal.com"", ""content"":""beta-tunnel.mcinternal.com"", ""ttl"":1, ""proxied"":true }">
				</cfhttp>
				<cfset local.strCFAPIResult3 = deserializeJSON(local.cfapiresult3.filecontent)>
				<cfif local.strCFAPIResult3.success neq "true">
					<cfthrow message="Unable to create Cloudflare tunnel for BETA.">
				</cfif>
			</cfif>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
		</cfcatch>
		</cftry>

		<cfset insertSiteLog(uid=local.uid, stepMsg='Site creation completed')>
	</cffunction>

	<cffunction name="updateSite" access="public" output="false" returntype="void">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser)>
			<cfscript>
				if (len(arguments.event.getTrimValue('frm_defaultAdminEmails',''))) {
					local.arrDefaultAdminEmails = listToArray(replace(replace(arguments.event.getTrimValue('frm_defaultAdminEmails'),',',';','ALL'),' ','','ALL'),';');
					for (var i=1; i lte arrayLen(local.arrDefaultAdminEmails); i++) {
						if (len(local.arrDefaultAdminEmails[i]) and not isValid("regex",local.arrDefaultAdminEmails[i],application.regEx.email)) {
							arrayDeleteAt(local.arrDefaultAdminEmails,i);
						}
					}
					arguments.event.setValue('frm_defaultAdminEmails',arrayToList(arrDefaultAdminEmails,';'));
				}
			</cfscript>
		</cfif>
		
		<cfquery datasource="#application.dsn.membercentral.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				DECLARE @hasRegistration int;
				
				BEGIN TRAN;
					<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser)>
						UPDATE dbo.sites
						SET 
							enableMobile = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('frm_enableMobile',0)#">,
							enableDeviceDetection = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('frm_enableDeviceDetection',0)#">,
							enableAdd2Home = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('frm_enableAdd2Home',0)#">,
							siteName = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('frm_siteName')#">,
							defaultLanguageID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('frm_defaultLanguageID')#">,
							defaultTimeZoneID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('frm_defaultTimeZoneID')#">,
							defaultPostalState = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('frm_defaultPostalState')#">,
							deliveryPolicyURL = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('frm_deliveryPolicyURL')#">, 
							privacyPolicyURL = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('frm_privacyPolicyURL')#">, 
							rrPolicyURL = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('frm_rrPolicyURL')#">, 
							tcURL = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('frm_tcURL')#">,
							joinURL = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('frm_joinURL')#">,
							defaultAdminEmails = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('frm_defaultAdminEmails')#">,
							GA4MeasurementID = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('frm_GA4MeasurementID')#">,
							GTMContainerID = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('frm_GTMContainerID')#">,
							UserWayAccountCode = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('frm_UserWayAccountCode')#">,
							dropboxAppKey = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getTrimValue('frm_dropboxAppKey')#">,
							showCurrencyType = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.event.getValue('frm_showCurrencyType')#">,
							pageChangeUpdateNotificationEmails = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('frm_pageChangeUpdateNotificationEmails')#">,
							showHomePageWarning = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.event.getValue('frm_showHomePageWarning')#">,
							defaultOrgIdentityID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('frm_defaultOrgIdentityID')#">
						WHERE siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mc_siteinfo.siteid')#">;

						UPDATE trialsmith.dbo.depoTLA
						SET websitename = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('frm_siteName')#">
						WHERE [State] = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('mc_siteinfo.siteCode')#">;
					<cfelse>
						UPDATE dbo.sites
						SET pageChangeUpdateNotificationEmails = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('frm_pageChangeUpdateNotificationEmails')#">,
							showHomePageWarning = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.event.getValue('frm_showHomePageWarning')#">
						WHERE siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mc_siteinfo.siteid')#">
					</cfif>
					
					EXEC dbo.cms_updateContent 
					@contentID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('frm_HomePageWarningContentID')#" null="no">,
					@languageID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="1" null="no">,
					@isHTML=<cfqueryparam cfsqltype="CF_SQL_BIT" value="1" null="no">,
					@contentTitle=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="" null="no">,
					@contentDesc=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="" null="no">,
					@rawcontent=<cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.event.getValue('frm_HomePageWarningContent')#" null="no">,
					@memberID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=arguments.event.getValue('mc_siteInfo.orgID'))#" null="no">;

					EXEC dbo.cms_updateContent 
					@contentID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('frm_inactiveUserContentID')#" null="no">,
					@languageID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="1" null="no">,
					@isHTML=<cfqueryparam cfsqltype="CF_SQL_BIT" value="1" null="no">,
					@contentTitle=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="" null="no">,
					@contentDesc=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="" null="no">,
					@rawcontent=<cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.event.getValue('frm_inactiveUserContent')#" null="no">,
					@memberID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=arguments.event.getValue('mc_siteInfo.orgID'))#" null="no">;

					<cfif arguments.event.valueExists('frm_CustomHeadContent')>
						EXEC dbo.cms_updateContent 
						@contentID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('frm_CustomHeadContentID')#" null="no">,
						@languageID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="1" null="no">,
						@isHTML=<cfqueryparam cfsqltype="CF_SQL_BIT" value="1" null="no">,
						@contentTitle=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="" null="no">,
						@contentDesc=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="" null="no">,
						@rawcontent=<cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.event.getValue('frm_CustomHeadContent')#" null="no">,
						@memberID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=arguments.event.getValue('mc_siteInfo.orgID'))#" null="no">;
					</cfif>
					
					<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser)>						
						EXEC dbo.cms_saveFeaturedImageConfigSettings 
						@referenceID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteid')#">,
						@referenceType=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="websitePageSection">,
						@featureImageConfigID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('pageSectionFtdImgConfigID',0)#">,
						@enteredByMemberID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberData.memberID#">,
						@featuredimagesizesXML=<cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="<featuredimagesizes></featuredimagesizes>">;
						
						EXEC dbo.cms_saveFeaturedImageConfigSettings 
						@referenceID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteid')#">,
						@referenceType=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="websitePage">,
						@featureImageConfigID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('pageFtdImgConfigID',0)#">,
						@enteredByMemberID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberData.memberID#">,
						@featuredimagesizesXML=<cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="<featuredimagesizes></featuredimagesizes>">;
					</cfif>
				COMMIT TRAN;
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>
	</cffunction>

	<cffunction name="updateSiteUserLoginSettings" access="public" output="false" returntype="void">
		<cfargument name="Event" type="any">

		<cfset var qryUpdateSiteUserLoginSettings = "">

		<!--- fully qualify all links in the welcome message content --->
		<cfset var welcomeContent = application.objResourceRenderer.qualifyAllLinks(content=arguments.event.getValue('frm_welcomeMessageContent'), siteid=arguments.event.getValue('mc_siteinfo.siteid'))>
				
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryUpdateSiteUserLoginSettings">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				
				DECLARE @siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteid')#">,
					@recordedByMemberID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=arguments.event.getValue('mc_siteInfo.orgID'))#">,
					@noRightsContentID int, @noRightsNotLoggedInContentID int, @siteAgreementContentID int, @welcomeMessageContentID int, 
					@firstTimeLoginContentID int;

				SELECT @noRightsContentID = noRightsContentID, @noRightsNotLoggedInContentID = noRightsNotLoggedInContentID, 
					@siteAgreementContentID = siteAgreementContentID, @welcomeMessageContentID = welcomeMessageContentID, 
					@firstTimeLoginContentID = firstTimeLoginContentID
				FROM dbo.sites
				WHERE siteID = @siteID;
				
				BEGIN TRAN;
					<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser)>
						UPDATE dbo.sites
						SET forceLoginPage = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('frm_forceLoginPage')#">,
							enforceSiteAgreement = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.event.getValue('frm_enforceSiteAgreement')#">,
							loginOrgIdentityID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('frm_loginOrgIdentityID')#">,
							useRemoteLogin = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.event.getValue('frm_useRemoteLogin')#">,
							<cfif arguments.event.getValue('frm_useRemoteLogin') is 1>
								useRemoteLoginForm = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.event.getValue('frm_useRemoteLoginForm')#">,
								<cfif arguments.event.getValue('frm_useRemoteLoginForm') is 1>
									remoteLoginFormURL = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('frm_remoteLoginFormURL')#">,
								<cfelse>
									remoteLoginFormURL = NULL,
								</cfif>
							<cfelse>
								useRemoteLoginForm = 0,
								remoteLoginFormURL = NULL,
							</cfif>
							alternateForgotPasswordLink = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('frm_alternateForgotPasswordLink')#">,
							loginLimitModeID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('frm_loginLimitModeID')#">,
							allowGuestAccounts = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('frm_allowGuestAccounts',0)#">,
							alternateGuestAccountCreationLink = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('frm_alternateGuestAccountCreationLink')#">
						WHERE siteID = @siteID;
					</cfif>

					EXEC dbo.cms_updateContent @contentID=@noRightsContentID, @languageID=1, @isHTML=1, @contentTitle='', @contentDesc='',
						@rawcontent=<cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.event.getValue('frm_noRightsContent')#">,
						@memberID=@recordedByMemberID;

					EXEC dbo.cms_updateContent @contentID=@noRightsNotLoggedInContentID, @languageID=1, @isHTML=1, @contentTitle='', @contentDesc='',
						@rawcontent=<cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.event.getValue('frm_noRightsNotLoggedInContent')#">,
						@memberID=@recordedByMemberID;

					EXEC dbo.cms_updateContent @contentID=@siteAgreementContentID, @languageID=1, @isHTML=1, @contentTitle='', @contentDesc='',
						@rawcontent=<cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.event.getValue('frm_siteAgreementContent')#">,
						@memberID=@recordedByMemberID;

					EXEC dbo.cms_updateContent @contentID=@firstTimeLoginContentID, @languageID=1, @isHTML=1, @contentTitle='', @contentDesc='',
						@rawcontent=<cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.event.getValue('frm_firstTimeLoginContent')#">,
						@memberID=@recordedByMemberID;

					EXEC dbo.cms_updateContent @contentID=@welcomeMessageContentID, @languageID=1, @isHTML=1, @contentTitle='', @contentDesc='',
						@rawcontent=<cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#welcomeContent#">,
						@memberID=@recordedByMemberID;
				COMMIT TRAN;
			
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>
	</cffunction>
	
	<cffunction name="doesOrgCodeExist" access="public" output="false" returntype="boolean">
		<cfargument name="orgcode" type="String" required="true">

		<cfset var qryCheck = "">

		<cfquery name="qryCheck" datasource="#application.dsn.memberCentral.dsn#">
			SELECT dbo.fn_isValidUsageCode(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.orgcode#">,'org') AS isValidOrgCode
		</cfquery>

		<cfreturn NOT qryCheck.isValidOrgCode>
	</cffunction>
	
	<cffunction name="doesSiteCodeExist" access="public" output="false" returntype="boolean">
		<cfargument name="sitecode" type="String" required="true">

		<cfset var qryCheck = "">

		<cfquery name="qryCheck" datasource="#application.dsn.memberCentral.dsn#">
			SELECT dbo.fn_isValidUsageCode(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.sitecode#">,'site') AS isValidSiteCode
		</cfquery>

		<cfreturn NOT qryCheck.isValidSiteCode>
	</cffunction>
	
	<cffunction name="doesMainHostNameExist" access="public" output="false" returntype="boolean">
		<cfargument name="nameToCheck" type="String" required="true">
		<cfargument name="hostNameID" type="numeric" required="false" default="0">

		<cfset var qryCheck = "">
		
		<cfquery name="qryCheck" datasource="#application.dsn.memberCentral.dsn#">
			SELECT hostName
			FROM dbo.siteHostNames
			WHERE hostName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.nameToCheck#">
			<cfif arguments.hostNameID gt 0>
				and hostNameID <> <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.hostNameID#">
			</cfif>
		</cfquery>

		<cfif qryCheck.recordCount>
			<cfreturn true>
		<cfelse>
			<cfreturn false>
		</cfif>
	</cffunction>

	<cffunction name="isReservedHostName" access="public" output="false" returntype="boolean">
		<cfargument name="nameToCheck" type="String" required="true">

		<cfset var local = structNew()>
		<cfset local.numparts = listlen(arguments.nameToCheck,".")>
		<cfif listfind("4,5",local.numparts) and findNoCase(".membercentral.com",arguments.nameToCheck)>
			<cfset local.return = true>
		<cfelse>
			<cfset local.return = false>
		</cfif>
		<cfreturn local.return>
	</cffunction>

	<cffunction name="getDomainDetails" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="hostNameID" type="numeric" required="yes">
		
		<cfset var qryDomain = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryDomain">
			SELECT hostNameID, hostName, environmentID, useRedirect, internalIPAddress, hasSSL, sslCertFilename, sslPrivateKeyFilename,
				cloudflare_customHostnameID
			FROM dbo.siteHostNames
			WHERE siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
			AND hostNameID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.hostNameID#">
		</cfquery>

		<cfreturn qryDomain>
	</cffunction>

	<cffunction name="getDomains" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="yes">
		
		<cfset var qryDomains = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryDomains">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			declare @environmentID int, @environmentName varchar(50) = <cfqueryparam value="#application.MCEnvironment#" cfsqltype="CF_SQL_VARCHAR">;

			select @environmentID = environmentID from dbo.platform_environments where environmentName = @environmentName;

			SELECT hostNameID, hostName 
			FROM dbo.siteHostNames
			WHERE siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteID#">
			and environmentID = @environmentID
			order by hostName;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryDomains>
	</cffunction>
	
	<cffunction name="getMainHost" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="yes">

		<cfset var local = structNew()>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryMainHost">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			declare @environmentID int, @environmentName varchar(50) = <cfqueryparam value="#application.MCEnvironment#" cfsqltype="CF_SQL_VARCHAR">;

			select @environmentID = environmentID from dbo.platform_environments where environmentName = @environmentName;

			SELECT sh.hostname as mainHostname, sh.hostNameID as mainHostID
			FROM dbo.siteEnvironments as se
			inner join dbo.siteHostnames as sh on sh.hostnameID = se.mainHostnameID
				and sh.siteID = se.siteID
				and se.environmentID = @environmentID
				and sh.siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteID#">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn local.qryMainHost>
	</cffunction>
	
	<cffunction name="getEnvironment" access="public" output="false" returntype="string">
		<cfargument name="environmentID" type="numeric" required="yes">

		<cfset var qryGetEnvironment = structNew()>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryGetEnvironment">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			declare @environmentID int = <cfqueryparam value="#arguments.environmentID#" cfsqltype="CF_SQL_INTEGER">;

			select environmentName from dbo.platform_environments where environmentID = @environmentID;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryGetEnvironment.environmentName>
	</cffunction>

	<cffunction name="insertDomain" access="package" output="false" returntype="numeric">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="hostName" type="string" required="true">
		<cfargument name="useRedirect" type="string" required="true">
		<cfargument name="internalIPAddress" type="string" required="true">
		<cfargument name="hasSSL" type="boolean" required="true">
		<cfargument name="sslCertFileName" type="string" required="true">
		<cfargument name="sslPrivateKeyFileName" type="string" required="true">

		<cfset var hostNameID = "">

		<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="createSiteHostName">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#application.MCEnvironment#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.hostName#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.useRedirect#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.internalIPAddress#">
			<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.hasSSL#">
			<cfif arguments.hasSSL>
				<cfif len(arguments.sslCertFileName)>
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.sslCertFileName#">
				<cfelse>
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" null="yes">
				</cfif>
				<cfif len(arguments.sslPrivateKeyFileName)>
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.sslPrivateKeyFileName#">
				<cfelse>
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" null="yes">
				</cfif>
			<cfelse>
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" null="yes">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" null="yes">
			</cfif>
			<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="hostNameID">
		</cfstoredproc>

		<cfset local.objRewrites = CreateObject("component","model.system.platform.rewrites")>
		<cfset local.objRewrites.refreshAllConfFiles(siteid=arguments.siteid)>

		<cfreturn hostNameID>
	</cffunction>

	<cffunction name="updateDomain" access="package" output="false" returntype="void">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="hostNameID" type="numeric" required="true">
		<cfargument name="hostName" type="string" required="true">
		<cfargument name="useRedirect" type="string" required="true">
		<cfargument name="internalIPAddress" type="string" required="true">
		<cfargument name="hasSSL" type="boolean" required="true">
		<cfargument name="sslCertFileName" type="string" required="true">
		<cfargument name="sslPrivateKeyFileName" type="string" required="true">

		<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="updateSiteHostName">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.hostNameID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.hostName#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.useRedirect#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.internalIPAddress#">
			<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.hasSSL#">
			<cfif arguments.hasSSL>
				<cfif len(arguments.sslCertFileName)>
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.sslCertFileName#">
				<cfelse>
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" null="yes">
				</cfif>
				<cfif len(arguments.sslPrivateKeyFileName)>
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.sslPrivateKeyFileName#">
				<cfelse>
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" null="yes">
				</cfif>
			<cfelse>
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" null="yes">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" null="yes">
			</cfif>
		</cfstoredproc>
	</cffunction>
	
	<cffunction name="deleteDomain" access="public" output="false" returntype="void">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="hostNameID" type="numeric" required="true">

		<cfset var qryDelete = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryDelete">
			DELETE FROM dbo.siteHostNames
			WHERE hostNameID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.hostNameID#">
			AND siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteID#">
		</cfquery>
		
		<cfset local.objRewrites = CreateObject("component","model.system.platform.rewrites")>
		<cfset local.objRewrites.refreshAllConfFiles(siteid=arguments.siteid)>

	</cffunction>

	<cffunction name="updatePublicBetaAccessExpires" access="public" output="false" returntype="void">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="action" type="string" required="true">
	
		<cfset var qryUpdatePublicAccess = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryUpdatePublicAccess">
			update dbo.sites 
			set datePublicBetaAccessExpires = 
				<cfif arguments.action EQ 'enable'>
					DATEADD(day, 90, GETDATE())
				<cfelseif arguments.action EQ 'extend'>
					DATEADD(week, 2, datePublicBetaAccessExpires)
				<cfelse>
					NULL
				</cfif>
			where siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteID#">
		</cfquery>	
	</cffunction>
	
	<cffunction name="updateNewMainHost" access="public" output="false" returntype="void">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="newMainHostID" type="numeric">
		<cfargument name="oldMainHostID" type="numeric">

		<cfset var local = structNew()>
		<cfset local.objRewrites = CreateObject("component","model.system.platform.rewrites")>
		
		<!--- update hostname --->
		<!--- also need to update all the aliases so they have the new host name --->
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryUpdate">
			set nocount on;

			declare @siteId int = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteID#">, 
				@newHostnameID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.newMainHostID#">,
				@environmentName varchar(50) = <cfqueryparam value="#application.MCEnvironment#" cfsqltype="CF_SQL_VARCHAR">,
				@environmentID int, @mainhostname varchar(80), @minhostname varchar(80);

			select @environmentID = environmentID from dbo.platform_environments where environmentName = @environmentName;

			UPDATE se 
			set se.mainHostNameID = sh.hostnameID
			from dbo.siteHostNames sh
			inner join dbo.siteEnvironments se on se.siteID=sh.siteID
				and se.environmentID = @environmentID
			where sh.hostNameID = @newHostnameID
			AND sh.siteID = @siteId;
			
			select top 1 @mainhostname = hostname from dbo.SiteHostnames where siteID = @siteid and hostNameID = @newHostnameID;
			
			select @minhostname = min(hostname) from dbo.SiteHostnames where siteID = @siteid and hostNameID <> @newHostnameID;
			WHILE @minhostname is not null BEGIN
				update dbo.siteRedirects
				set redirectURL = replace(redirectURL,'//' + @minhostname + '/','//' + @mainhostname + '/')
				where siteID = @siteid;
			
				select @minhostname = min(hostname) from dbo.SiteHostnames where siteID = @siteid and hostNameID <> @newHostnameID and hostname > @minhostname;
			END
		</cfquery>

		<!--- if a semweb participant, update the catalog URL --->
		<cfquery name="local.getSiteCode" datasource="#application.dsn.membercentral.dsn#">
			SELECT siteCode
			from dbo.sites 
			where siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteID#">
		</cfquery>
		<cfquery name="local.getSWParticipantID" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			SELECT dbo.fn_getParticipantIDFromOrgcode(<cfqueryparam value="#local.getSiteCode.siteCode#" cfsqltype="CF_SQL_VARCHAR">) as participantID
		</cfquery>
		<cfif local.getSWParticipantID.participantID gt 0>
			<cfstoredproc datasource="#application.dsn.tlasites_seminarweb.dsn#" procedure="sw_CorrectCatalogURL">
				<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#local.getSWParticipantID.participantID#">
			</cfstoredproc>
		</cfif>

		<!--- update rewrite files --->
		<cfset local.objRewrites.refreshAllConfFiles(siteid=arguments.siteid)>

	</cffunction>
	
	<cffunction name="getFeatures" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="true">
		
		<cfset var qryGetFeatures = "">

		<cfquery name="qryGetFeatures" datasource="#application.dsn.memberCentral.dsn#">
			set nocount on;

			declare @sitecode varchar (10), @siteID int;
			set @siteID = <cfqueryparam value="#arguments.siteID#" cfsqltype="cf_sql_integer">;
			select @sitecode = sitecode from sites where siteID = @siteID;
			
			select distinct toolType
			from (
				select tt.toolType
				from admin_siteToolRestrictions tr
				inner join admin_toolTypes tt on tt.tooltypeID = tr.tooltypeID
				where siteID = @siteID
				and tt.toolCFC not like '%Reports.%'
					union
				select 'Reports'
				from admin_siteToolRestrictions tr
				inner join admin_toolTypes tt on tt.tooltypeID = tr.tooltypeID
				where tt.toolCFC like 'Reports.custom.' + @sitecode + '.%'
				or (
					tt.toolCFC like 'Reports.%' 
					and left(tt.toolCFC,15) <> 'Reports.custom.' 
					and tt.toolCFC not in ('Reports.Accounting.Transactions','Reports.Accounting.TransactionsVoid')
				)
				and siteID = @siteID
					union
				select 'MemberDocs' from dbo.siteFeatures where siteID = @siteID and memberDocuments = 1
					union
				select 'MemberHistoryAdmin' from dbo.siteFeatures where siteID = @siteID and memberHistory = 1
					union
				select 'APIAccess' from dbo.siteFeatures where siteID = @siteID and mcAPI = 1
					union
				select 'BadgeDeviceAdmin' from dbo.siteFeatures where siteID = @siteID and badgePrinters = 1
					union
				select 'ContributionAdmin' from dbo.siteFeatures where siteID = @siteID and contributions = 1
					union
				select 'EnableSitePasswords' from dbo.siteFeatures where siteID = @siteID and sitePasswords = 1
					union
				select 'referralsSMS' from dbo.siteFeatures where siteID = @siteID and referralsSMS = 1
					union
				select 'RecurringEvents' from dbo.siteFeatures where siteID = @siteID and recurringEvents = 1
			) as tmp;
		</cfquery>

		<cfreturn qryGetFeatures>
	</cffunction>
	
	<cffunction name="insertFeature" access="public" output="false" returntype="void">
		<cfargument name="siteID" type="numeric" required="true" />
		<cfargument name="toolTypeList" type="string" required="true" />

		<cfset var qryInsert = "" />

		<cfquery name="qryInsert" datasource="#application.dsn.membercentral.dsn#">
			set nocount on

			declare	@siteID int, @toolTypeList varchar(1000)
				 
			select @siteID = <cfqueryparam value="#arguments.siteID#" cfsqltype="cf_sql_integer" />
			select @toolTypeList = <cfqueryparam value="#arguments.toolTypeList#" cfsqltype="cf_sql_varchar" />
				 
			exec dbo.enableSiteFeature @siteID=@siteID, @toolTypeList=@toolTypeList
						
			set nocount off
		</cfquery>
	</cffunction>

	<cffunction name="generateDeployCommands" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>

		<cfset local.jsonStr = structNew()>
		<cfset local.jsonStr.branchname = 'master'>
		<cfset local.jsonStr.repo = 'mc'>
		<cfset local.jsonStr.shatag = arguments.event.getTrimValue('frm_shaTag','')>
		<cfif arguments.event.getTrimValue('frm_cmd_resetAppVars',0) is 1>
			<cfset local.jsonStr.resetAppVars = true>
		<cfelse>			
			<cfset local.jsonStr.resetAppVars = false>
		</cfif>
		<cfset local.jsonStr.mvcReinit = false>
		<cfset local.jsonStr.autoRefresh = false>
		
		<cftry>
			<cfset local.newText = serializeJSON(local.jsonStr)>
			<cffile action="write" file="#application.paths.gitDeploy.path#deploy-#local.jsonStr.repo#.txt" output="#local.newText#">
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
		</cfcatch>
		</cftry>
		
		<cfreturn local.jsonStr>
	</cffunction>	
	
	<cffunction name="associateOrganizationLists" access="public" output="false" returntype="void">
		<cfargument name="siteCode" type="string" required="true">
		
		<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="list_associateAllOrgLists">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.siteCode#">
		</cfstoredproc>
	</cffunction>

	<cffunction name="getSiteRedirects" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="string" required="true">
		
		<cfset var qrySiteRedirects = "">

		<cfquery name="qrySiteRedirects" datasource="#application.dsn.membercentral.dsn#">
			select redirectID, redirectName
			from dbo.siteRedirects
			where siteID = <cfqueryparam value="#arguments.siteID#" cfsqltype="cf_sql_integer">
			order by redirectName
		</cfquery>

		<cfreturn qrySiteRedirects>
	</cffunction>

	<cffunction name="getUniversalRoles" access="public" output="false" returntype="query">
		<cfset var qryUniversalRoles = "">

		<cfquery name="qryUniversalRoles" datasource="#application.dsn.membercentral.dsn#">
			select srroles.roleID, srroles.roleName
			from dbo.cms_siteResourceRoles as srroles 
			inner join dbo.cms_siteResourceRoleTypes as srrt on srrt.roleTypeID = srroles.roleTypeID
			where srrt.roleTypeName = 'UniversalRole'
			and srroles.roleName not in ('Super Administrator','System Operations Administrator')
			order by srroles.roleName;
		</cfquery>

		<cfreturn qryUniversalRoles>
	</cffunction>

	<cffunction name="getNetworkDetails" access="public" output="false" returntype="query">
		<cfargument name="networkID" type="numeric" required="yes">

		<cfset var qryNetwork = "">

		<cfquery name="qryNetwork" datasource="#application.dsn.memberCentral.dsn#">
			select networkID, networkName, accountName, supportProviderName, supportProviderPhone, 
				supportProviderEmail, addNewAccountsToApprovals, networkCode, emailFrom
			from dbo.networks
			where networkID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.networkID#">
		</cfquery>

		<cfreturn qryNetwork>
	</cffunction>

	<cffunction name="getSendGridSubUserDomains" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="yes">

		<cfset var qrySendGridSubUserDomains = "">

		<cfquery name="qrySendGridSubUserDomains" datasource="#application.dsn.platformMail.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
			
			SELECT su.subuserID, su.username, su.activeSubuserDomainID, sud.subuserDomainID, sud.sendingHostname, sud.linkBrandHostname
			FROM dbo.sendgrid_subusers AS su
			INNER JOIN dbo.sendgrid_subuserDomains AS sud ON sud.subuserID = su.subuserID
			WHERE su.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
			ORDER BY su.username;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qrySendGridSubUserDomains>
	</cffunction>

	<cffunction name="updateHostnameDetails" access="public" output="false" returntype="void">
		<cfargument name="hostNameID" type="numeric" required="true">
		<cfargument name="hostnamestatus" type="string" required="true">
		<cfargument name="sslstatus" type="string" required="true">
		<cfargument name="sslvalidation_url" type="string" required="true">
		<cfargument name="sslvalidation_body" type="string" required="true">
		<cfargument name="httpownershipverfication_url" type="string" required="true">
		<cfargument name="httpownershipverfication_body" type="string" required="true">

		<cfset var qryUpdateHostname = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryUpdateHostname">
			UPDATE dbo.siteHostNames
			SET cloudflare_hostnamestatus = NULLIF(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.hostnamestatus#">,''),
				cloudflare_sslstatus = NULLIF(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.sslstatus#">,''),
				cloudflare_sslvalidation_url = NULLIF(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.sslvalidation_url#">,''),
				cloudflare_sslvalidation_body = NULLIF(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.sslvalidation_body#">,''),
				cloudflare_httpownershipverfication_url = NULLIF(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.httpownershipverfication_url#">,''),
				cloudflare_httpownershipverfication_body = NULLIF(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.httpownershipverfication_body#">,''),
				cloudflare_lastChecked = GETDATE()
			WHERE hostNameID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.hostNameID#">
		</cfquery>
	</cffunction>

	<cffunction name="getWebHookDetails" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="webhookID" type="numeric" required="true">

		<cfset var qryWebhook = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryWebhook">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT webhookID, hookURL, [uid]
			FROM dbo.hooks_webhooks
			WHERE siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
			AND webhookID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.webhookID#">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryWebhook>
	</cffunction>

	<cffunction name="getWebHookSubsInfo" access="public" output="false" returntype="array">
		<cfargument name="orgID" type="numeric" required="yes">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="webhookID" type="numeric" required="yes">
		<cfargument name="ruleID" type="numeric" required="no" default="0">
		<cfargument name="arrSubTypes" type="array" required="no" default="#arrayNew(1)#">
		<cfargument name="arrSubs" type="array" required="no" default="#arrayNew(1)#">
		<cfargument name="arrSubRates" type="array" required="no" default="#arrayNew(1)#">
		<cfargument name="arrSubStatuses" type="array" required="no" default="#arrayNew(1)#">
		<cfargument name="paymentStatusID" type="numeric" required="no" default="0">

		<cfset var local = structNew()>
		<cfset local.arrSubRules = []>

		<cfif arrayLen(arguments.arrSubStatuses)>
			<cfset local.strSubArgs = {
				"subTypes":arrayLen(arguments.arrSubTypes) ? arguments.arrSubTypes.toList() : 0,
				"subs":arrayLen(arguments.arrSubs) ? arguments.arrSubs.toList() : 0,
				"subRates":arrayLen(arguments.arrSubRates) ? arguments.arrSubRates.toList() : 0,
				"subStatuses":arrayLen(arguments.arrSubStatuses) ? arguments.arrSubStatuses.toList() : 0
			}>

			<cfif local.strSubArgs.subTypes EQ 0>
				<cfset local.strSubArgs['subs'] = 0>
				<cfset local.strSubArgs['subRates'] = 0>
			<cfelseif local.strSubArgs.subs EQ 0>
				<cfset local.strSubArgs['subRates'] = 0>
			</cfif>
		<cfelse>
			<cfset local.strSubArgs = {}>
		</cfif>
		
		<cfquery name="local.qryWebhookSubs" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @orgID int, @siteID int, @webhookID int, @ruleID int, @paymentStatusID int;
			DECLARE @tmpSubTypeIDs TABLE (typeID int PRIMARY KEY);
			DECLARE @tmpSubscriptionIDs TABLE (subscriptionID int PRIMARY KEY);
			DECLARE @tmpSubRateIDs TABLE (rateID int PRIMARY KEY);
			DECLARE @tmpSubStatusIDs TABLE (statusID int PRIMARY KEY);
			DECLARE @tmpSubscriptionStatusChange TABLE (typeID int, subscriptionID int, rateID int, statusID int, paymentStatusID int, ruleID int);

			SET @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">;
			SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;
			SET @webhookID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.webhookID#">;
			SET @ruleID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.ruleID#">;
			SET @paymentStatusID = NULLIF(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.paymentStatusID#">,0);

			<cfif NOT structIsEmpty(local.strSubArgs)>
				INSERT INTO @tmpSubTypeIDs (typeID)
				SELECT DISTINCT listItem
				FROM dbo.fn_intListToTableInline(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.strSubArgs.subTypes#">,',');

				INSERT INTO @tmpSubscriptionIDs (subscriptionID)
				SELECT DISTINCT listItem
				FROM dbo.fn_intListToTableInline(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.strSubArgs.subs#">,',');

				INSERT INTO @tmpSubRateIDs (rateID)
				SELECT DISTINCT listItem
				FROM dbo.fn_intListToTableInline(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.strSubArgs.subRates#">,',');

				INSERT INTO @tmpSubStatusIDs (statusID)
				SELECT DISTINCT listItem
				FROM dbo.fn_intListToTableInline(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.strSubArgs.subStatuses#">,',');

				INSERT INTO @tmpSubscriptionStatusChange (typeID, subscriptionID, rateID, statusID, paymentStatusID, ruleID)
				SELECT DISTINCT NULLIF(t.typeID,0), NULLIF(s.subscriptionID,0), NULLIF(r.rateID,0), st.statusID, @paymentStatusID, @ruleID
				FROM @tmpSubTypeIDs AS t
				CROSS JOIN @tmpSubscriptionIDs AS s
				CROSS JOIN @tmpSubRateIDs AS r
				CROSS JOIN @tmpSubStatusIDs AS st;
				
				INSERT INTO @tmpSubscriptionStatusChange (typeID, subscriptionID, rateID, statusID, paymentStatusID, ruleID)
				SELECT typeID, subscriptionID, rateID, statusID, paymentStatusID, ruleID
				FROM dbo.hooks_webhooks_subscriptionStatusChange
				WHERE webhookID = @webhookID
				AND ruleID <> @ruleID;
			</cfif>

			SELECT DISTINCT s.statusName, t.[uid] AS typeUID, ss.[uid] AS subsUID, sr.[uid] AS rateUID, ps.statusName AS paymentStatusName, ws.ruleID
			FROM @tmpSubscriptionStatusChange AS ws
			INNER JOIN dbo.sub_statuses AS s ON s.statusID = ws.statusID
			LEFT OUTER JOIN dbo.sub_Types AS t ON t.typeID = ws.typeID
				AND t.siteID = @siteID
			LEFT OUTER JOIN dbo.sub_subscriptions AS ss ON ss.subscriptionID = ws.subscriptionID
				AND ss.orgID = @orgID
			LEFT OUTER JOIN dbo.sub_rates AS sr ON sr.rateID = ws.rateID
			LEFT OUTER JOIN dbo.sub_paymentStatuses AS ps ON ps.statusID = ws.paymentStatusID
			ORDER BY ws.ruleID;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfoutput query="local.qryWebhookSubs" group="ruleID">
			<cfset local.thisRuleID = val(local.qryWebhookSubs.ruleID)>
			<cfset local.tmp = {}>
			
			<cfquery name="local.qryThisRuleSubTypes" dbtype="query">
				SELECT DISTINCT typeUID
				FROM local.qryWebhookSubs
				WHERE ruleID = #local.thisRuleID#
				AND typeUID IS NOT NULL
			</cfquery>
			<cfif local.qryThisRuleSubTypes.recordCount>
				<cfset structInsert(local.tmp, "type_api_id", local.qryThisRuleSubTypes.columndata("typeUID"))>
			</cfif>

			<cfquery name="local.qryThisRuleSubs" dbtype="query">
				SELECT DISTINCT subsUID
				FROM local.qryWebhookSubs
				WHERE ruleID = #local.thisRuleID#
				AND subsUID IS NOT NULL
			</cfquery>
			<cfif local.qryThisRuleSubs.recordCount>
				<cfset structInsert(local.tmp, "subscription_api_id", local.qryThisRuleSubs.columndata("subsUID"))>
			</cfif>

			<cfquery name="local.qryThisRuleSubRates" dbtype="query">
				SELECT DISTINCT rateUID
				FROM local.qryWebhookSubs
				WHERE ruleID = #local.thisRuleID#
				AND rateUID IS NOT NULL
			</cfquery>
			<cfif local.qryThisRuleSubRates.recordCount>
				<cfset structInsert(local.tmp, "rate_api_id", local.qryThisRuleSubRates.columndata("rateUID"))>
			</cfif>

			<cfquery name="local.qryThisRuleSubStatuses" dbtype="query">
				SELECT DISTINCT statusName
				FROM local.qryWebhookSubs
				WHERE ruleID = #local.thisRuleID#
			</cfquery>
			<cfif local.qryThisRuleSubStatuses.recordCount>
				<cfset structInsert(local.tmp, "status", local.qryThisRuleSubStatuses.columndata("statusName"))>
			</cfif>

			<cfif len(local.qryWebhookSubs.paymentStatusName)>
				<cfset structInsert(local.tmp, "activationstatus", local.qryWebhookSubs.paymentStatusName)>
			</cfif>

			<cfif NOT structIsEmpty(local.tmp)>
				<cfset local.arrSubRules.append(local.tmp)>
			</cfif>
		</cfoutput>

		<cfreturn local.arrSubRules>
	</cffunction>

	<cffunction name="getWebHookSubsDetails" access="public" output="false" returntype="query">
		<cfargument name="webhookID" type="numeric" required="yes">
		<cfargument name="ruleID" type="numeric" required="yes">
		
		<cfset var qryWebHookSubs = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryWebHookSubs">
			SELECT webhookID, ISNULL(typeID,0) as typeID, ISNULL(subscriptionID,0) as subscriptionID, rateID, statusID, paymentStatusID
			FROM dbo.hooks_webhooks_subscriptionStatusChange
			WHERE webhookID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.webhookID#">
			AND ruleID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.ruleID#">
		</cfquery>

		<cfreturn qryWebHookSubs>
	</cffunction>

	<cffunction name="removeSubTypeFromWebhooks" access="public" output="false" returntype="boolean">
		<cfargument name="webhookID" type="numeric" required="yes">
		<cfargument name="ruleID" type="numeric" required="yes">

		<cfset var local = structNew()>

		<cftry>
			<cfquery name="local.qryDeleteSubs" datasource="#application.dsn.membercentral.dsn#">
				SET NOCOUNT ON;

				DECLARE @ruleID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.ruleID#">;

				DELETE FROM dbo.hooks_webhooks_subscriptionStatusChange
				WHERE webhookID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.webhookID#">
				AND ruleID = @ruleID;

				DELETE FROM dbo.hooks_webhooks_subscriptionStatusChangeRules
				WHERE ruleID = @ruleID;
			</cfquery>

			<cfset local.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.success>
	</cffunction>

	<cffunction name="hasLoginPolicy" access="public" output="false" returntype="boolean">
		<cfargument name="siteID" type="numeric" required="yes">
		
		<cfset var qryLoginPolicy = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryLoginPolicy">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;

			SELECT TOP 1 slp.loginPolicyID
			FROM dbo.siteLoginPolicies AS slp
			INNER JOIN dbo.cms_siteResources AS sr ON sr.siteID = @siteID
				AND sr.siteResourceID = slp.siteResourceID 
				AND sr.siteResourceStatusID = 1
			WHERE slp.siteID = @siteID;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn val(qryLoginPolicy.loginPolicyID) GT 0>
	</cffunction>

	<cffunction name="getLoginPolicy" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="loginPolicyID" type="numeric" required="yes">
		
		<cfset var qryLoginPolicy = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryLoginPolicy">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">, 
				@lowestRankedMCStaffControlledPolicyID int, @highestRankedNonMCStaffControlledPolicyID int;

			SELECT TOP 1 @lowestRankedMCStaffControlledPolicyID = slp.loginPolicyID
			FROM dbo.siteLoginPolicies AS slp
			INNER JOIN dbo.cms_siteResources AS sr ON sr.siteID = @siteID
				AND sr.siteResourceID = slp.siteResourceID 
				AND sr.siteResourceStatusID = 1
			WHERE slp.siteID = @siteID
			AND slp.isMCStaffControlled = 1
			ORDER BY slp.rank DESC;

			SELECT TOP 1 @highestRankedNonMCStaffControlledPolicyID = slp.loginPolicyID
			FROM dbo.siteLoginPolicies AS slp
			INNER JOIN dbo.cms_siteResources AS sr ON sr.siteID = @siteID
				AND sr.siteResourceID = slp.siteResourceID 
				AND sr.siteResourceStatusID = 1
			WHERE slp.siteID = @siteID
			AND slp.isMCStaffControlled = 0
			ORDER BY slp.rank;

			SELECT slp.loginPolicyID, slp.policyName, slp.policyCode, slp.isMCStaffControlled, slp.siteResourceID, slp.rank, 
				slp.loginLimitModeID, slp.maxDaysBetweenVerifications, slp.complianceDeadline, slp.complianceDaysForNewAccounts,
				CASE WHEN @lowestRankedMCStaffControlledPolicyID = slp.loginPolicyID THEN 1 ELSE 0 END AS isLowestRankedMCStaffControlledPolicyID,
				CASE WHEN @highestRankedNonMCStaffControlledPolicyID = slp.loginPolicyID THEN 1 ELSE 0 END AS isHighestRankedNonMCStaffControlledPolicyID,
				slp.reqCountOfFactors, slp.recCountOfFactors, slp.noOfDaysForRecCountPrompts
			FROM dbo.siteLoginPolicies AS slp
			INNER JOIN dbo.cms_siteResources AS sr ON sr.siteID = @siteID
				AND sr.siteResourceID = slp.siteResourceID 
				AND sr.siteResourceStatusID = 1
			WHERE slp.siteID = @siteID
			and slp.loginPolicyID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.loginPolicyID#">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryLoginPolicy>
	</cffunction>

	<cffunction name="getLoginPolicyMethods" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="loginPolicyID" type="numeric" required="yes">
		
		<cfset var qryLoginPolicyMethods = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryLoginPolicyMethods">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;

			SELECT slpvm.policyMethodID, vm.verificationMethodID, vm.methodName
			FROM dbo.siteLoginPolicies AS slp
			INNER JOIN dbo.cms_siteResources AS sr ON sr.siteID = @siteID
				AND sr.siteResourceID = slp.siteResourceID 
				AND sr.siteResourceStatusID = 1
			INNER JOIN dbo.siteLoginPolicyVerificationMethods AS slpvm ON slpvm.siteID = @siteID
				AND slpvm.loginPolicyID = slp.loginPolicyID
			INNER JOIN dbo.platform_verificationMethods AS vm ON vm.verificationMethodID = slpvm.verificationMethodID
			WHERE slp.siteID = @siteID
			AND slp.loginPolicyID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.loginPolicyID#">
			AND vm.isSelectable = 1
			ORDER BY vm.securityRank;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryLoginPolicyMethods>
	</cffunction>

	<cffunction name="getLoginPolicyMethod" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="loginPolicyID" type="numeric" required="yes">
		<cfargument name="policyMethodID" type="numeric" required="yes">
		
		<cfset var qryLoginPolicyMethod = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryLoginPolicyMethod">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT slpvm.policyMethodID, vm.verificationMethodID, vm.methodName, slpvm.isRequired
			FROM dbo.siteLoginPolicyVerificationMethods AS slpvm
			INNER JOIN dbo.platform_verificationMethods AS vm ON vm.verificationMethodID = slpvm.verificationMethodID
			WHERE slpvm.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
			AND slpvm.loginPolicyID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.loginPolicyID#">
			AND slpvm.policyMethodID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.policyMethodID#">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryLoginPolicyMethod>
	</cffunction>

	<cffunction name="getLoginVerificationMethods" access="public" output="false" returntype="query">
		<cfargument name="isSelectable" type="boolean" required="false" default="true">
		
		<cfset var qryLoginVerificationMethods = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryLoginVerificationMethods">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT verificationMethodID, methodCode, methodName, isSelectable, securityRank
			FROM dbo.platform_verificationMethods
			<cfif arguments.isSelectable>
				WHERE isSelectable = 1
			</cfif>
			ORDER BY securityRank;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryLoginVerificationMethods>
	</cffunction>

	<cffunction name="getSiteAPIAccessFees" access="public" output="false" returntype="query">
		<cfargument name="siteCode" type="string" required="yes">

		<cfset var qrySiteAPIAccessFees = "">

		<cfquery name="qrySiteAPIAccessFees" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
			
			DECLARE @siteCode varchar(10) = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.siteCode#">;

			SELECT a.monthlyFee, a.noofCallIncFee, a.overageFee, a.noofCallsInOverageFee, a.noFee
			FROM trialsmith.dbo.memberCentralBillingAPIAccess AS a
			INNER JOIN (
				SELECT orgCode, MAX(effectiveDate) AS effectiveDate
				FROM trialsmith.dbo.memberCentralBillingAPIAccess
				WHERE effectiveDate <= GETDATE()
				AND orgCode = @siteCode
				GROUP BY orgCode
			) tmp ON tmp.orgCode = a.orgCode AND tmp.effectiveDate = a.effectiveDate
			WHERE a.orgCode = @siteCode;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qrySiteAPIAccessFees>
	</cffunction>

	<cffunction name="getWebhookIDByUID" access="public" output="false" returntype="numeric">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="webhookuid" type="string" required="true">
		
		<cfscript>
			var qryLookupWebhookID = queryExecute("
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
				
				SELECT top 1 webhookID
				FROM dbo.hooks_webhooks
				WHERE siteID = :siteid
				AND [uid] = :webhookuid;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				", 
				{ 
					siteid = { value=arguments.siteID, cfsqltype="CF_SQL_INTEGER" },
					webhookuid = { value=arguments.webhookuid, cfsqltype="CF_SQL_IDSTAMP" }
				},
				{ datasource="#application.dsn.memberCentral.dsn#" }
			);

			return val(qryLookupWebhookID.webhookID);
		</cfscript>
	</cffunction>

	<cffunction name="getWebhookGroupUIDsArray" access="public" output="false" returntype="array">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="webhookID" type="numeric" required="true">

		<cfset var qryGroups = "">

		<cfquery name="qryGroups" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @orgID int, @siteID int, @webhookID int;
			SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;
			SET @webhookID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.webhookID#">;
			SELECT @orgID = dbo.fn_getOrgIDFromSiteID(@siteID);

			SELECT g.[uid]
			FROM dbo.ams_groups AS g
			INNER JOIN dbo.hooks_webhooks_memberGroupChanged AS hg on hg.groupID = g.groupID
				AND hg.webhookID = @webhookID
			WHERE g.orgID = @orgID
			AND g.[status] = 'A'
			AND g.hideOnGroupLists = 0
			ORDER BY groupPathExpanded;
			
			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryGroups.columndata("uid")>
	</cffunction>

	<cffunction name="getTrialSmithInfo" access="package" output="false" returntype="struct">
		<cfargument name="siteCode" type="string" required="yes">

		<cfset var local = structNew()>
		<cfset local.strReturn = {}>
		<cfset var siteCode = arguments.siteCode>

		<cfstoredproc datasource="#application.dsn.tlasites_trialsmith.dsn#" procedure="tla_getAssociations">
			<cfprocparam type="IN" cfsqltype="CF_SQL_VARCHAR" null="true">
			<cfprocparam type="IN" cfsqltype="CF_SQL_DATE" value="#dateformat(now(),'m/d/yyyy')#">
			<cfprocresult name="local.qrySites">
		</cfstoredproc>

		<cfset local.strReturn.qryDepoTLA = local.qrySites.filter(function(row) { return arguments.row.orgcode eq siteCode })>

		<cfquery name="local.strReturn.qryPriceGroups" datasource="#application.dsn.tlasites_trialsmith.dsn#" cachedwithin="#CreateTimeSpan(0,1,0,0)#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT pricegroupID, priceGroupName, priceGroupDesc, orderBy, isExpertGroup = 
				CASE WHEN EXISTS (SELECT mtpg.priceGroupID 
								FROM dbo.membertypePriceGroups as mtpg
								INNER JOIN dbo.depoTLA AS tla ON mtpg.priceGroupID = tla.PriceGroupID
								WHERE tla.state = 'EX'
								AND mtpg.priceGroupID = memberTypePriceGroups.pricegroupID) THEN 1
				ELSE 0
				END
			FROM dbo.memberTypePriceGroups
			ORDER BY orderBy;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfquery name="local.strReturn.qryDepoDefaultBankGroups" datasource="#application.dsn.tlasites_trialsmith.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select g.GroupID, g.Description
			from dbo.depoGroups as g
			inner join dbo.depoTLA as tla on tla.State = g.orgcode
			where g.DefaultBank = 1
			and g.orgcode = <cfqueryparam value="#arguments.siteCode#" cfsqltype="CF_SQL_VARCHAR">
			order by g.Description;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfquery name="local.strReturn.qryDepoDocTypes" datasource="#application.dsn.tlasites_trialsmith.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select TypeID, Description, acctcode
			from dbo.depoDocumentTypes
			order by Description;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfquery name="local.strReturn.qryStates" datasource="#application.dsn.tlasites_trialsmith.dsn#" cachedwithin="#createTimeSpan(1,0,0,0)#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select code, name
			from dbo.states
			order by orderpref, name;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfquery name="local.strReturn.qryNotes" datasource="#application.dsn.tlasites_trialsmith.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select NoteID, Note, DateEntered 
			from dbo.depotlaNotes 
			where orgcode = <cfqueryparam value="#arguments.siteCode#" cfsqltype="CF_SQL_VARCHAR">
			order by NoteId desc;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn local.strReturn>
	</cffunction>

</cfcomponent>
<cfoutput>
<div class="pm-cardbox<cfif len(attributes.data.cardFailedLastDate)> pm-border-danger</cfif>">
	<div class="pm-d-flex pm-align-items-center">
		<img src="/assets/common/images/payment/check.png" style="width:25px;" class="pm-mr-3">
		<div class="pm-d-flex pm-flex-column pm-font-size-lg">
			<div class="pm-font-weight-bold">**** #right(attributes.data.cardDetail,4)#</div>
			<div class="pm-font-size-md">#attributes.data.bankAccountType#</div>
			<cfif len(attributes.data.nickname)><div class="pm-font-size-md">#attributes.data.nickname#</div></cfif>
		</div>
		<div class="pm-ml-auto">
			<div class="pm-dropdown" tabindex="1">
				<i class="pm-dropbtnmask" tabindex="1"></i>
				<span class="pm-dropbtn pm-font-size-xl" role="listbox" aria-label="Actions"><i class="icon-ellipsis-horizontal"></i></span>
				<div class="pm-dropdown-content pm-font-size-md">
					<cfloop array="#attributes.data.arrActions#" index="local.thisAction">
						<a href="##" onclick="<cfif structKeyExists(local.thisAction,"onclick") and len(local.thisAction.onclick)>#local.thisAction.onclick#</cfif>$('.pm-dropdown').blur();" class="pm-col-auto" title="#local.thisAction.title#">
							<i class="#local.thisAction.feIconClass#" style="width:20px;"></i>
							<span class="pm-ml-2">#local.thisAction.title#</span>
						</a>
					</cfloop>
				</div>
			</div>
		</div>
	</div>
	<cfif attributes.data.keyExists("associatedWith") AND len(attributes.data.associatedWith)>
		<div class="badge badge-info" style="white-space:wrap;height:auto;text-align:left;margin-top:3px;">#attributes.data.associatedWith#</div>
	</cfif>
	<cfif len(attributes.data.cardFailedLastDate)>
		<div class="pm-text-red pm-mt-2"><i class="icon-warning-sign" style="margin-right:.15em;"></i> Issue: Failed #attributes.data.cardFailedLastDate#</div>
	</cfif>
</div>
</cfoutput>
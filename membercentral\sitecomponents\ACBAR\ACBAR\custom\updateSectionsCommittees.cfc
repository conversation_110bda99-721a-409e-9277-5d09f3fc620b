<cfcomponent extends="model.customPage.customPage" output="false">

<cffunction name="init" access="private" returntype="void" output="false">
   <cfargument name="Event" type="any">

      <!--- variables --->
      <cfset variables.applicationReservedURLParams = "fa,sid">
      <cfset variables.membershipDuesUID = "0915B3C9-B130-4EBD-B952-A1993E41CEB5"/>
      <cfset variables.subSetSectionsUID = "0D4F59DC-4FEF-498C-B94A-6080BC192034"/>
      <cfset variables.subSetCommitteesUID = "3BB57871-B9BA-48A1-B9C9-3244B758372B"/>
      <cfset variables.subSetSectionsLawStudentsUID = "CF30E38B-116A-492A-AB56-06BAEC93B13A"/>
      <cfset variables.subSetUIDs = "#variables.subSetSectionsUID#,#variables.subSetCommitteesUID#,#variables.subSetSectionsLawStudentsUID#"/>

      <cfscript>
         /* ************************* */
         /* Custom Page Custom Fields */
         /* ************************* */
         local.arrCustomFields = [];
         
         local.tmpField = { name="Step1Header", type="CONTENTOBJ", desc="Form title and instructions at the top of Step 1.", value="Please make changes below and click continue." }; 
         arrayAppend(local.arrCustomFields, local.tmpField);

         local.tmpField = { name="NonMemberMessage", type="CONTENTOBJ", desc="Message displayed to users without Membership subscription.", value="Membership Add-Ons are a benefit of ACBA active members. To join these, you must first become a member." }; 
         arrayAppend(local.arrCustomFields, local.tmpField);

         local.tmpField = { name="NoAddonsMessage", type="STRING", desc="Message displayed to users who have a Membership but do not currently have any add-on subscriptions.", value="You do not currently have this type of Add-On." }; 
         arrayAppend(local.arrCustomFields, local.tmpField);

         local.tmpField = { name="InstructionsRemove", type="CONTENTOBJ", desc="Instructions above Remove add-ons.", value="Your ACBA membership includes the following add-ons. To remove one, uncheck the box next to the group name." }; 
         arrayAppend(local.arrCustomFields, local.tmpField);

         local.tmpField = { name="InstructionsAdd", type="CONTENTOBJ", desc="Instructions above Add add-ons.", value="If you would like to add or update your ACBA membership add-ons, please check the box next to the add-on(s) you wish to add." }; 
         arrayAppend(local.arrCustomFields, local.tmpField);

         local.tmpField = { name="ActiveSubPastExpiration", type="CONTENTOBJ", desc="Message displayed to user with an Active membership subscription and the current date is past the expiration date on the active membership subscription ", value="Your current ACBA Membership is currently out of date.  Please access your renewal to update your Addons or contact the <NAME_EMAIL>." }; 
         arrayAppend(local.arrCustomFields, local.tmpField);

         local.tmpField = { name="Step2Header", type="CONTENTOBJ", desc="Instructions at the top of Step 2.", value="Please confirm your selections." }; 
         arrayAppend(local.arrCustomFields, local.tmpField);

         local.tmpField = { name="PayMethodCredit", type="STRING", desc="Payment Profile Code of Credit Card gateway.", value="MCPLHHXX" }; 
         arrayAppend(local.arrCustomFields, local.tmpField);

         local.tmpField = { name="PayMethodCheck", type="STRING", desc="Payment Profile Code of Pay Later gateway.", value="ACBA_Later" }; 
         arrayAppend(local.arrCustomFields, local.tmpField);
         
         local.tmpField = { name="PayMethodACH",type="STRING",desc="Payment Profile Code of ACH gateway.",value="" }; 
         arrayAppend(local.arrCustomFields, local.tmpField);

         local.tmpField = { name="ConfirmationMessage", type="CONTENTOBJ", desc="Message displayed on confirmation screen/email.", value="Thank you for updating your add-on subscriptions." }; 
         arrayAppend(local.arrCustomFields, local.tmpField);

         local.tmpField = { name="ConfirmationSub", type="STRING", desc="Subject line of emailed user confirmation", value="Committee/Section Update Receipt" }; 
         arrayAppend(local.arrCustomFields, local.tmpField);

         local.tmpField = { name="StaffConfirmationSub", type="STRING", desc="Subject line of emailed staff confirmation", value="Committee/Section Update Form Submission" }; 
         arrayAppend(local.arrCustomFields, local.tmpField);

         local.tmpField = { name="StaffConfirmationTo", type="STRING", desc="who do we send staff confirmations to", value="<EMAIL>" }; 
         arrayAppend(local.arrCustomFields, local.tmpField);

         local.tmpField = { name="ConfirmationFrom", type="STRING", desc="who do we send member confirmations from", value="<EMAIL>" }; 
         arrayAppend(local.arrCustomFields, local.tmpField);

         variables.strPageFields = application.objCustomPageUtils.setFormCustomFields(siteID=arguments.event.getValue('mc_siteInfo.siteID'),siteResourceID=this.siteResourceID,arrCustomFields=local.arrCustomFields);
     
         /* ***************** */
         /* set form defaults */
         /* ***************** */
         StructAppend(variables, application.objCustomPageUtils.setFormDefaults(event=arguments.event, 
               formName='frmUpdateSections',
               formNameDisplay='Add-on Update Form Submission',
               orgEmailTo=variables.strPageFields.StaffConfirmationTo,
               memberEmailFrom=variables.strPageFields.ConfirmationFrom
         ));
         variables.MEMBEREMAIL.SUBJECT = variables.strPageFields.ConfirmationSub;
         variables.ORGEMAIL.SUBJECT = variables.strPageFields.StaffConfirmationSub;
         variables.memberNumber = application.objMember.getMemberNumberByMemberID(memberID=variables.memberID,orgID=variables.orgID);
      </cfscript>

      <!--- for form action --->
   
   <cfset variables.baselink = "#cgi.SCRIPT_NAME#?#getBaseQueryString(false)#">        
</cffunction>

<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
   <cfargument name="Event" type="any">
   
   <cfset var local = structNew()>
   <cfset init(event=arguments.Event)>
   <cfset local.returnHTML = "">
   <cfset local.qryMemberShip =  application.objCustomPageUtils.getMembershipByStatus(statusCodeList='A,P,R,O', subUID = variables.membershipDuesUID, memberID = variables.memberID, siteID=variables.siteID)>

   <cfquery name="local.qryGetActiveExpirePast" dbtype="query">
      select * from [local].qryMemberShip where subEndDate < now()   
   </cfquery>
   
   <cfif isValidMember(variables.memberID) EQ false>
      <cfset local.returnHTML = showError(errorCode='notfound')>
   <cfelseif local.qryGetActiveExpirePast.recordCount>
      <cfset local.returnHTML = showError(errorCode='expiredsub')>
   <cfelse>
         <cfswitch expression="#arguments.event.getValue('fa','showForm')#">
            <cfcase value="error">
               <cfset local.returnHTML = showError(errorCode=arguments.event.getValue('sid',''))>
            </cfcase>
            <cfcase value="processStep1">
               <cfset local.returnHTML = processStep1(event=arguments.event)>
            </cfcase>
            <cfcase value="processStep2">	
               <cfset processStep2(event=arguments.event)>
            </cfcase>
            <cfcase value="complete">
               <cfif NOT isDefined("session.invoice")>
                  <cflocation url="#variables.baselink#" addtoken="false">
               <cfelse>
                  <cfsavecontent variable="local.returnHTML">
                        <cfoutput>
                           <p>#variables.strPageFields.ConfirmationMessage#</p><hr/>
                           #session.invoice#   
                           <br/>                             
                        </cfoutput>
                  </cfsavecontent>
                  <cfset StructDelete(session,"invoice")>
               </cfif>
            </cfcase>
            <cfdefaultcase>
               
               <cfset local.subSetUID = variables.subSetUIDs/>
               
               <cfset local.qryCurrentSubscriptions = application.objCustomPageUtils.getCurrentSubscriptions(memberID = variables.memberID, subSetUID = local.subSetUID, subUID = "#variables.membershipDuesUID#", siteID=variables.siteID)>
			           
			  <cfset local.qryAvailable = application.objCustomPageUtils.getAvailableSections(memberID = variables.memberID, existingSubsList = valueList(local.qryCurrentSubscriptions.subscriptionID), subSetUID = local.subSetUID, siteID=variables.siteID)>
		                        
			   <cfquery name="local.subSetCommitteesCurrentCount" dbtype="query">
				  select count(*) as total from [local].qryCurrentSubscriptions where setUID = '#variables.subSetCommitteesUID#' 
			   </cfquery>
				<cfquery name="local.subSetSectionsCurrentCount" dbtype="query">
				  select count(*) as total from [local].qryCurrentSubscriptions where setUID = '#variables.subSetSectionsUID#' 
			   </cfquery>
			   <cfquery name="local.subSetSectionsLawStudentsCurrentCount" dbtype="query">
				  select count(*) as total from [local].qryCurrentSubscriptions where setUID = '#variables.subSetSectionsLawStudentsUID#' 
			   </cfquery>
			                           
			   <cfquery name="local.subSetCommitteesCount" dbtype="query">
				  select count(*) as total from [local].qryAvailable where setUID = '#variables.subSetCommitteesUID#' 
			   </cfquery>
			   <cfquery name="local.subSetSectionsCount" dbtype="query">
				  select count(*) as total from [local].qryAvailable where setUID = '#variables.subSetSectionsUID#' 
			   </cfquery>
			   <cfquery name="local.subSetSectionsLawStudentsCount" dbtype="query">
				  select count(*) as total from [local].qryAvailable where setUID = '#variables.subSetSectionsLawStudentsUID#' 
			   </cfquery>
			   
			   <cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryAddons">
					SET NOCOUNT ON;
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

					select ao.addonID, ao.subscriptionID, ao.childSetID, ao.minAllowed, ao.maxAllowed
					from dbo.sub_addons ao 
					inner join dbo.sub_sets sets on sets.setID = ao.childSetID and sets.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteid')#">
					inner join dbo.sub_subscriptions s on s.subscriptionID =  ao.subscriptionID
					where s.uid in ('#variables.membershipDuesUID#') and sets.uid in ('#variables.subSetSectionsLawStudentsUID#') and sets.status = 'A';

					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				</cfquery>

               <cfsavecontent variable="local.headCode">
                  <cfoutput>
					#variables.pageJS#
                     <style>
                        ##updatePage input{margin:0px!important;}
                     </style>
                     <script type="text/javascript">                
                        
                        function hideAlert() { $('##issuemsg').html('').hide(); };
                        function showAlert(msg) { $('##issuemsg').html(msg).attr('class','alert-danger').show(); };

                        function _FB_validateForm() {
                           var thisForm = document.forms["#variables.formName#"];
                           var arrFormReq = new Array();	
                           hideAlert();
                           var amountRegex =  /(?:^\d{1,3}(?:\.?\d{3})*(?:,\d{2})?$)|(?:^\d{1,3}(?:,?\d{3})*(?:\.\d{2})?$)/;
                           var numSubsToRemove = $("[name='subsToExpire']:checked").length;
                           var numSubsToAdd = $("[name='newSubs']:checked").length;

                           if (numSubsToAdd==0 && numSubsToRemove==0) arrFormReq[arrFormReq.length] = 'Please indicate the changes you would like to make to your Add-on subscriptions.';

                           $('##subbox [data-allchangeprice=1]input[type=checkbox]:checked').each(function(){
                              if(($(this).is(':checked')) && (($.trim($('##sub_rateOverride' + $(this).val()).val()).length == 0) || ($.trim($('##sub_rateOverride' + $(this).val()).val()).length > 0 && (!amountRegex.test($.trim($('##sub_rateOverride' + $(this).val()).val())) || $('##sub_rateOverride' + $(this).val()).val() <= 0  ))) ){
                                 arrFormReq[arrFormReq.length] = " Enter a valid amount for "+$(this).data("subname")+".";
                              }
                           });
						   
						   var maxAllowed = parseInt("#local.qryAddons.maxAllowed#", 10);
						   var selectedCount = 0;

						   // Count selected checkboxes from "Your Current Sections - Law Students"
						   $("input[name='tmpsubsToExpire']:checked").each(function(){
							   if($(this).attr('setUID') == "#variables.subSetSectionsLawStudentsUID#") {
								   selectedCount++;
							   }
						   });

						   // Count selected checkboxes from "Sections - Law Students"
						   $("input[name='newSubs']:checked").each(function(){
							   if($(this).attr('newSetUID') == "#variables.subSetSectionsLawStudentsUID#") {
								   selectedCount++;
							   }
						   });

						   // Validate maxAllowed
						   if (selectedCount > maxAllowed) {
							   arrFormReq.push("You can select a maximum of " + maxAllowed + " Sections - Law Students.");
						   }

                           if (arrFormReq.length > 0) {
                              var msg = '';
                              for (var i=0; i < arrFormReq.length; i++) msg += arrFormReq[i] + '<br/>';
                              showAlert(msg);
                              $('html,body').animate({scrollTop: $('##issuemsg').offset().top-100},500);	
                              return false;
                           }
                           $("button[type='submit'],input[type='submit']", thisForm).html("Please Wait...").attr('disabled', 'disabled');
                           return true;
                        }	
                        function getTotalAmount() {
                           var totalAmount = 0;

                           $("##subbox input[type=checkbox]:checked").each(function(){        
                                 var price = $(this).attr('data-price');
                                 if($(this).data("allchangeprice") == "1"){
                                    price = $.trim($('##sub_rateOverride' + $(this).val()).val());
                                 }                                
                                 totalAmount += parseFloat(price);                                         
                           });

                           return totalAmount;
                        }	
                        function checkPrice(cb){
							
                           $('##subbox input[type=checkbox]:checked').each(function(){
                                 if ($(this).attr('data-chkboxgroup') == cb.attr('data-chkboxgroup') && $(this).attr('id') != cb.attr('id'))
                                    $(this).prop("checked", false).attr('checked',false);
                           });
                           var totalAmount = getTotalAmount();
                                 totalAmount = totalAmount.toFixed(2).replace(/./g, function(c, i, a) {
                                    return i && c !== "." && ((a.length - i) % 3 === 0) ? ',' + c : c;
                                 });	
                           $("##totalAmount").html("$ " + totalAmount);
                        }	
                        function changeSubsToExpire(cb){
                           if(cb.is(':checked')){
                              $("[name='subsToExpire'][value='"+cb.val()+"']").prop("checked", false).attr('checked',false);
                              $(cb).siblings('i').hide();
                           }else{
                              $("[name='subsToExpire'][value='"+cb.val()+"']").prop("checked", true).attr('checked',true);
                              $(cb).siblings('i').show();
                           }
                        }	
                        $(document).ready(function(){                                    
                           var subsToExpire = $.trim("#arguments.event.getValue('subsToExpire','')#");
                           var newSubs = $.trim("#arguments.event.getValue('newSubs','')#");
                           var subsToExpireAry = [];
                           var newSubsAry = [];
                           if(subsToExpire.length){
                                 subsToExpireAry = subsToExpire.split(",");
                           }
                           if(newSubs.length){
                                 newSubsAry = newSubs.split(",");
                           }
                           $.each(newSubsAry, function( index, value ) {
                              $("[value='"+value+"']").trigger("click");
                           });
                           $.each(subsToExpireAry, function( index, value ) {
                              $("[name='subsToExpire'][value='"+value+"']").prop("checked", true).attr('checked',true);
                              $("[name='tmpsubsToExpire'][value='"+value+"']").prop("checked", false).attr('checked',false);
                           });
						   
                        });
                     </script>
                  </cfoutput>
               </cfsavecontent>
               <cfhtmlhead text="#local.headCode#">
			   

               <cfsavecontent variable="local.returnHTML">
                  <cfoutput>
                     <div id="updatePage">
                        <cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return _FB_validateForm();">
                           <cfinput type="hidden" name="fa" id="fa" value="processStep1">
                           <cfinput type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="fa">
                           <div class="row-fluid">
                              <div class="span12">						
                                 <div id="issuemsg" style="display:none;padding:10px;"></div>
                              </div>
                           </div>
                           <div class="row-fluid">
                              <div class="span12">
                                 #variables.strPageFields.Step1Header#
                              </div>
                           </div>
                           <div class="row-fluid">
                              <div class="span12">
                                 #variables.strPageFields.InstructionsRemove#
                              </div>
                           </div>
                           
							<div id="remsubbox">
								<cfif val(local.subSetCommitteesCount.total) OR val(local.subSetCommitteesCurrentCount.total)>
									<div class="well">
										<legend>Your Current Committees</legend>
										<div class="row-fluid">
											<div class="span12">   
												<cfif val(local.subSetCommitteesCurrentCount.total) neq 0>                                 
													<cfoutput query="local.qryCurrentSubscriptions">  
														<cfif local.qryCurrentSubscriptions.setUID eq variables.subSetCommitteesUID>                                                    
															<div>
																<input type="checkbox" style="visibility: hidden;" <cfif ListFindNoCase('R,O',local.qryCurrentSubscriptions.childStatusCode) >disabled="disabled"</cfif> name="subsToExpire" id="subsToExpire#local.qryCurrentSubscriptions.subscriberID#" 
																value="#local.qryCurrentSubscriptions.subscriberID#"> 

																<input type="checkbox" <cfif ListFindNoCase('R,O',local.qryCurrentSubscriptions.childStatusCode) >disabled="disabled"<cfelse>checked="checked"</cfif> name="tmpsubsToExpire" id="tmpsubsToExpire#local.qryCurrentSubscriptions.subscriberID#" 
																value="#local.qryCurrentSubscriptions.subscriberID#" onChange="changeSubsToExpire($(this));" setUID="#local.qryCurrentSubscriptions.setUID#">
																#local.qryCurrentSubscriptions.subscriptionName# <i style="color:red" class="hide">This add-on membership will be expired.</i>
															</div>
														</cfif>                     									
													</cfoutput> 
												<cfelse>
													#variables.strPageFields.NoAddonsMessage#
												</cfif>
											</div>
										</div>
									</div>
								</cfif>
								<cfif val(local.subSetSectionsCount.total) OR val(local.subSetSectionsCurrentCount.total)>
									<div class="well">
										<legend>Your Current Sections</legend>
										<div class="row-fluid">
											<div class="span12">   
												<cfif val(local.subSetSectionsCurrentCount.total) neq 0>                                 
												<cfoutput query="local.qryCurrentSubscriptions">  
													<cfif local.qryCurrentSubscriptions.setUID eq variables.subSetSectionsUID>                                                                                                                          
														<div>
															<input type="checkbox" style="visibility: hidden;" <cfif ListFindNoCase('R,O',local.qryCurrentSubscriptions.childStatusCode) >disabled="disabled"</cfif> name="subsToExpire" id="subsToExpire#local.qryCurrentSubscriptions.subscriberID#" 
															value="#local.qryCurrentSubscriptions.subscriberID#"> 

															<input type="checkbox" <cfif ListFindNoCase('R,O',local.qryCurrentSubscriptions.childStatusCode) >disabled="disabled"<cfelse>checked="checked"</cfif> name="tmpsubsToExpire" id="tmpsubsToExpire#local.qryCurrentSubscriptions.subscriberID#" 
															value="#local.qryCurrentSubscriptions.subscriberID#" onChange="changeSubsToExpire($(this));" setUID="#local.qryCurrentSubscriptions.setUID#"> 
															#local.qryCurrentSubscriptions.subscriptionName# <i style="color:red" class="hide">This add-on membership will be expired.</i>
														</div>
													</cfif>                     									
												</cfoutput> 
												<cfelse>
													#variables.strPageFields.NoAddonsMessage#
												</cfif>
											</div>                                  
										</div>
									</div>
								</cfif>
								<cfif val(local.subSetSectionsLawStudentsCount.total) OR val(local.subSetSectionsLawStudentsCurrentCount.total)>
									<div class="well">
										<legend>Your Current Sections - Law Students</legend>
										<div class="row-fluid">
											<div class="span12">   
												<cfif val(local.subSetSectionsLawStudentsCurrentCount.total) neq 0>                                 
													<cfoutput query="local.qryCurrentSubscriptions">  
														<cfif local.qryCurrentSubscriptions.setUID eq variables.subSetSectionsLawStudentsUID>                                                                                                                          
															<div>
																<input type="checkbox" style="visibility: hidden;" <cfif ListFindNoCase('R,O',local.qryCurrentSubscriptions.childStatusCode) >disabled="disabled"</cfif> name="subsToExpire" id="subsToExpire#local.qryCurrentSubscriptions.subscriberID#" 
																value="#local.qryCurrentSubscriptions.subscriberID#"> 

																<input type="checkbox" <cfif ListFindNoCase('R,O',local.qryCurrentSubscriptions.childStatusCode) >disabled="disabled"<cfelse>checked="checked"</cfif> name="tmpsubsToExpire" id="tmpsubsToExpire#local.qryCurrentSubscriptions.subscriberID#" 
																value="#local.qryCurrentSubscriptions.subscriberID#" onChange="changeSubsToExpire($(this));" setUID="#local.qryCurrentSubscriptions.setUID#"> 
																#local.qryCurrentSubscriptions.subscriptionName# <i style="color:red" class="hide">This add-on membership will be expired.</i>
															</div>
														</cfif>                     									
													</cfoutput> 
												<cfelse>
													#variables.strPageFields.NoAddonsMessage#
												</cfif>
											</div>                                  
										</div>
									</div>
								</cfif>								
							<div class="row-fluid">
								<div class="span12">
								 #variables.strPageFields.InstructionsAdd#
								</div>
							</div>
							<div id="subbox">
								<cfif val(local.subSetCommitteesCount.total) OR val(local.subSetCommitteesCurrentCount.total)>
									<div class="well">
										<legend>Committees</legend>
										<div class="row-fluid">
											<cfset local.j = 1>                                        
											<cfoutput query="local.qryAvailable">    
											   <cfif local.qryAvailable.setUID eq variables.subSetCommitteesUID>  
												  <cfif local.j eq 1>
													 <div class="span6">
												  </cfif>                                  
												  <div>
													 <input type="checkbox" data-price="#local.qryAvailable.rateAmt#" value="#local.qryAvailable.subIDrateID#" 
														   data-chkboxgroup="sectionDivisionGrp#local.qryAvailable.subscriptionID#" name="newSubs" 
														   id="sectionDivision#local.qryAvailable.subIDrateID#" onChange="checkPrice($(this));" newSetUID="#local.qryAvailable.setUID#"> 
													 #local.qryAvailable.subscriptionName# #dollarFormat(local.qryAvailable.rateAmt)#
												  </div>
												  <cfif (local.j) eq Round(val(local.subSetCommitteesCount.total)/2)>
													 </div>
													 <div class="span6">
												  </cfif>
												  <cfset local.j = local.j+1>	         
											   </cfif>                                										
											</cfoutput>
											<cfif local.j GT 1></div></cfif>  
										</div>
									</div>
								</cfif>
							  
								<cfif val(local.subSetSectionsCount.total) OR val(local.subSetSectionsCurrentCount.total)>
									<div class="well" >
										<legend>Sections</legend>
										<div class="row-fluid">                                               
											<cfset local.j = 1>                                        
											<cfoutput query="local.qryAvailable">  
											   <cfif local.qryAvailable.setUID eq variables.subSetSectionsUID>    
												  <cfif local.j eq 1>
													 <div class="span6">
												  </cfif>                                  
												  <div>
													 <input type="checkbox" data-price="#local.qryAvailable.rateAmt#" value="#local.qryAvailable.subIDrateID#" 
														   data-chkboxgroup="sectionDivisionGrp#local.qryAvailable.subscriptionID#" name="newSubs" 
														   id="sectionDivision#local.qryAvailable.subIDrateID#" onChange="checkPrice($(this));" newSetUID="#local.qryAvailable.setUID#"> 
													 #local.qryAvailable.subscriptionName# #dollarFormat(local.qryAvailable.rateAmt)#
												  </div>
												  <cfif (local.j) eq Round(val(local.subSetSectionsCount.total)/2)>
													 </div>
													 <div class="span6">
												  </cfif>
												  <cfset local.j = local.j+1>	
											   </cfif>                                         										
											</cfoutput>
											<cfif local.j GT 1></div></cfif>  
										</div>
									</div>
								</cfif>
                              
								<cfif val(local.subSetSectionsLawStudentsCount.total) OR val(local.subSetSectionsLawStudentsCurrentCount.total)>
									<div class="well">
										<legend>Sections - Law Students </legend>
										<div class="row-fluid">
											<cfset local.j = 1>                                        
											<cfoutput query="local.qryAvailable">    
												<cfif local.qryAvailable.setUID eq variables.subSetSectionsLawStudentsUID>  
													<cfif local.j eq 1>
														<div class="span6">
													</cfif>                                  
													<div j="#local.j#" round="#local.subSetSectionsLawStudentsCurrentCount.total#">
														<input type="checkbox" data-price="#local.qryAvailable.rateAmt#" value="#local.qryAvailable.subIDrateID#" 
														data-chkboxgroup="sectionDivisionGrp#local.qryAvailable.subscriptionID#" name="newSubs" 
														id="sectionDivision#local.qryAvailable.subIDrateID#" onChange="checkPrice($(this));" newSetUID="#local.qryAvailable.setUID#"> 
														#local.qryAvailable.subscriptionName# #dollarFormat(local.qryAvailable.rateAmt)#
													</div>
													<cfif (local.j) eq Round(val(local.subSetSectionsLawStudentsCount.total)/2)>
														</div>
														<div class="span6">
													</cfif>
													<cfset local.j = local.j+1>											  
												</cfif>
											</cfoutput>
											<cfif local.j GT 1>
												</div>
											</cfif>  
										</div>
									</div>
								</cfif>
                           </div>
                           <div class="well">
                              <div class="row-fluid">
                                 <div class="span10">
                                    <b>Amount Due:</b>
                                 </div>
                                 <div class="span2">
                                    <b><span id="totalAmount">$ 0.00</span></b>
                                 </div>
                              </div>
                           </div>
                           <div class="row-fluid">
                              <div class="span12">
                                 <button type="submit" class="btn btn-default pull-right" name="btnToStep2">Continue</button>
                              </div>
                           </div>
                        </cfform>
                     </div>                                           
                  </cfoutput>
               </cfsavecontent>
            </cfdefaultcase>
         </cfswitch>
      </cfif>        

      <cfreturn returnAppStruct(local.returnHTML,"echo")>
</cffunction>

<cffunction name="processStep1" access="private" output="false" returntype="string">
   <cfargument name="Event" type="any">

   <cfset var local = structNew()>

   <cfset local.subSetUID = variables.subSetUIDs/>

   <cfset local.rateOverrideArray = structnew()>
   <cfloop list="#arguments.event.getValue('newSubs','')#" index="local.subIDrateID">
      <cfif val(arguments.event.getValue('sub_rateOverride#local.subIDrateID#',0))>
         <cfset local.rateOverrideArray["#local.subIDrateID#"] = val(arguments.event.getValue('sub_rateOverride#local.subIDrateID#',0))>
      </cfif> 
   </cfloop>

   <cfset local.strSubsToExpire = validateSubsToExpire(subsToExpire=arguments.event.getValue('subsToExpire',''), subSetUID = local.subSetUID, subUID = "#variables.membershipDuesUID#")>
   <cfset local.strNewSubs = validateNewSubs(newSubs=arguments.event.getValue('newSubs',''), subsToExpire=local.strsubsToExpire.subsToExpire, subSetUID=local.subSetUID, subUID = "#variables.membershipDuesUID#", rateOverrideArray = local.rateOverrideArray)>

   <cfset local.paymentRequired = false>
   <cfif local.strNewSubs.newSubsAmount gt 0>
      <cfset local.arrPayMethods = []>
      <cfif len(variables.strPageFields.PayMethodCredit) gt 0 AND variables.strPageFields.PayMethodCredit neq 'NULL'>
         <cfset ArrayAppend(local.arrPayMethods, variables.strPageFields.PayMethodCredit)>		
      </cfif>
      <cfif len(variables.strPageFields.PayMethodCheck) gt 0 AND variables.strPageFields.PayMethodCheck neq 'NULL'>
         <cfset ArrayAppend(local.arrPayMethods, variables.strPageFields.PayMethodCheck)>		
      </cfif>
      <cfif len(variables.strPageFields.PayMethodACH) gt 0 AND variables.strPageFields.PayMethodACH neq 'NULL'>
         <cfset ArrayAppend(local.arrPayMethods, variables.strPageFields.PayMethodACH)>		
      </cfif>

      <cfset local.strReturn = application.objCustomPageUtils.renderPaymentForm(
               arrPayMethods=local.arrPayMethods, 
               siteID=variables.siteID, 
               memberID=variables.memberID, 
               title="Choose Your Payment Method", 
               formName=variables.formName, 
               backStep="backSection")>
      <cfset local.paymentRequired = true>
   </cfif>

   <cfsavecontent variable="local.headCode">
      <cfoutput>
         <cfif local.paymentRequired>
            #local.strReturn.headcode#
         </cfif>
         <style type="text/css">
            div.tsAppSectionContentContainer{
               border: 1px solid ##e3e3e3;
               -webkit-border-radius: 4px;
               -moz-border-radius: 4px;
               border-radius: 4px;
            }
            input[name=mccf_payMeth]{margin:0;}
         </style>
            
         <script type="text/javascript"> 
            function validatePaymentForm(isPaymentRequired) {
            
               if(isPaymentRequired == true){
                  var arrReq = mccf_validatePPForm();
                  if (arrReq.length > 0) {
                     var msg = '';
                     for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
                     showAlert(msg);
                     return false;
                  }
               }
                     
               return true;
            }		
            function _FB_validateForm() {
               var thisForm = document.forms["#variables.formName#"];
               var arrReq = new Array();	
               
               $("button[type='submit'],input[type='submit']", thisForm).html("Please Wait...").attr('disabled', 'disabled');
               return true;
            }				
            function hideAlert() { $('##issuemsg').html('').hide(); };
            function showAlert(msg) { $('##issuemsg').html(msg).attr('class','alert-danger').show();$('html, body').animate({ scrollTop: 0 }, 500); };	
            function mc_goBackForm(form,type){
               $("[name='fa']").val('');
               form.submit();
            }
            $(document).ready(function(){
               $('[name=mccf_payMeth]').change(function() {
                  var btnContinue = $("##mccfdiv_"+this.value.replace(/\s/g, '')).find("[name='btnContinue']");
                  btnContinue.removeClass('btn').addClass('btn');
                  $("##mccfdiv_"+this.value.replace(/\s/g, '')).find("[name='btnBack']").removeClass('btn').addClass('btn').html("Back to Previous Step");
                  if(this.value == "#variables.strPageFields.PayMethodCredit#"){
                     var meth = "#variables.strPageFields.PayMethodCredit#";
                     var btnContinue = $("##mccfdiv_"+meth.replace(/\s/g, '')).find("[name='btnContinue']");
                     btnContinue.removeClass('btn').addClass('btn');
                     $("##mccfdiv_"+meth.replace(/\s/g, '')).find("[name='btnBack']").removeClass('btn').addClass('btn').html("Back to Previous Step");                            
                     btnContinue.html("Confirm Changes & Pay");   
                     $("##mccfdiv_"+meth.replace(/\s/g, '')).css('overflow','unset');
                     $("##mccfdiv_"+meth.replace(/\s/g, '')).css('height','auto');
                     $("##mccfdiv_"+meth.replace(/\s/g, '')).show();
                     btnContinue.html("Confirm Changes & Pay");                            
                  }
                  else if(this.value == "#variables.strPageFields.PayMethodACH#")
                     btnContinue.html("Confirm Changes & Pay");
                  else if(this.value == "#variables.strPageFields.PayMethodCheck#")
                     btnContinue.html("Confirm Changes & Send Payment");
               });
            });
         </script>
      </cfoutput>
   </cfsavecontent>
   <cfif local.strNewSubs.newSubsAmount gt 0>
      <cfhtmlhead text="#local.headcode#">
   </cfif>
   <cfset local.subSummary = ""/>
   <cfsavecontent variable="local.returnHTML">
      <cfoutput>
         <cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return _FB_validateForm();">
            <cfinput type="hidden" name="fa" id="fa" value="processStep2">
            <cfinput type="hidden" name="subsToExpire" id="subsToExpire" value="#local.strsubsToExpire.subsToExpire#">
            <cfinput type="hidden" name="newSubs" id="newSubs" value="#local.strNewSubs.newSubs#">
            <cfinput type="hidden" name="doNotIncludeList" id="doNotIncludeList" value="fa">
            <cfloop list="#arguments.event.getValue('newSubs','')#" index="local.subIDrateID">
               <cfif val(arguments.event.getValue('sub_rateOverride#local.subIDrateID#',0))>
                   <cfinput type="hidden" name="sub_rateOverride#local.subIDrateID#" value="#val(arguments.event.getValue('sub_rateOverride#local.subIDrateID#',0))#">
               </cfif> 
            </cfloop>
           
            <div class="row-fluid">
               <button name="btnBack" type="button" class="tsAppBodyButton btn pull-right" onclick="hideAlert();mc_goBackForm($('###variables.formName#'),'backSection');">Back to Previous Step</button>
            </div>
            <div class="row-fluid">
               <div class="span12">						
                     <div id="issuemsg" style="display:none;padding:10px;margin-top: 10px;margin-bottom: 10px;"></div>
               </div>
            </div>
            <div class="row-fluid">
               <div class="span12">
                     #variables.strPageFields.Step2Header#
               </div>
            </div>

            <div class="well">                   
               <cfif listLen(local.strsubsToExpire.subsToExpire)>
                     <cfloop query="local.strSubsToExpire.qrySubsToExpire">
                        <div class="row-fluid">
                           <div class="span12">
                                 REMOVE: #local.strSubsToExpire.qrySubsToExpire.subscriptionName#
                                 <cfset local.subSummary = local.subSummary & "REMOVE: #local.strSubsToExpire.qrySubsToExpire.subscriptionName#
                                 "/>
                           </div>
                        </div>
                     </cfloop>
               </cfif>
               <cfif listLen(local.strNewSubs.newSubs)>
                     <cfloop query="local.strNewSubs.qrySubsToAdd">
                        <div class="row-fluid">
                           <div class="span12">
                                 <div class="span10">ADD: #local.strNewSubs.qrySubsToAdd.subscriptionName#</div>
                                 <div class="span2 pull-right">#dollarFormat(local.strNewSubs.qrySubsToAdd.rateAmt)#</div>
                                 <cfset local.subSummary = local.subSummary & "ADD: #local.strNewSubs.qrySubsToAdd.subscriptionName# - #dollarFormat(local.strNewSubs.qrySubsToAdd.rateAmt)#
                                 "/>
                           </div>
                        </div>
                     </cfloop>
                     <cfif local.strNewSubs.newSubsAmount gt 0>
                        <div class="row-fluid">
                           <div class="span10"><b>Amount Due for New Sections:</b></div>
                           <div class="span2 pull-right"><b>#dollarFormat(local.strNewSubs.newSubsAmount)#</b></div>
                        </div>
                     </cfif>
               </cfif>
            </div>

            <cfif local.strNewSubs.newSubsAmount gt 0>
               #local.strReturn.paymentHTML#
            <cfelse>
               <div class="row-fluid">
                     <div class="span12">
                        <button name="btnContinue" type="submit" class="tsAppBodyButton btn" onclick="hideAlert();">Confirm Changes</button>
                        <button name="btnBack" type="button" class="tsAppBodyButton btn pull-right" onclick="hideAlert();mc_goBackForm($('###variables.formName#'),'backSection');">Back to Previous Step</button>
                     </div>  
               </div>  
            </cfif>
            <cfinput type="hidden" name="subSummary" id="subSummary" value="#local.subSummary#">
         </cfform>
         <cfif local.paymentRequired>
            #local.strReturn.headcode#
         </cfif>
      </cfoutput>
   </cfsavecontent>
               
   <cfreturn local.returnHTML>
</cffunction>

<cffunction name="processStep2" access="private" output="false" returntype="void">
   <cfargument name="Event" type="any">

      <cfset var local = structNew()>
      
      <cfset local.subSetUID = variables.subSetUIDs/>

      <cfset local.profileID = 0/>
      <cfset local.profileCode = ""/>
      
      <cfif ListContainsNoCase("#variables.strPageFields.PayMethodCredit#,#variables.strPageFields.PayMethodCheck#,#variables.strPageFields.PayMethodACH#",arguments.event.getValue('mccf_payMeth',''))>
         <cfif arguments.event.getValue('mccf_payMeth','') eq "#variables.strPageFields.PayMethodCredit#">
            <cfset local.profileCode = variables.strPageFields.PayMethodCredit>
         <cfelseif arguments.event.getValue('mccf_payMeth','') eq "#variables.strPageFields.PayMethodCheck#">
            <cfset local.profileCode = variables.strPageFields.PayMethodCheck>
         <cfelseif arguments.event.getValue('mccf_payMeth','') eq "#variables.strPageFields.PayMethodACH#">
            <cfset local.profileCode = variables.strPageFields.PayMethodACH>
         </cfif>

         <cfset local.profileID = application.objCustomPageUtils.acct_getProfileID(siteid=variables.siteID,profileCode=local.profileCode)>
      </cfif>

      <cfset local.rateOverrideArray = structnew()>
      <cfloop list="#arguments.event.getValue('newSubs','')#" index="local.subIDrateID">
         <cfif val(arguments.event.getValue('sub_rateOverride#local.subIDrateID#',0))>
            <cfset local.rateOverrideArray["#local.subIDrateID#"] = val(arguments.event.getValue('sub_rateOverride#local.subIDrateID#',0))>
         </cfif> 
      </cfloop>

      <cfset local.qryCurrentSubscriptions = application.objCustomPageUtils.getCurrentSubscriptions(memberID = variables.memberID, subSetUID = local.subSetUID, subUID = "#variables.membershipDuesUID#", siteID=variables.siteID)>
      <cfset local.strSubsToExpire = validateSubsToExpire(subsToExpire=arguments.event.getValue('subsToExpire',''), subSetUID = local.subSetUID, subUID = "#variables.membershipDuesUID#")>
      <cfset local.strNewSubs = validateNewSubs(newSubs=arguments.event.getValue('newSubs',''), subsToExpire=local.strsubsToExpire.subsToExpire, subSetUID=local.subSetUID, subUID = "#variables.membershipDuesUID#", rateOverrideArray = local.rateOverrideArray)>

      <cfif local.strNewSubs.newSubsAmount gt 0 and arguments.event.getValue('mccf_payMeth','') eq "#variables.strPageFields.PayMethodCredit#" and NOT val(arguments.event.getTrimValue('p_#local.profileID#_mppid','0'))>
         <cflocation url="#variables.baselink#&fa=error&sid=paymentFailed" addtoken="no">
      </cfif>
      <cfset local.qryHistorySectionUpdate = application.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='General', subName='Section Update')>
      
      <cfif local.strNewSubs.newSubsAmount gt 0>
         <cfset local.useHistoryID = application.objCustomPageUtils.mh_addHistory(memberID=variables.memberID, categoryID=local.qryHistorySectionUpdate.categoryID, 
                                    subCategoryID=local.qryHistorySectionUpdate.subCategoryID, description=arguments.event.getValue('subSummary',''), 
                                    enteredByMemberID=variables.memberID, newAccountsOnly=false,dollarAmt=local.strNewSubs.newSubsAmount)>	
      <cfelse>
         <cfset local.useHistoryID = application.objCustomPageUtils.mh_addHistory(memberID=variables.memberID, categoryID=local.qryHistorySectionUpdate.categoryID, 
                                    subCategoryID=local.qryHistorySectionUpdate.subCategoryID, description=arguments.event.getValue('subSummary',''), 
                                    enteredByMemberID=variables.memberID, newAccountsOnly=false)>	
      </cfif>


      <cfset local.qryMemberShip =  application.objCustomPageUtils.getMembershipByStatus(statusCodeList='A,P,R,O', subUID = variables.membershipDuesUID, memberID = variables.memberID, siteID=variables.siteID)>
      <cfquery name="local.qryGetSubActive" dbtype="query">
         select * from [local].qryMemberShip where statusCode = 'A' 
      </cfquery>

      <cfquery name="local.qryGetSubAccepted" dbtype="query">
         select * from [local].qryMemberShip where statusCode = 'P' 
      </cfquery>

      <cfquery name="local.qryGetSubBilled" dbtype="query">
         select * from [local].qryMemberShip where statusCode in ('R','O') 
      </cfquery>

      <cfquery name="local.qryGetSubActiveBilled" dbtype="query">
         select * from [local].qryMemberShip where statusCode in ('A','R','O') 
      </cfquery>

      
      <cfset local.subSetUID = variables.subSetUIDs/>
      
      <cfset local.qryCustomSubsToExpireArray = arrayNew()/>
      <cfset local.qryCustomSubsToExpireStatusArray = arrayNew()>
      <cfif local.qryGetSubBilled.recordcount>
         <cfset local.qryMemberShip = local.qryGetSubActiveBilled/>
         <cfset local.qryCustomSubsToExpire = application.objCustomPageUtils.getSubscriberFromParentSubscriptions(memberID = variables.memberID, subscriberIDList = local.strsubsToExpire.subsToExpire ,parentStatusCodeList ="A,R,O" , subSetUID = local.subSetUID, subUID = variables.membershipDuesUID, siteID=variables.siteID)>
         <cfset local.qryCustomSubsToExpireArray = listToArray(valueList(local.qryCustomSubsToExpire.subscriberID))>
         <cfset local.qryCustomSubsToExpireStatusArray = listToArray(valueList(local.qryCustomSubsToExpire.statusCode))>
      <cfelseif local.qryGetSubAccepted.recordcount>
         <cfset local.qryMemberShip = local.qryGetSubAccepted/>
         <cfset local.qryCustomSubsToExpire = application.objCustomPageUtils.getSubscriberFromParentSubscriptions(memberID = variables.memberID, subscriberIDList = local.strsubsToExpire.subsToExpire, parentStatusCodeList ="A,P", subSetUID = local.subSetUID, subUID = variables.membershipDuesUID, siteID=variables.siteID)>
         <cfset local.qryCustomSubsToExpireArray = listToArray(valueList(local.qryCustomSubsToExpire.subscriberID))>
         <cfset local.qryCustomSubsToExpireStatusArray = listToArray(valueList(local.qryCustomSubsToExpire.statusCode))>
      <cfelseif len(local.strsubsToExpire.SUBSTOEXPIRE)>
         <cfset local.qryCustomSubsToExpireArray = listToArray(valueList(local.strsubsToExpire.QRYSUBSTOEXPIRE.subscriberID))/>
         <cfset local.qryCustomSubsToExpireStatusArray = listToArray(valueList(local.strsubsToExpire.QRYSUBSTOEXPIRE.childStatusCode))>
      </cfif>

      <!--- -------------- --->
      <!--- 1. Delete Subs --->
      <!--- -------------- --->

      
      <cfset local.strRemoveMessages = structNew()>
      <cfif listLen(local.strsubsToExpire.subsToExpire)>
         <cfset local.objSubAdmin = CreateObject("component","model.admin.subscriptions.subscriptions")>
         <cfloop from="1" to="#arrayLen(local.qryCustomSubsToExpireArray)#" index="local.i">
               <cfif local.qryCustomSubsToExpireStatusArray[local.i] EQ 'A'>
                  <cfset local.strExpireResult = local.objSubAdmin.expireMemberSubscription(actorMemberID=variables.memberID,
                                    actorStatsSessionID=session.cfcuser.statsSessionID, memberID=variables.memberID,
                                    subscriberID=local.qryCustomSubsToExpireArray[local.i], siteID=variables.siteID,
                                    AROption='C')>
               <cfelse>
                  <cfset local.strExpireResult = local.objSubAdmin.removeMemberSubscription(actorMemberID=variables.memberID,
                                    actorStatsSessionID=session.cfcuser.statsSessionID, memberID=variables.memberID,
                                    subscriberID=local.qryCustomSubsToExpireArray[local.i], siteID=variables.siteID,
                                    AROption='C')>
               </cfif>
         </cfloop>
      </cfif>

      <!--- -------------- --->
      <!--- 2. Create Subs --->
      <!--- -------------- --->

      <cfif listLen(local.strNewSubs.newSubs)>
			<cfset local.objSubReg = CreateObject("component","model.admin.subscriptions.SubscriptionReg")>
			<cfset local.arrCreatedSubs = arrayNew(1)>

            <cfset local.subscriberIDArray = listToArray(valueList(local.qryMemberShip.subscriberID))>
            <cfset local.subscriberStatusCodeArray = listToArray(valueList(local.qryMemberShip.statusCode))>

            <cfif arrayLen(local.subscriberIDArray) gt 0>
               
               <cfloop query="local.strNewSubs.qrySubsToAdd">
                  <cfloop from="1" to="#arrayLen(local.subscriberIDArray)#" index="local.q">
                     <cfif local.subscriberStatusCodeArray[local.q] EQ 'A'>
                           <cfset local.subExpired = application.objCustomPageUtils.getSubscriberExpiredFromParentSubscriptions(memberID=variables.memberID, parentSubscriberID=local.subscriberIDArray[local.q])>

                           <cfif local.subExpired.recordCount>
                              <cfquery name="local.subRemove" dbtype="query">
                                 select * from [local].subExpired where subscriptionID = #local.strNewSubs.qrySubsToAdd.subscriptionID#;
                              </cfquery>
                              <cfif local.subRemove.recordCount>
                                 <cfset local.objSubAdmin = CreateObject("component","model.admin.subscriptions.subscriptions")>
                                 <cfset local.strExpireResult = local.objSubAdmin.removeMemberSubscription(actorMemberID=variables.memberID,
                                                               actorStatsSessionID=session.cfcuser.statsSessionID, memberID=variables.memberID,
                                                               subscriberID=local.subRemove.subscriberID, siteID=variables.siteID,
                                                               AROption='C')>
                              </cfif>
                           </cfif>
                     </cfif>
                  </cfloop>
                  <cfloop from="1" to="#arrayLen(local.subscriberIDArray)#" index="local.i">
                     <cfset arrayAppend(local.arrCreatedSubs,
                           local.objSubReg.addSubscriberToTree(orgID=variables.orgID, parentSubscriberID=local.subscriberIDArray[local.i], 
                                             memberID=variables.memberID, subscriptionID=local.strNewSubs.qrySubsToAdd.subscriptionID,
                                             rateFrequencyID=local.strNewSubs.qrySubsToAdd.rfid, status=local.subscriberStatusCodeArray[local.i],
                                             pcFree=local.strNewSubs.qrySubsToAdd.rateAmt is 0, rateAmt=local.strNewSubs.qrySubsToAdd.rateAmt,
                                             recordedByMemberID=variables.memberID, statsSessionID=session.cfcuser.statsSessionID))>
                  </cfloop>
               </cfloop>
         </cfif>
      </cfif>
      
      <!--- ------------------------- --->
      <!--- 3. Payment and accounting --->
      <!--- ------------------------- --->
      <cfif local.strNewSubs.newSubsAmount gt 0>
         <cfset local.strAccTemp = { totalPaymentAmount=local.strNewSubs.newSubsAmount, assignedToMemberID=variables.memberID, recordedByMemberID=variables.memberID, rc=arguments.event.getCollection() } >
         <cfset local.strAccTemp.payment = { detail="#variables.organization# - #variables.formNameDisplay#", amount=local.strAccTemp.totalPaymentAmount, profileID=local.profileID, profileCode=local.profileCode }>
         <cfset local.objAccounting = CreateObject("component","model.system.platform.accountingObj").init()>
         <cfset local.strACCResponse = local.objAccounting.doCustomFormPaymentAndAccounting(argumentcollection=local.strAccTemp)>

         <cfif StructKeyExists(local.strACCResponse,"paymentResponse") and local.strACCResponse.paymentResponse.responseCode is 1 and local.strACCResponse.paymentResponse.mc_transactionID gt 0>	
            <cfloop array="#local.arrCreatedSubs#" index="local.thisSub">
               <cfif local.thisSub.invoiceID gt 0 and QueryAddRow(local.objAccounting.invoicePool)>
                  <cfset QuerySetCell(local.objAccounting.invoicePool,"invoiceid",local.thisSub.invoiceID)>
                  <cfset QuerySetCell(local.objAccounting.invoicePool,"invoiceProfileID",local.thisSub.invoiceProfileID)>
                  <cfset QuerySetCell(local.objAccounting.invoicePool,"amount",local.thisSub.invoiceAmount)>
                  <cfif ListContainsNoCase("#variables.strPageFields.PayMethodCredit#,#variables.strPageFields.PayMethodCredit#",arguments.event.getValue('mccf_payMeth',''))>
                     <cfset application.objCustomPageUtils.sub_checkActivationsByMember(orgID=variables.orgID, memberID=variables.memberID, subscriberID=local.thisSub.SUBSCRIBERID, bypassQueue=1)>
                  </cfif>
               </cfif>
            </cfloop>
            <cfset local.objAccounting.allocateToInvoice(paymentTransactionID=local.strACCResponse.paymentResponse.mc_transactionID, recordedByMemberID=variables.memberID, transactionDate=now())>
         </cfif>
      </cfif>

      <!--- -------------- --->
      <!--- 4. Email Staff --->
      <!--- -------------- --->
      <cfsavecontent variable="local.pageCSS">
         <cfoutput>
         <style type="text/css">
            body { font-size:12pt; font-family: Calibri,Arial,Helvetica; }
            .customPage { font-size:12pt; font-family: Calibri,Arial,Helvetica; }
            p { font-size:12pt; font-family: Calibri,Arial,Helvetica; }
            .msgHeader{ background:rgba(10,34,56,0.90); font-weight:bold; padding:5px;color:white!important; }
            .frmText{ font-size:12pt; } 
            .b{ font-weight:bold; }
         </style>
         </cfoutput>
      </cfsavecontent>	

      <cfsavecontent variable="local.invoice">
         <cfoutput>
            #local.pageCSS#
         <p>#variables.formNameDisplay# submitted on #dateformat(now(),"dddd, m/d/yyyy")# #timeformat(now(),"h:mm tt")#.</p>

         <table cellpadding="2" cellspacing="0" width="100%" border="1" class="customPage">
                  <tr class="msgHeader"><td colspan="2" class="b">Member Information</td></tr>
                  <tr><td class="frmText b">MemberNumber:</td><td class="frmText">#session.cfcuser.memberdata.membernumber#&nbsp;</td></tr>
                  <tr><td class="frmText b">First Name:</td><td class="frmText">#session.cfcUser.memberData.firstName#&nbsp;</td></tr>	
                  <tr><td class="frmText b">Last Name:</td><td class="frmText">#session.cfcUser.memberData.lastName#&nbsp;</td></tr>	
                  <tr><td class="frmText b">Email:</td><td class="frmText">#session.cfcUser.memberData.email#&nbsp;</td></tr>
                  <tr><td colspan="2">&nbsp;</td></tr>

                  <tr class="msgHeader"><td colspan="2" class="b">Add-on Changes</td></tr>
                  <cfif listLen(local.strsubsToExpire.subsToExpire)>
                     <cfloop query="local.strSubsToExpire.qrySubsToExpire">
                        <tr>
                              <td class="frmText b">Removed</td>
                              <td class="frmText">
                                 #local.strSubsToExpire.qrySubsToExpire.subscriptionName#
                                 <cfif structKeyExists(local.strRemoveMessages,local.strSubsToExpire.qrySubsToExpire.subscriberID)>
                                    <!--MSG#local.strSubsToExpire.qrySubsToExpire.subscriberID#-->
                                 </cfif>
                              </td>
                        </tr>
                     </cfloop>
                  </cfif>
                  <cfif listLen(local.strNewSubs.newSubs)>
                     <cfloop query="local.strNewSubs.qrySubsToAdd">
                        <tr><td class="frmText b">Added</td><td class="frmText">#local.strNewSubs.qrySubsToAdd.subscriptionName# <cfif local.strNewSubs.qrySubsToAdd.rateAmt NEQ 0> - #local.strNewSubs.qrySubsToAdd.rateName# (#dollarFormat(local.strNewSubs.qrySubsToAdd.rateAmt)#)</cfif></td></tr>
                     </cfloop>
                  </cfif>
                  <tr><td colspan="2">&nbsp;</td></tr>

                  <tr class="msgHeader"><td colspan="2" class="b">Payment Information</td></tr>
                  <tr><td class="frmText b"><b>Amount Due for New Sections:</b> &nbsp;</td><td class="frmText"><b>#dollarFormat(local.strNewSubs.newSubsAmount)#</b>&nbsp;</td></tr>
                  <tr><td colspan="2">&nbsp;</td></tr>

                  <cfif local.strNewSubs.newSubsAmount gt 0>
                     <tr>
                        <td class="frmText b">Pay Method:</td><td class="frmText">
                           <cfif arguments.event.getValue('mccf_payMeth','') eq "#variables.strPageFields.PayMethodCredit#">
                              Credit Card
                              <cfset arguments.event.setValue('p_#local.profileID#_mppid',int(val(arguments.event.getValue('p_#local.profileID#_mppid',0)))) />
                              
                              <cfif arguments.event.getValue('p_#local.profileID#_mppid') gt 0>
                                 <cfset local.qrySavedInfoOnFile = application.objPayments.getSavedInfoOnFile(
                                          mppid     = arguments.event.getValue('p_#local.profileID#_mppid'),
                                          memberID  = variables.memberID,
                                          profileID = local.profileID)>
                                 - #local.qrySavedInfoOnFile.detail#
                              </cfif>	
                              
                           <cfelseif arguments.event.getValue('mccf_payMeth','') eq "#variables.strPageFields.PayMethodCheck#">
                              Check
                           <cfelseif arguments.event.getValue('mccf_payMeth','') eq "#variables.strPageFields.PayMethodACH#">
                              ACH                               
                           </cfif>                            					
                        </td>
                     </tr>
                  </cfif>
         </table>
         </cfoutput>
      </cfsavecontent>

      <!--- email submitter --->
      <cftry>
         <cfset local.emailSentToUser = TRUE>

         <cfif len(session.cfcUser.memberData.email)>

            <cfsavecontent variable="local.mailContent">
               <cfoutput>						
                  <p>#variables.strPageFields.ConfirmationMessage#</p><hr/>
                  #local.invoice#	
               </cfoutput>
            </cfsavecontent>

            <cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
                     emailfrom={ name="", email=variables.memberEmail.from },
                     emailto=[{ name="", email=session.cfcUser.memberData.email }],
                     emailreplyto=variables.ORGEmail.to,
                     emailsubject=variables.strPageFields.ConfirmationSub,
                     emailtitle="#arguments.event.getTrimValue('mc_siteinfo.sitename')# - #variables.formNameDisplay#",
                     emailhtmlcontent=local.mailContent,
                     siteID=variables.siteID,
                     memberID=val(variables.useMID),
                     messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
                     sendingSiteResourceID=this.siteResourceID)>
         <cfelse>
            <cfthrow>
         </cfif> 
      <cfcatch type="Any">
         <cfset local.emailSentToUser = FALSE>
      </cfcatch>
      </cftry>

      <cfset local.invoiceForStaff = local.invoice>
      <cfloop collection="#local.strRemoveMessages#" item="local.thisMsg">
         <cfset local.invoiceForStaff = replaceNoCase(local.invoiceForStaff, "<!--MSG#local.thisMsg#-->", "<span style='color:red'><b>#local.strRemoveMessages[local.thisMsg]#</b></span>")>
      </cfloop>

   <!--- email staff --->
   <cftry>
      <cfsavecontent variable="local.mailContent">
         <cfoutput>
            <cfif NOT local.emailSentToUser>
               <p><b>The member was NOT sent an e-mail confirmation of this submission.</b></p>
            </cfif>
            #local.invoiceForStaff#
         </cfoutput>
      </cfsavecontent>

      <cfscript>
         local.arrEmailTo = [];
         variables.ORGEmail.to = replace(variables.ORGEmail.to,",",";","all");
         local.toEmailArr = listToArray(variables.ORGEmail.to,';');
         for (local.i=1; local.i lte arrayLen(local.toEmailArr); local.i++) {
            local.arrEmailTo.append({ name:'', email:local.toEmailArr[local.i] });
         }
         if (arrayLen(local.arrEmailTo)) {
            local.responseStruct = application.objEmailWrapper.sendMailESQ(
               emailfrom={ name="", email=variables.ORGEmail.FROM },
               emailto=local.arrEmailTo,
               emailreplyto=variables.ORGEmail.FROM,
               emailsubject=local.subject,
               emailtitle="#arguments.event.getTrimValue('mc_siteinfo.sitename')# - #variables.formNameDisplay#",
               emailhtmlcontent=local.mailContent,
               siteID=variables.siteID,
               memberID=arguments.event.getValue('mc_siteinfo.sysMemberID'),
               messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
               sendingSiteResourceID=this.siteResourceID
            )


         }
      </cfscript>

   <cfcatch type="Any">
   </cfcatch>
   </cftry>

   <!--- relocate to message page --->
   <cfset session.invoice = local.invoice>
   
   <cflocation url="#variables.baselink#&fa=complete" addtoken="false">
</cffunction>

<cffunction name="isValidMember" access="private" output="false" returntype="string">
   <cfargument name="memberID" type="numeric" required="true">

   <cfset var local = structNew()>
   <cfset local.isValid = false>

   <cfset local.qryMember = application.objMember.getMemberInfo(memberID=arguments.memberID, orgID=variables.orgID)>

   <cfif local.qryMember.recordcount>

      <cfset local.qryActiveSubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='A', distinct=true)>

      <cfif listFindNoCase(valueList(local.qryActiveSubs.uid), variables.membershipDuesUID)>
         <cfset local.isValid = true>
      <cfelse>
         <cfset local.qryAcceptedSubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='P', distinct=true)>
         <cfif listFindNoCase(valueList(local.qryAcceptedSubs.uid), variables.membershipDuesUID)>
            <cfset local.isValid = true>
         <cfelse>
            <cfset local.qryBilledSubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='O', distinct=true)>

            <cfif listFindNoCase(valueList(local.qryBilledSubs.uid), variables.membershipDuesUID)>
               <cfset local.isValid = true>
            </cfif>
         </cfif>	
      </cfif>
   </cfif>
   <cfreturn local.isValid>
</cffunction>

<cffunction name="validateSubsToExpire" access="private" output="false" returntype="struct">
   <cfargument name="subsToExpire" type="string" required="true">
   <cfargument name="subSetUID" type="string" required="true">
   <cfargument name="subUID" type="string" required="true">

   <cfset var local = structNew()>
   <cfset local.strReturn = structNew()>

   <cfset local.strReturn.subsToExpire = arguments.subsToExpire>
   <cfif listlen(local.strReturn.subsToExpire)>
         <cfset local.strReturn.qrySubsToExpire = application.objCustomPageUtils.getCurrentSubscriptions(memberID = variables.memberID, subsToExpire=local.strReturn.subsToExpire, subSetUID = arguments.subSetUID, subUID = arguments.subUID, siteID=variables.siteID)>
      <cfset local.strReturn.subsToExpire = valueList(local.strReturn.qrySubsToExpire.subscriberID)>
   </cfif>

   <cfreturn local.strReturn>
</cffunction>

<cffunction name="validateNewSubs" access="private" output="false" returntype="struct">
   <cfargument name="newSubs" type="string" required="true">
   <cfargument name="subsToExpire" type="string" required="true">
   <cfargument name="subSetUID" type="string" required="true">
   <cfargument name="subUID" type="string" required="true">
   <cfargument name="rateOverrideArray" type="struct" required="false">  

   <cfset var local = structNew()>
      
   <cfset local.strReturn = structNew()>

   <cfset local.strReturn.newSubsAmount = 0>
   <cfset local.strReturn.newSubs = arguments.newSubs>
   <cfif listlen(local.strReturn.newSubs)>
      <cfset local.qryCurrentSubscriptions = application.objCustomPageUtils.getCurrentSubscriptions(memberID = variables.memberID, subSetUID = arguments.subSetUID, subUID = arguments.subUID, siteID=variables.siteID)>
      <cfset local.strReturn.qrySubsToAdd = application.objCustomPageUtils.getAvailableSections(memberID = variables.memberID, existingSubsList = valueList(local.qryCurrentSubscriptions.subscriptionID), subSetUID = arguments.subSetUID, memberID = variables.memberID, newSubs=local.strReturn.newSubs, siteID=variables.siteID)>

      <cfset local.strReturn.newSubs = valueList(local.strReturn.qrySubsToAdd.subIdRateId)>	

      <cfset local.totalRateAmt = 0>
      <cfloop query="local.strReturn.qrySubsToAdd">
         <cfif structKeyExists(arguments.rateOverrideArray, local.strReturn.qrySubsToAdd.subIdRateId)>
            <cfset local.totalRateAmt = local.totalRateAmt + arguments.rateOverrideArray["#local.strReturn.qrySubsToAdd.subIdRateId#"]>
            <cfset local.strReturn.qrySubsToAdd.rateAmt = arguments.rateOverrideArray["#local.strReturn.qrySubsToAdd.subIdRateId#"]>
         <cfelse>
             <cfset local.totalRateAmt = local.totalRateAmt + val(local.strReturn.qrySubsToAdd.rateAmt)>
         </cfif>        
      </cfloop>
      <cfset local.strReturn.newSubsAmount = val(local.totalRateAmt)>
   </cfif>
   <cfreturn local.strReturn>
</cffunction>

<cffunction name="showError" access="private" output="false" returntype="string">
   <cfargument name="errorCode" type="string" required="true">

   <cfset var local = structNew()>

   <cfsavecontent variable="local.returnHTML">
      <cfoutput>
      <div class="tsAppSectionHeading">There was an issue continuing with your application.</div>
      <div class="tsAppSectionContentContainer">						
         <cfif arguments.errorCode eq "notfound">					
            #variables.strPageFields.NonMemberMessage#
         <cfelseif arguments.errorCode eq "paymentFailed">
            This submission is missing information. Ensure you have entered all required fields and it is valid.
         <cfelseif arguments.errorCode eq "expiredsub">
            #variables.strPageFields.ActiveSubPastExpiration#
         </cfif>
      </div>
      </cfoutput>
   </cfsavecontent>

   <cfreturn local.returnHTML>
</cffunction>
</cfcomponent>
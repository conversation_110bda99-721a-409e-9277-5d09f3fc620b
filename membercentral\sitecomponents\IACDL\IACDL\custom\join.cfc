<cfcomponent extends="model.customPage.customPage" output="false">

	<cffunction name="init" access="private" returntype="void" output="false">
		<cfargument name="Event" type="any">

		<!--- variables --->
		<cfset variables.objCustomPageUtils = application.objCustomPageUtils>
		<cfset variables.orgID = arguments.event.getTrimValue('mc_siteinfo.orgID')>
		<cfset variables.siteID = arguments.event.getTrimValue('mc_siteinfo.siteID')>

		<cfset variables.formNameDisplay = "Join Form">
		<cfset variables.organization = arguments.event.getValue('mc_siteInfo.ORGShortName')>

		<cfset variables.profile_1._profileCode = "AUTHCIM">
		<cfset variables.profile_1._profileID = application.objCustomPageUtils.acct_getProfileID(siteid=arguments.event.getValue('mc_siteinfo.siteid'),profileCode=variables.profile_1._profileCode)>
		<cfset variables.profile_1._description = "#variables.organization# - #variables.formNameDisplay#">

		<cfset variables.qryStates = application.objCommon.getStates()>
		<cfset variables.qryAffiliateType =application.objCustomPageUtils.getCustomFieldData(orgID=variables.orgID,columnName='Your Affiliate')> 
		<cfset variables.contactType = application.objCustomPageUtils.getCustomFieldData(orgID=variables.orgID,columnName='Contact Type')>
		<cfset variables.expertiseAreas = application.objCustomPageUtils.getCustomFieldData(orgID=variables.orgID,columnName='Areas Of Expertise')>
		<cfset variables.practiceType = application.objCustomPageUtils.getCustomFieldData(orgID=variables.orgID,columnName='Type Of Practice')>
		<cfset variables.county = application.objCustomPageUtils.getCustomFieldData(orgID=variables.orgID,columnName='County')>
		
		<cfset variables.formName = "join">
		<cfscript>
			local.arrCustomFields = [];
			local.tmpField = { name="staffEmailTitle", type="CONTENTOBJ", desc="Content at top of user confirmation page and email", value="Thank you for your application to IACDL." }; 
			arrayAppend(local.arrCustomFields, local.tmpField);
			variables.strPageFields = variables.objCustomPageUtils.setFormCustomFields(siteID=arguments.event.getValue('mc_siteInfo.siteID'), siteResourceID=this.siteResourceID, arrCustomFields=local.arrCustomFields);
        </cfscript>
		<!--- Email confirmation settings --->
		<cfset variables.strEMailSettings_staff = { 
				from="<EMAIL>", 
				to="<EMAIL>,<EMAIL>", 
				subject=variables.formNameDisplay,
				type="HTML",
				mailerid=arguments.event.getTrimValue('mc_siteinfo.sitename')
			}>
		<cfset variables.strEMailSettings_member = { 
				from="<EMAIL>", 
				to=arguments.event.getTrimValue('email','<EMAIL>'), 
				subject=variables.formNameDisplay,
				type="HTML",
				mailerid=arguments.event.getTrimValue('mc_siteinfo.sitename')
			}>
		
		<!--- for form action --->
		<cfset variables.applicationReservedURLParams = "fa,sid">
		<cfset variables.baselink = "/?#getBaseQueryString(false)#">
	</cffunction>
	
	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset init(event=arguments.Event)>
		<cfset local.returnHTML = "">
		<cfset local.objCffp = CreateObject("component","model.cfformprotect.cffpVerify").init()>

		<cfset local.strMsgCodes = structNew()>
		<cfset local.strMsgCodes['507D7690-F01F-AF51-C9CCB83F029DD786'] = { err=1, msg="This submission has been flagged as spam and was not submitted." }>
		<cfset local.strMsgCodes['50BA4017-F01F-AF51-C9CCE480104528F0'] = { err=1, msg="This submission is missing information. Ensure you have entered all required fields and the e-mail address is valid." }>

		<cfif listFindNoCase("processStep1,processStep2",arguments.event.getValue('fa','showForm'))>
			<!--- --------------- --->
			<!--- form validation --->
			<!--- --------------- --->
			<!---  --->
			<cfif NOT val(arguments.event.getTrimValue('memberID','0')) 
				OR NOT len(arguments.event.getTrimValue('firstName','')) 
				OR NOT len(arguments.event.getTrimValue('lastName','')) 	 
				OR NOT len(arguments.event.getTrimValue('phone',''))
				OR NOT len(arguments.event.getTrimValue('cell_phone',''))			
				OR NOT len(arguments.event.getTrimValue('email',''))
				OR NOT IsValid("email",arguments.event.getTrimValue('email',''))
				OR NOT len(arguments.event.getTrimValue('job_title',''))
				OR NOT len(arguments.event.getTrimValue('contactType',''))
				OR NOT len(arguments.event.getTrimValue('firm_name',''))
				OR NOT len(arguments.event.getTrimValue('website',''))
				OR NOT len(arguments.event.getTrimValue('firm_address',''))
				OR NOT len(arguments.event.getTrimValue('firm_city','')) 
				OR NOT len(arguments.event.getTrimValue('firm_stateID',''))
				OR NOT len(arguments.event.getTrimValue('firm_zip','')) 
				OR NOT len(arguments.event.getTrimValue('firm_fax','')) 
				OR NOT len(arguments.event.getTrimValue('amount',''))
				OR NOT len(arguments.event.getTrimValue('expertiseArea',''))
				OR NOT len(arguments.event.getTrimValue('practiceType',''))
				>
				
				<cflocation url="#variables.baselink#&fa=showForm&sid=50BA4017-F01F-AF51-C9CCE480104528F0" addtoken="no">

			<cfelseif len(arguments.event.getTrimValue('contactType',''))>
				<cfset local.allowedArr = ['Attorney','Lifetime Member','Public Defender & Law Proffesor']>
				<cfif listFind(arrayToList(local.allowedArr), arguments.event.getTrimValue('contactType',''))>
					<cfif NOT val(arguments.event.getTrimValue('barDate','0')) >
						<cflocation url="#variables.baselink#&fa=showForm&sid=50BA4017-F01F-AF51-C9CCE480104528F0" addtoken="no">
					</cfif>
				</cfif>
			</cfif>
		</cfif>
		<cfswitch expression="#arguments.event.getValue('fa','showForm')#">
			<cfcase value="processStep1">
							
				<cfset variables.profile_1.strPaymentForm = application.objPayments.showGatewayInputForm(
																			siteid=arguments.event.getValue('mc_siteinfo.siteid'),
																			profilecode=variables.profile_1._profileCode,
																			pmid = arguments.event.getTrimValue('memberid'),
																			showCOF = arguments.event.getTrimValue('memberid') EQ session.cfcUser.memberData.memberID,
																			usePopup=false,
																			usePopupDIVName='ccForm',
																			autoShowForm=1)>
				
				<cfif len(variables.profile_1.strPaymentForm.headCode)>
					<cfhtmlhead text="#application.objCommon.minText(variables.profile_1.strPaymentForm.headCode)#">
				</cfif>

				<cfset local.memAmt = arguments.event.getTrimValue('amount') >
					
					<cfif arguments.event.getValue('amount') eq 1>					
					
					 	<cfset local.memAmt = 2500>
					  	<cfset  local.memberShipName = ''>
					
					<cfelseif arguments.event.getValue('amount') eq 2>
					 	<cfset local.memAmt = 500 	>
					  	<cfset  local.memberShipName = 'Per Year'>
					
					<cfelseif arguments.event.getValue('amount') eq 3>
					 	<cfset local.memAmt = 195 	>
					  	<cfset  local.memberShipName = 'Per Year'>
					<cfelseif arguments.event.getValue('amount') eq 4>
					 	<cfset local.memAmt = 100 	>
					  	<cfset  local.memberShipName = 'Per Year'>
					<cfelseif arguments.event.getValue('amount') eq 5>
					 	<cfset local.memAmt = 125 	>
					  	<cfset  local.memberShipName = 'Per Year'>
					<cfelseif arguments.event.getValue('amount') eq 6>
					 	<cfset local.memAmt = 75 	>
					  	<cfset  local.memberShipName = 'Per Year'>
					<cfelseif arguments.event.getValue('amount') eq 7>
					 	<cfset local.memAmt = 50 	>
					  	<cfset  local.memberShipName = 'Per Year'>
					</cfif>
					<cfset  local.totalAmount = local.memAmt>
				
							
				<cfset local.amountToChargeNow = local.totalAmount>
	
				<cfset local.formName = "join">

				<cfsavecontent variable="local.headCode">
					<cfoutput>
					<style type="text/css">
						body { font-size:12pt; font-family: Calibri,Arial,Helvetica; }
						h5 { font-size:13pt; font-weight:bold; margin:0 0 0 0;}
						div.CPSection { border:1px solid ##666666; margin-bottom:15px; }
						div.CPSection div.CPSectionTitle { font-size:14pt; font-weight:bold; color:##ffffff; padding:10px; background:##355b8c; font-family: Calibri,Arial,Helvetica; }
						div.CPSection div.BB { border-bottom:1px solid ##666666; }
						div.CPSection span.frmText, div.CPSection td.frmText { font-size:9pt; color:##505050; font-family: Calibri,Arial,Helvetica; }
						div.CPSection span.frmText span.block { display:block; }
						div.CPSection span.frmText span.b { font-weight:bold; }
						div.CPSection td.r { text-align:right; }
						label{cursor: pointer; cursor: hand;}
						a {font-size:10pt;}
						.l{text-align: left;}
						.P{padding: 10px;}
					</style>
					<script type="text/javascript">
						function getSelectedRadio(buttonGroup) {
							if (buttonGroup[0]) {
								for (var i=0; i<buttonGroup.length; i++) { if (buttonGroup[i].checked) return i }
							} else { if (buttonGroup.checked) return 0; }
							return -1;
						}						
						function checkPaymentMethod() {							
							if ($('##payMethCC').is(':checked')) {
								$('##CCInfo').show();
								$('##BDInfo').hide();
							}else{
								$('##CCInfo').hide();
								$('##BDInfo').show();
							}
						}	
						function getMethodOfPayment() {
							var btnGrp = document.forms['#local.formName#'].payMeth;
							var i = getSelectedRadio(btnGrp);
							if (i == -1) return "";
							else {
								if (btnGrp[i]) return btnGrp[i].value;
								else return btnGrp.value;
							}
						}						
						function _FB_validateForm() {
							var thisForm = document.forms["#local.formName#"];
							var arrReq = new Array();
							// -----------------------------------------------------------------------------------------------------------------
							if (!_FB_hasValue(thisForm['payMeth'], 'RADIO')) arrReq[arrReq.length] 	= 'Method of Payment';
							var MethodOfPaymentValue = getMethodOfPayment();
							alert(MethodOfPaymentValue);
							return false;
							if( MethodOfPaymentValue == 'CC' )	{
								#variables.profile_1.strPaymentForm.jsvalidation#	
							}
							
							// -----------------------------------------------------------------------------------------------------------------
							if (arrReq.length > 0) {
								var msg = 'Please address the following issues with your application:\n\n';
								for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '\n';
								alert(msg);
								return false;
							}
							return true;
						}					
						function hideAlert() { $('##issuemsg').html('').hide(); };
						function showAlert(msg) { $('##issuemsg').html(msg).attr('class','alert').show(); };
						function showAlertDone(msg) { $('##issuemsg').html(msg).attr('class','success').show(); };
					</script>
					</cfoutput>
				</cfsavecontent>
				<cfhtmlhead text="#local.headCode#">

				<cfsavecontent variable="local.returnHTML">

					<cfoutput>
					<div style="clear:both;"><br/><br/></div>
					<div>
						<div class="TitleText" style="padding-bottom:15px;">#variables.formNameDisplay#</div>

						<div class="CPSection">
							<div class="CPSectionTitle NVTitle">MEMBERSHIP INFORMATION</div>
							<div style="padding:10px;">
								<table cellspacing="0" cellpadding="4" border="0" width="100%">
								<tr><td>Membership Amount.</td><td class="r">#dollarFormat(local.memAmt)# #local.memberShipName#&nbsp;</td></tr>
								<tr><td><b>Total Charge:</b> &nbsp;</td><td class="r"><b>#dollarFormat(local.totalAmount)# #local.memberShipName#</b>&nbsp;</td></tr>
								</table>
							</div>
						</div>
						
						<div id="paymentTable">
							<div id="payerrDIV" style="display:none;margin:6px 0;"></div>
							<div class="form">
								<cfform name="#local.formName#" id="#local.formName#" method="post" action="#variables.baselink#" onsubmit="return _FB_validateForm();">
									<cfinput type="hidden" name="fa" id="fa" value="processStep2">
										
											
									<cfloop list="#arguments.event.getValue('fieldnames','')#" index="local.thisField">
										<cfif NOT listFindNoCase("fa,btnAddAssoc,btnSubmit",local.thisField) and left(local.thisField,9) neq "formfield">
											<cfinput type="hidden" name="#local.thisField#" id="#local.thisField#" value="#arguments.event.getValue(local.thisField)#">
										</cfif>
									</cfloop>
									
									<div class="CPSection">
										<div class="CPSectionTitle NVTitle">*Method of Payment for Membership</div>
										<div class="P">
											<table cellpadding="2" cellspacing="0" width="100%" border="0">
												<tr valign="top">
													<td colspan="2">Please select your preferred method of payment from the options below.</td>
												</tr>
												<tr>
													<td>
														<table cellpadding="2" cellspacing="0" width="100%" border="0">
															<tr>
																<td width="25"><input value="CC" class="tsAppBodyText optionsRadio" name="payMeth" id="payMethCC" type="radio" onClick="checkPaymentMethod();"></td>
																<td><label for="payMethCC">Credit Card</label></td>
															</tr>
														</table>
													</td>
												</tr>
											</table>
										</div>
									</div>								
										
									<div id="CCInfo" class="CPSection" style="display:none;">
										<div class="CPSectionTitle NVTitle">Credit Card Information</div>
										<div id="ccForm" style="padding:10px;">
											<div>#variables.profile_1.strPaymentForm.inputForm#</div>
											<div><button type="submit" class="btn" name="btnSubmit">SUBMIT</button></div>
										</div>
									</div>
								</cfform>
							</div>
						</div>
					</div>
					</cfoutput>
				</cfsavecontent>
			</cfcase>

			<cfcase value="processStep2">
				<cfset processStep2(event=arguments.event)>				
			</cfcase>

			<cfcase value="complete">
				<cfif NOT isDefined("session.invoice")>
					<cflocation url="#variables.baselink#" addtoken="false">
				</cfif>
				
				<cfsavecontent variable="local.pageCSS">
					<cfoutput>
					<style type="text/css">
						body { font-size:12pt; font-family: Calibri,Arial,Helvetica; }
						.customPage { font-size:12pt; font-family: Calibri,Arial,Helvetica; }
						p { font-size:12pt; font-family: Calibri,Arial,Helvetica; }
						.msgHeader{ background:##224563; color:##fff; font-weight:bold; padding:5px;}
						.msgHeader td{padding: 2px}
						.frmText{ font-size:9pt; color:##505050; }
						.b{ font-weight:bold; }
						.required { color:red; }
						.l{text-align: left;}
						.P{padding: 10px;}
					</style>
					</cfoutput>
				</cfsavecontent>						

				<cfsavecontent variable="local.returnHTML">
					<cfoutput>
					<cfhtmlhead text="#local.pageCSS#">
					<div style="clear:both;"><br/><br/></div>
					<div>

						<p><b>Thank you for your application!</b></p>
						<p>A confirmation of your application has been e-mailed to you.</p>
						<cfif isDefined("session.invoice")>
							#session.invoice#
							<cfset StructDelete(session,"invoice")>
						</cfif>
					</div>
					</cfoutput>
				</cfsavecontent>
			</cfcase>

			<cfdefaultcase>

				<!--- setup memberdata struct and prefill with logged in member --->
				<cfset local.memberData = {}>
				<cfset local.memberData.homeAddress = application.objMember.getMemberAddressByAddressType(orgID=variables.orgID, memberID=session.cfcUser.memberData.memberID, addressType="Address")>
				<cfset local.memberData.phone = application.objMember.getMemberPhones(orgID=variables.orgID, memberID=session.cfcUser.memberData.memberID)>
				<cfset local.memberData.fax = application.objMember.getMemberPhones(orgID=variables.orgID, memberID=session.cfcUser.memberData.memberID, addressTypeID=494)>
				<cfset local.memberData.firmAddress = application.objMember.getMemberAddressByAddressType(orgID=variables.orgID, memberID=session.cfcUser.memberData.memberID, addressType="Business Address")>
				
				<cfsavecontent variable="local.headCode">
					<cfoutput>
					<style type="text/css">
						body { font-size:12pt; font-family: Calibri,Arial,Helvetica; }
						h5 { font-size:13pt; font-weight:bold; margin:0 0 0 0;}
						div.CPSection { border:1px solid ##666666; margin-bottom:15px;}
						div.CPSection div.CPSectionTitle { font-size:14pt; font-weight:bold; color:##ffffff; padding:10px; background:##355b8c; font-family: Calibri,Arial,Helvetica;}
						div.CPSection div.BB { border-bottom:1px solid ##666666; }
						div.CPSection span.frmText, div.CPSection td.frmText { font-size:12pt; color:##505050; font-family: Calibri,Arial,Helvetica; }
						div.CPSection span.frmText span.block { display:block; }
						div.CPSection span.frmText span.b { font-weight:bold; }
						div.CPSection td.r { text-align:right; width:25%;}
						div.frmButtons{ padding:12px; border-top:1px solid ##666666; border-bottom:1px solid ##666666; font-family: Calibri,Arial,Helvetica; }
						.alert { background:##fff6bf url(/assets/common/images/exclamation.png) 15px center no-repeat; text-align:left; margin-top:4px; padding:5px 20px 5px 45px; border-top:2px solid ##f00; border-bottom:2px solid ##f00; }
						.success { background:##fff6bf url(/assets/common/images/tick.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px; border-top:2px solid ##0f0; border-bottom:2px solid ##0f0; }
						.required { color:red; }
						.info{ font-style:italic; font-size:8pt; color:##777777; }
						label{cursor: pointer; cursor: hand;}
						label a {font-size:10pt;}
						##zoneD p { margin:0!important; padding:5px!important; }
						##zoneE p { margin:0!important; padding:5px!important; }
						##zoneF p { margin:0!important; padding:5px!important; }
						.frmRow1{ background:##fff; }
						.frmRow2{ background:##dee1e3; }		
						.bb { border-bottom:1px solid ##0d3566; }	
						##divTotals {font-weight:bold;}		
						.l a{font-size:10pt;}	
						.newmem-div{ padding: 10px;width: 100%;}
						.datePicker {
							background-image: url("/assets/common/images/calendar/monthView.gif");
							background-position: right center;
							background-repeat: no-repeat;
						}
						.hide{display: none}
						.l{text-align: left;}
						.P{padding: 5px;}
						@media (max-width: 600px) {
							##join .CPSection.step1 table select, 
							##join  table button.ui-widget, 
							##join .CPSection.step1 table input{
								width:100% !important;
							}
												
						}
					</style>
					<script type="text/javascript">
						var allowed_arr = ['Attorney','Lifetime Member','Public Defender & Law Proffesor'];
						function selectMember() {
							hideAlert();
							$.colorbox( {innerWidth:550, innerHeight:330, href:'/?pg=accountLocator&autoClose=0&retFunc=addMember', iframe:true, overlayClose:false} );
						}
						function resizeBox(newW,newH) { $.colorbox.resize({innerWidth:newW,innerHeight:newH});}
						function addMember(memObj) {
							$.colorbox.close();
							assignMemberData(memObj);
						}
						function assignMemberData(memObj){
							var thisForm = document.forms["#variables.formName#"];
							var er_change = function(r) {
								var results = r;
								if( results.success ){
									
									AJAXGetRecordType(results.memberid);
									
									$('##memberID').val(results.memberid);
									$('##firstName').val(results.firstname);
									$('##middleName').val(results.middlename);
									$('##lastName').val(results.lastname);
									$('##suffix').val(results.suffix);
									$('##firmName').val(results.company);
									$('##phone').val(results.phone);
									$('##email').val(results.email);
									$('##address').val(results.address1);
									$('##address2').val(results.address2);
									$('##city').val(results.city);
									$('##stateID').val(results.stateid);
									$('##zip').val(results.postalcode);
									$('##website').val(results.website);
									$('##firm_name').val(results.company);
									$('##amount').val("");
									$('##divTotals').html("");
									$('##memberNumber').val(results.membernumber);									
									
									$('##formToFill').show();
								}
							};

							$('##isNewRecord').val(memObj.isNewRecord);

							var objParams = { memberNumber:memObj.memberNumber };
							TS_AJX('MEMBER','getMemberDataByMemberNumber',objParams,er_change,er_change,1000000,er_change);
						}	
						function _FB_validateForm(stepNum) {
							hideAlert();
							var arrReq = new Array();
							var todayDate = "#dateFormat(now(),'mm/dd/yyyy')#";							
							if ($('##contactType').val().length == 0) arrReq[arrReq.length] 		= 'Select your Contact Type.';
							if ($('##firstName').val().length == 0) arrReq[arrReq.length] = 'Enter your First Name.';
							if ($('##lastName').val().length == 0) arrReq[arrReq.length] 	= 'Enter your Last Name.';
							if ($('##phone').val().length == 0) arrReq[arrReq.length] 		= 'Enter your Telephone Number.';
							if ($('##email').val().length == 0) arrReq[arrReq.length] 		= 'Enter your Email Address.';
							if ($('##job_title').val().length == 0) arrReq[arrReq.length] 		= 'Enter your Job Title.';
							var current_val = $('##contactType').val();
							
							if(current_val.length != 0 && $.inArray(current_val,allowed_arr) > -1){
								if ($('##barDate').val().length == 0) arrReq[arrReq.length] 		= 'Enter your Bar Admission Date.';
							}
							if ($('##practiceType').val().length == 0) arrReq[arrReq.length] 		= 'Select your Practice Type.';
							if ($('##firm_name').val().length == 0) arrReq[arrReq.length] 		= 'Enter your Business Name.';
							if ($('##website').val().length == 0) arrReq[arrReq.length] 		= 'Enter your Website.';	
							if ($('##firm_address').val().length == 0) arrReq[arrReq.length] 		= 'Enter your Firm Street Address.';
							if ($('##firm_city').val().length == 0) arrReq[arrReq.length] 		= 'Enter your Firm City.';
							if ($('##firm_stateID').val().length == 0) arrReq[arrReq.length] 		= 'Enter your Firm State.';
							if( $('##countyName').val().length == 0) arrReq[arrReq.length] 		= 'Select your Firm County.';
							if ($('##firm_zip').val().length == 0) arrReq[arrReq.length] 		= 'Enter your Firm Postal Code.';
							if ($('##firm_fax').val().length == 0) arrReq[arrReq.length] 		= 'Enter your Firm Fax Number.';
							if ($('##cell_phone').val().length == 0) arrReq[arrReq.length] 		= 'Enter your Cell Phone Number.';
							if($('##expertiseArea').val() == null) arrReq[arrReq.length] 		= 'Select your Area of Expertise.';
							
							if (arrReq.length > 0) {
								var msg = 'Please address the following issues with your application:<br/>';
								for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
								showAlert(msg);
								$('html,body').animate({scrollTop: $('##issuemsg').offset().top},500);
								return false;
							}

							return true;
						}
						
						function loadMember(memNumber){
							var objParams = { memberNumber:memNumber };
							assignMemberData(objParams);
						}						
						function hideAlert() { $('##issuemsg').html('').hide(); };
						function showAlert(msg) { $('##issuemsg').html(msg).attr('class','alert').show(); };
						function showAlertDone(msg) { $('##issuemsg').html(msg).attr('class','success').show(); };
						function AJAXGetRecordType(member){
							var returnVar = "";
							var stopProgress = function(r){
								if(r.data.isorganization[0] == true){
									window.location.href = "#variables.baselink#&msg=2";
								}
							};
							
							var params = { memberID:member };
							TS_AJX('CUSTOM_FORM_UTILITIES','mem_getRecordType',params,stopProgress);
						}								
						
						$(document).ready(function(){
							mca_setupDatePickerField('barDate');
							if (#session.cfcUser.memberData.memberID# > 1){
								AJAXGetRecordType(#session.cfcUser.memberData.memberID#);
							}							
							
							$(".isRequired").hide();
							<cfif val(session.cfcUser.memberData.memberID)>
								$('##formToFill').show();
							</cfif>
							
							<cfif arguments.event.valueExists('sid')>
								<cfif StructKeyExists(local.strMsgCodes,arguments.event.getTrimValue('sid',''))>
									<cfset local.theMessage = local.strMsgCodes[arguments.event.getTrimValue('sid','')].msg>
									<cfset local.isErr = local.strMsgCodes[arguments.event.getTrimValue('sid','')].err>
								<cfelse>
									<cfset local.theMessage = "We've run into issues recording your submission. Please try again.">
									<cfset local.isErr = 1>
								</cfif>
								<cfif local.isErr>
									showAlert('#jsStringFormat(local.theMessage)#');
								<cfelse>
									showAlertDone('#jsStringFormat(local.theMessage)#');
									$('##formTable').hide();
								</cfif>
							</cfif>
							$('##contactType').change(function(){
								var current_val = $(this).val();
								if($.inArray(current_val,allowed_arr) > -1){
									$('.bar_tr').show();
								}else{
									$('.bar_tr').hide();
								}
							});
							$("##expertiseArea").multiselect({
								<!---header: "Select options",--->
								multiple: true,
								header: false,							
								selectedList: 2,
								minWidth: 300
							});		
							$("##expertiseArea").multiselect("uncheckAll");						
						});
					</script>
					</cfoutput>
				</cfsavecontent>
				<cfhtmlhead text="#local.headCode#">
				<cfsavecontent variable="local.returnHTML">
					<cfoutput>
					<div style="clear:both;"><br/><br/></div>
					<div id="customPage">							
						<div id="issuemsg" style="display:none;margin:6px 0 10px 0;"></div>
						<div id="formTable">
							<cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return _FB_validateForm(2);">
							<cfinput type="hidden" name="fa" id="fa" value="processStep1">
							<cfinput type="hidden" name="memberID" id="memberID" value="#session.cfcUser.memberData.memberID#">
							<cfinput type="hidden" name="memberNumber" id="memberNumber" value="#session.cfcUser.memberData.memberNumber#">
							<cfinput type="hidden" name="isNewRecord" id="isNewRecord" value="">	

							<!--- ACCOUNT LOCATOR --->
							<cfif NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
								<div class="CPSection step1">
									<div class="CPSectionTitle NVTitle BB">Create New Account</div>
									<div class="frmRow1" style="padding:10px;">
										<table cellspacing="0" cellpadding="2" border="0" width="100%">
										<tr>
											<td width="175" class="c">
												<div id="associatedMemberIDSelect" style="display: inline; margin-right: 5px;">
													<button name="btnAddAssoc" type="button" class="btn" onClick="selectMember()">Create Account</button>
												</div>
											</td>
											<td>
												<span class="frmText">
													<p style="margin:0px">Click the <b>Create Account</b> button to the left to begin the join process.</p>
												</span>
											</td>
										</tr>
										</table>
									</div>
								</div>
							</cfif>
							
							<div id="formToFill" style="display:none;">
								<div class="newmem-div">
									<h2>IACDL Membership Application</h2>
								</div>
								<div class="CPSection step1">

									<div class="NVTitle  CPSectionTitle BB">Member Information</div>
										<div class="frmRow1 frmText" style="padding:10px;">
											<table width="100%" cellpadding="3" border="0" cellspacing="0">	
											<tr class="frmRow1 BB">
												<td class="r" ><span class="required">*</span>Contact Type:</td>
												<td>
													<select name="contactType" id="contactType">
														<option value="">---Please Select---</option>
														<cfloop query="variables.contactType">
															<option value="#variables.contactType.columnValueString#">#variables.contactType.columnValueString#</option>>
														</cfloop>
													</select>
												</td>
											</tr>								
											<tr class="frmRow1 BB">
												<td class="r" ><span class="required">*</span>First Name:</td>
												<td><cfinput type="text" size="60" name="firstName" id="firstName" value="#session.cfcUser.memberData.firstname#"  /></td>
											</tr>
											<tr class="frmRow2 BB">
												<td class="r" >Middle Name:</td>
												<td><cfinput type="text" size="60" name="middleName" id="middleName" value="#session.cfcUser.memberData.middlename#"  /></td>
											</tr>																	
											<tr class="frmRow1 nvRow BB">
												<td class="r" ><span class="required">*</span>Last Name:</td>
												<td><cfinput type="text" size="60" name="lastName" id="lastName" value="#session.cfcUser.memberData.lastname#"  /></td>
											</tr>
												<tr class="frmRow2 nvRow BB">
												<td class="r" >Suffix:</td>
												<td><cfinput type="text" size="60" name="suffix" id="suufix" value="#session.cfcUser.memberData.suffix#"  /></td>
											</tr>	
											<tr class="frmRow1 BB">	
												<td class="r"><span class="required">*</span>Email:</td>
												<td><cfinput size="60" name="email" id="email" type="text" value="#session.cfcUser.memberData.email#" /></td>
											</tr>	
											<tr class="frmRow2 BB">	
												<td class="r"><span class="required">*</span>Job Title:</td>
												<td><cfinput size="60" name="job_title" id="job_title" type="text" value="" /></td>
											</tr>
											<tr class="frmRow1 BB bar_tr hide">	
												<td class="r"></span>Bar Number:</td>
												<td><cfinput size="60" name="bar_number" id="bar_number" type="text" value="" /></td>
											</tr>
											<tr class="frmRow2 BB bar_tr hide">	
												<td class="r"><span class="required">*</span>Bar Admission Date:</td>
												<!---<td><cfinput size="60" class="datePicker" name="bar_date" id="bar_date" type="text" value="" /></td>--->
												<td><cfinput size="15" class="datePicker" name="barDate" id="barDate" type="text" value="" /></td>
											</tr>
											<tr class="frmRow1 nvRow BB bar_tr hide">	
											<td class="r">Bar State:</td>
											<td>
												<cfselect name="bar_state" id="bar_state">
													<option value=""></option>
														<cfset local.currentCountryID = 1>
														<optgroup label="United States">
														<cfloop query="variables.qryStates">
															<cfif variables.qryStates.countryID eq local.currentCountryID>
																<cfoutput>
																	<option value="#variables.qryStates.stateID#" >#variables.qryStates.stateName# (#variables.qryStates.stateCode#)</option>
																</cfoutput>
															</cfif>
														</cfloop>
														<cfset local.currentCountryID = 2>
														<optgroup label="Canada">
														<cfloop query="variables.qryStates">
															<cfif variables.qryStates.countryID eq local.currentCountryID>
																<cfoutput>
																	<option value="#variables.qryStates.stateID#">#variables.qryStates.stateName# (#variables.qryStates.stateCode#)</option>
																</cfoutput>
															</cfif>
														</cfloop>	
														<cfset local.currentCountryID = 8>
														<optgroup label="M&eacute;xico">
														<cfloop query="variables.qryStates">
															<cfif variables.qryStates.countryID eq local.currentCountryID>
																<cfoutput>
																	<option value="#variables.qryStates.stateID#">#variables.qryStates.stateName# (#variables.qryStates.stateCode#)</option>
																</cfoutput>
															</cfif>
														</cfloop>																											
													</optgroup>
												</cfselect>
											</td>
										</tr>
										<tr class="frmRow2 BB">	
												<td class="r">Who may we thank for referring you?</td>
												<td><cfinput size="60" name="referal_name" id="referal_name" type="text" value="" /></td>
											</tr>
											</table>									
										</div>

									<div class="NVTitle  CPSectionTitle BB">Firm Information</div>
										<div class="frmRow1 frmText" style="padding:10px;">
										<table width="100%" cellpadding="3" border="0" cellspacing="0">	
										<tr class="frmRow1 BB">	
											<td class="r"><span class="required">*</span>Type Of Practice:</td>
											<td>
												<select id="practiceType" name="practiceType">
													<option value="">---Please Select---</option>
													<cfloop query="variables.practiceType">
														<option value="#variables.practiceType.columnValueString#">#variables.practiceType.columnValueString#</option>
													</cfloop>
												</select>
											</td>
										</tr>
										<tr class="frmRow2 BB">	
											<td class="r"><span class="required">*</span>Business Name:</td>
											<td><cfinput size="60" name="firm_name" id="firm_name" type="text" value="#session.cfcUser.memberData.company#" /></td>
										</tr>
										<tr class="frmRow1 BB">	
											<td class="r"><span class="required">*</span>Website:</td>
											<td><cfinput size="25" name="website" id="website" type="text" value="" /></td>
										</tr>
										<tr class="frmRow2 BB">	
											<td class="r"><span class="required">*</span>Address:</td>
											<td><cfinput size="60" name="firm_address" id="firm_address" type="text" value="#local.memberData.firmAddress.address1#" /></td>
										</tr>
										<tr class="frmRow1 nvRow BB">	
											<td class="r">&nbsp;</td>
											<td><cfinput size="60" name="firm_address2" id="firm_address2" type="text" value="#local.memberData.firmAddress.address2#" /></td>
										</tr>
										<tr class="frmRow2 BB">	
											<td class="r"><span class="required">*</span>City:</td>
											<td><cfinput size="25" name="firm_city" id="firm_city" type="text" value="#local.memberData.firmAddress.city#" /></td>
										</tr>
										<tr class="frmRow1 nvRow BB">	
											<td class="r"><span class="required">*</span>State:</td>
											<td>
												<cfselect name="firm_stateID" id="firm_stateID">
													<option value=""></option>
														<cfset local.currentCountryID = 1>
														<optgroup label="United States">
														<cfloop query="variables.qryStates">
															<cfif variables.qryStates.countryID eq local.currentCountryID>
																<cfoutput>
																	<option value="#variables.qryStates.stateID#" <cfif local.memberData.firmAddress.StateCode eq variables.qryStates.stateCode>selected="true"</cfif>>#variables.qryStates.stateName# (#variables.qryStates.stateCode#)</option>
																</cfoutput>
															</cfif>
														</cfloop>
														<cfset local.currentCountryID = 2>
														<optgroup label="Canada">
														<cfloop query="variables.qryStates">
															<cfif variables.qryStates.countryID eq local.currentCountryID>
																<cfoutput>
																	<option value="#variables.qryStates.stateID#" <cfif local.memberData.firmAddress.StateCode eq variables.qryStates.stateCode>selected="true"</cfif>>#variables.qryStates.stateName# (#variables.qryStates.stateCode#)</option>
																</cfoutput>
															</cfif>
														</cfloop>	
														<cfset local.currentCountryID = 8>
														<optgroup label="M&eacute;xico">
														<cfloop query="variables.qryStates">
															<cfif variables.qryStates.countryID eq local.currentCountryID>
																<cfoutput>
																	<option value="#variables.qryStates.stateID#" <cfif local.memberData.firmAddress.StateCode eq variables.qryStates.stateCode>selected="true"</cfif>>#variables.qryStates.stateName# (#variables.qryStates.stateCode#)</option>
																</cfoutput>
															</cfif>
														</cfloop>																											
													</optgroup>
												</cfselect>
											</td>
										</tr>
										<tr class="frmRow1 BB">	
											<td class="r"><span class="required">*</span>County:</td>
											<td>
												<select id="countyName" name="countyName">
													<option value="">---Please Select---</option>
													<cfloop query="variables.county">
														<option value="#variables.county.columnValueString#">#variables.county.columnValueString#</option>
													</cfloop>
												</select>
											</td>
										</tr>										
										<tr class="frmRow2 BB">	
											<td class="r"><span class="required">*</span>Postal Code:</td>
											<td><cfinput size="10" maxlength="15" name="firm_zip" id="firm_zip" type="text" value="#local.memberData.firmAddress.postalCode#" /></td>
										</tr>
										
										<tr class="frmRow1 BB">	
											<td class="r"><span class="required">*</span>Fax:</td>
											<td><cfinput size="25" name="firm_fax" id="firm_fax" type="text" value="#local.memberData.fax.phone#" /></td>
										</tr>
										</table>
									</div>
									<div class="NVTitle  CPSectionTitle BB">Home Information</div>
										<div class="frmRow1 frmText" style="padding:10px;">
										<table width="100%" cellpadding="3" border="0" cellspacing="0">	

										<tr class="frmRow1 BB">	
											<td class="r">Address:</td>
											<td><cfinput size="60" name="address" id="address" type="text" value="#local.memberData.homeAddress.address1#" /></td>
										</tr>
										<tr class="frmRow2 nvRow BB">	
											<td class="r">&nbsp;</td>
											<td><cfinput size="60" name="address2" id="address2" type="text" value="#local.memberData.homeAddress.address2#" /></td>
										</tr>
										<tr class="frmRow1 BB">	
											<td class="r">City:</td>
											<td><cfinput size="25" name="city" id="city" type="text" value="#local.memberData.homeAddress.city#" /></td>
										</tr>
										<tr class="frmRow2 nvRow BB">	
											<td class="r">State:</td>
											<td>
												<cfselect name="stateID" id="stateID">
													<option value=""></option>
														<cfset local.currentCountryID = 1>
														<optgroup label="United States">
														<cfloop query="variables.qryStates">
															<cfif variables.qryStates.countryID eq local.currentCountryID>
																<cfoutput>
																	<option value="#variables.qryStates.stateID#" <cfif local.memberData.homeAddress.StateCode eq variables.qryStates.stateCode>selected="true"</cfif>>#variables.qryStates.stateName# (#variables.qryStates.stateCode#)</option>
																</cfoutput>
															</cfif>
														</cfloop>
														<cfset local.currentCountryID = 2>
														<optgroup label="Canada">
														<cfloop query="variables.qryStates">
															<cfif variables.qryStates.countryID eq local.currentCountryID>
																<cfoutput>
																	<option value="#variables.qryStates.stateID#" <cfif local.memberData.homeAddress.StateCode eq variables.qryStates.stateCode>selected="true"</cfif>>#variables.qryStates.stateName# (#variables.qryStates.stateCode#)</option>
																</cfoutput>
															</cfif>
														</cfloop>	
														<cfset local.currentCountryID = 8>
														<optgroup label="M&eacute;xico">
														<cfloop query="variables.qryStates">
															<cfif variables.qryStates.countryID eq local.currentCountryID>
																<cfoutput>
																	<option value="#variables.qryStates.stateID#" <cfif local.memberData.homeAddress.StateCode eq variables.qryStates.stateCode>selected="true"</cfif>>#variables.qryStates.stateName# (#variables.qryStates.stateCode#)</option>
																</cfoutput>
															</cfif>
														</cfloop>																											
													</optgroup>
												</cfselect>
											</td>
										</tr>
										<tr class="frmRow1 BB">	
											<td class="r">Postal Code:</td>
											<td><cfinput size="10" maxlength="15" name="zip" id="zip" type="text" value="#local.memberData.homeAddress.postalCode#" /></td>
										</tr>
										<tr class="frmRow2 nvRow BB">	
											<td class="r"><span class="required">*</span>Phone:</td>
											<td><cfinput size="13" maxlength="13" name="phone" id="phone" type="text" value="#local.memberData.phone.phone#" /> <span class="info">(XXX-XXX-XXXX)</span></td>
										</tr>	
										<tr class="frmRow1 nvRow BB">	
											<td class="r"><span class="required">*</span>Cell Phone:</td>
											<td><cfinput size="13" maxlength="13" name="cell_phone" id="cell_phone" type="text" value="" /> <span class="info">(XXX-XXX-XXXX)</span></td>
										</tr>																
																											
																																													
										</table>
									</div>
									<div class="NVTitle CPSectionTitle BB">Choose Membership Level</div>

									<div class="frmRow1 frmText" style="padding:10px;">

										<table width="100%" cellpadding="3" border="0" cellspacing="0">	
										<tr class="frmRow2"><td colspan="5" class="BB">Please Choose one of the following membership Levels</td></tr>
										<tr class="frmRow2">
											<td class="c P"><input checked="checked" type="radio" value="1" name="amount" /></td>
											<td>$2500</td>
											<td class="">Lifetime Member</td>
										</tr>
										<tr class="frmRow1">
											<td class="c P"><input type="radio" value="2" name="amount" /></td>
											<td>$500 per year </td>
											<td class="">Sustaining Member( for 5 years to fulfill Lifetime Members status)</td>
										</tr>
										<tr class="frmRow2">
											<td class="c P"><input type="radio" value="3" name="amount" /></td>
											<td>$195 per year </td>
											<td class="">Private Attorney</td>
										</tr>
										<tr class="frmRow1">
											<td class="c P"><input type="radio" value="4" name="amount" /></td>
											<td>$100 per year</td>
											<td class="">Full-Time Public Defender & Full-Time Law Professor</td>
										</tr>
										<tr class="frmRow2">
											<td class="c P"><input type="radio" value="5" name="amount" /></td>
											<td>$125 per year</td>
											<td class="">Associate (Investigator, Judges, trial consultants, mitigation specialists, forensics experts, paralegals, etc. )</td>
										</tr>
										<tr class="frmRow1">
											<td class="c P"><input type="radio" value="6" name="amount" /></td>
											<td>$75 per year</td>
											<td class="">Young Members (lawyers admitted to the bar for fewer than 3 years)</td>
										</tr>
										<tr class="frmRow2">
											<td class="c P"><input type="radio" value="7" name="amount" /></td>
											<td>$50 per year </td>
											<td class="">Law Student</td>
										</tr>
										</table>
									</div>
									<div class="NVTitle CPSectionTitle BB">Choose Your Area of Expertise</div>
									<div class="frmRow1 frmText" style="padding:10px;">
										<table width="100%" cellpadding="3" border="0" cellspacing="0">	
											<tr class="frmRow1">
												<td class="r" ><span class="required">*</span>Indicate your area(s) of expertise:</td>
												<td style="padding-left:15px;">
													<select name="expertiseArea" id="expertiseArea" multiple="multiple" class="tsAppBodyText" size="5" >
														<cfloop query="variables.expertiseAreas">
															<option value="#variables.expertiseAreas.columnValueString#">#variables.expertiseAreas.columnValueString#</option>
														</cfloop>
													</select>
												</td>
											</tr>
										</table>
									</div>
								</div>								

								<div class="frmButtons step1">
									<button type="submit" name="btnToStep2" class="btn" onClick="return _FB_validateForm(1);">Continue</button>
								</div>

								<div class="frmButtons step2" style="display:none;">
									<button type="button" class="btn" name="btnBack" onClick="changeStep(1);">Back</button>
									<button type="submit" class="btn" name="btnSubmit">Continue</button>
								</div>
							</div> 
							</cfform>
						</div> <!---// id="formTable" --->							
					</div>	<!---// id="customPage" --->
					</cfoutput>
				</cfsavecontent>
			</cfdefaultcase>
		</cfswitch>

		<cfreturn returnAppStruct(local.returnHTML,"echo")>
	</cffunction>	

	<cffunction name="processStep2" access="private" output="false" returntype="Query">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>

		<!--- ensure card is selected --->
		<cfif NOT val(arguments.event.getTrimValue('p_#variables.profile_1._profileID#_mppid','0')) >
			<cflocation url="#variables.baselink#&fa=showForm&sid=50BA4017-F01F-AF51-C9CCE480104528F0" addtoken="no">
		</cfif>

		<!--- UPDATE MEMBER RECORD  --->
		<cfset local.updateMemberNumber = event.getValue('memberNumber','')>
		<cftry>
			<cfset local.recordUpdated = false>	
			<cfset local.newMemIdArr = application.mcCacheManager.sessionGetValue(keyname='newMemIdArr', defaultValue=arrayNew(1))>	
			<cfif IsArray(local.newMemIdArr) AND listFind(ArrayToList(local.newMemIdArr),arguments.event.getTrimValue('memberid'))>		
					
				<cfscript>
				local.objSaveMember = application.objCustomPageUtils.mem_objSaveMember(memberID=arguments.event.getTrimValue('memberid'));
				local.objSaveMember.setDemo(prefix=arguments.event.getValue('prefix',''),firstName=arguments.event.getValue('firstName',''),
					middleName=arguments.event.getValue('middleName',''), lastName=arguments.event.getValue('lastName',''),company=arguments.event.getValue('company_name',''));
				if (len(arguments.event.getValue('email')))
					local.objSaveMember.setEmail(type='Email', value=arguments.event.getValue('email'));
				local.objSaveMember.setWebsite(type='Website', value=arguments.event.getValue('website'));
			    local.objSaveMember.setAddress(type='Home Address', address1=arguments.event.getValue('address',''),
			    	address2=arguments.event.getValue('address2',''), city=arguments.event.getValue('city',''), 
			    	stateID=arguments.event.getValue('stateID',0), postalCode=arguments.event.getValue('zip',''));
				local.objSaveMember.setAddress(type='Address', address1=arguments.event.getValue('firm_address',''),
					address2=arguments.event.getValue('firm_address2',''), city=arguments.event.getValue('firm_city',''), 
					stateID=arguments.event.getValue('firm_stateID',0), postalCode=arguments.event.getValue('firm_zip',''));
			    local.objSaveMember.setPhone(addressType='Home Address',type='Phone',value=event.getValue('phone',''));
			    local.objSaveMember.setPhone(addressType='Address',type='Fax',value=event.getValue('firm_fax',''));
			    local.objSaveMember.setPhone(addressType='Home Address',type='Cell Phone', value=event.getValue('cell_phone',''));
			    local.objSaveMember.setRecordType(recordType='Individual');
			    local.objSaveMember.setMemberType(memberType='User');

				if (len(event.getValue('contactType','')))
					local.objSaveMember.setCustomField(field='Contact Type', value=event.getValue('contactType'));
				if (len(event.getValue('practiceType','')))
					local.objSaveMember.setCustomField(field='Type of Practice', value=event.getValue('practiceType'));
				if (len(event.getValue('countyName','')))
					local.objSaveMember.setCustomField(field='County', value=event.getValue('countyName'));
				if(len(event.getValue('expertiseArea','')))
					local.objSaveMember.setCustomField(field='Areas of Expertise', value=event.getValue('expertiseArea'));

				if (len(event.getValue('barDate','')) and len(event.getValue('bar_number','')))
					local.objSaveMember.setProLicense(name='Illinois',status='Active',license=event.getValue('bar_number'), date=event.getValue('barDate',''));
			   
				local.strResult = local.objSaveMember.saveData(runImmediately=1);
				</cfscript>

				<cfset local.recordUpdated = true>
			</cfif>

		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.recordUpdated = false>
		</cfcatch>

		</cftry>
		<cfif arguments.event.getValue('amount') eq 1>					
		 	<cfset local.memAmt = 2500>
		  	<cfset  local.memberShipName = ''>
		<cfelseif arguments.event.getValue('amount') eq 2>
		 	<cfset local.memAmt = 500 	>
		  	<cfset  local.memberShipName = 'Per Year'>
		<cfelseif arguments.event.getValue('amount') eq 3>
		 	<cfset local.memAmt = 195 	>
		  	<cfset  local.memberShipName = 'Per Year'>
		<cfelseif arguments.event.getValue('amount') eq 4>
		 	<cfset local.memAmt = 100 	>
		  	<cfset  local.memberShipName = 'Per Year'>
		<cfelseif arguments.event.getValue('amount') eq 5>
		 	<cfset local.memAmt = 125 	>
		  	<cfset  local.memberShipName = 'Per Year'>
		<cfelseif arguments.event.getValue('amount') eq 6>
		 	<cfset local.memAmt = 75 	>
		  	<cfset  local.memberShipName = 'Per Year'>
		<cfelseif arguments.event.getValue('amount') eq 7>
		 	<cfset local.memAmt = 50 	>
		  	<cfset  local.memberShipName = 'Per Year'>
		</cfif>
		<cfset  local.totalAmount = local.memAmt>
		
		<cfset local.amountToChargeNow = local.totalAmount>
		<!--- get card used for this submission --->
		<cfset local.payMeth = "Credit Card">

		<!--- get statecode from stateid --->
		<cfif len(#event.getValue('stateid',0)#)>
			<cfquery name="local.qryGetState" dbtype="query">
				select stateName
				from variables.qryStates
				where stateID = <cfqueryparam cfsqltype="cf_sql_integer" value="#event.getValue('stateid',0)#">
			</cfquery>	
		</cfif>
		<!--- get statecode from stateid --->
		<cfquery name="local.qryGetFirmState" dbtype="query">
			select stateName
			from variables.qryStates
			where stateID = <cfqueryparam cfsqltype="cf_sql_integer" value="#event.getValue('firm_stateid',0)#">
		</cfquery>

		<cfquery name="local.qryGetBarState" dbtype="query">
			select stateName
			from variables.qryStates
			where stateID = <cfqueryparam cfsqltype="cf_sql_integer" value="#val(event.getValue('bar_state',0))#">
		</cfquery>	

		<!--- construct email / confirmation --->
		<cfsavecontent variable="local.pageCSS">
			<cfoutput>
			<style type="text/css">
				body { font-size:12pt; font-family: Calibri,Arial,Helvetica; }
				.customPage { font-size:12pt; font-family: Calibri,Arial,Helvetica; }
				p { font-size:12pt; font-family: Calibri,Arial,Helvetica; }
				.msgHeader{ background:##224563; color:##fff; font-weight:bold; padding:5px;}
				.msgHeader td{padding: 2px}
				.frmText{ font-size:12pt; color:##505050; }
				.b{ font-weight:bold; }
				.l{text-align: left;}
				.P{padding: 10px;}
			</style>
			</cfoutput>
		</cfsavecontent>	

		<cfsavecontent variable="local.invoice">
			<cfoutput>	
			<!-- @accResponseMessage@ -->
			<p>#variables.formNameDisplay# submitted on #dateformat(now(),"dddd, m/d/yyyy")# #timeformat(now(),"h:mm tt")#.</p>
			<table cellpadding="2" cellspacing="0" width="100%" border="1" class="customPage">			
			<tr class="msgHeader"><td colspan="2" class="b">CONTACT INFORMATION</td></tr>

			<tr ><td class="frmText b">MemberNumber:</td><td class="frmText">#local.updateMemberNumber#&nbsp;</td></tr>
			<tr ><td class="frmText b">Contact Type:</td><td class="frmText">#event.getValue('contactType','')#&nbsp;</td></tr>
			<tr ><td class="frmText b">First Name:</td><td class="frmText">#event.getValue('firstName','')#&nbsp;</td></tr>	
			<tr ><td class="frmText b">Middle Name:</td><td class="frmText">#event.getValue('middleName','')#&nbsp;</td></tr>				
			<tr ><td class="frmText b">Last Name:</td><td class="frmText">#event.getValue('lastName','')#&nbsp;</td></tr>
			<tr ><td class="frmText b">Suffix:</td><td class="frmText">#event.getValue('suffix','')#&nbsp;</td></tr>
			<tr ><td class="frmText b">Email:</td><td class="frmText">#event.getValue('email','')#&nbsp;</td></tr>
			<tr ><td class="frmText b">Job Title :</td><td class="frmText">#event.getValue('job_title','')#&nbsp;</td></tr>
			<tr ><td class="frmText b">Bar Number :</td><td class="frmText">#event.getValue('bar_number','')#&nbsp;</td></tr>
			<tr ><td class="frmText b">Bar Admission Date :</td><td class="frmText">#event.getValue('barDate','')#&nbsp;</td></tr>
			<tr ><td class="frmText b">Bar State :</td><td class="frmText">#local.qryGetBarState.stateName#&nbsp;</td></tr>
			<tr ><td class="frmText b">Who may we thank for referring you?:</td><td class="frmText">#event.getValue('referal_name','')#&nbsp;</td></tr>
			<tr ><td class="frmText b">Area of Expertise :</td><td class="frmText">#event.getValue('expertiseArea','')#&nbsp;</td></tr>
			<tr class="msgHeader"><td colspan="2" class="b">FIRM CONTACT INFORMATION</td></tr>
			<tr><td class="frmText b">Type Of Practice:</td><td class="frmText">#event.getValue('practiceType','')#&nbsp;</td></tr
			<tr><td class="frmText b">Business Name:</td><td class="frmText">#event.getValue('firm_name','')#&nbsp;</td></tr>
			<tr><td class="frmText b">Website:</td><td class="frmText">#event.getValue('website','')#&nbsp;</td></tr>
			<tr><td class="frmText b">Address:</td><td class="frmText">#event.getValue('firm_address','')#&nbsp;</td></tr>
			<tr><td class="frmText b"></td><td class="frmText">#event.getValue('firm_address2','')#&nbsp;</td></tr>
			<tr><td class="frmText b">City:</td><td class="frmText">#event.getValue('firm_city','')#&nbsp;</td></tr>
			<tr><td class="frmText b">State:</td><td class="frmText">
				#local.qryGetFirmState.stateName#&nbsp;
			</td></tr>
			<tr><td class="frmText b">Postal Code:</td><td class="frmText">#event.getValue('firm_zip','')#&nbsp;</td></tr>			
			<tr><td class="frmText b">County:</td><td class="frmText">#event.getValue('countyName','')#&nbsp;</td></tr>			
			<tr><td class="frmText b">Fax:</td><td class="frmText">#event.getValue('firm_fax','')#&nbsp;</td></tr>
			<tr class="msgHeader"><td colspan="2" class="b">HOME CONTACT INFORMATION</td></tr>
			<tr><td class="frmText b">Address:</td><td class="frmText">#event.getValue('address','')#&nbsp;</td></tr>
			<tr><td class="frmText b"></td><td class="frmText">#event.getValue('address2','')#&nbsp;</td></tr>
			<tr><td class="frmText b">City:</td><td class="frmText">#event.getValue('city','')#&nbsp;</td></tr>
			<tr><td class="frmText b">State:</td><td class="frmText">
			<cfif len(#event.getValue('stateid',0)#)>
			#local.qryGetState.stateName#&nbsp;
			</cfif>
			</td></tr>
			<tr><td class="frmText b">Postal Code:</td><td class="frmText">#event.getValue('zip','')#&nbsp;</td></tr>
			<tr><td class="frmText b">Phone:</td><td class="frmText">#event.getValue('phone','')#&nbsp;</td></tr>
			<tr><td class="frmText b">Cell Phone:</td><td class="frmText">#event.getValue('cell_phone','')#&nbsp;</td></tr>
				
			<tr><td colspan="2">&nbsp;</td></tr>
			<tr class="msgHeader"><td colspan="2" class="b">CONTRIBUTION INFORMATION</td></tr>
	
			<tr><td class="frmText b"><b>Total Charge:</b> &nbsp;</td><td class="frmText"><b>#dollarFormat(local.totalAmount)# #local.memberShipName#</b>&nbsp;</td></tr>			
			<tr><td colspan="2">&nbsp;</td></tr>
			<tr>
				<td class="frmText b">Contribution Pay Method:</td><td class="frmText">
					<cfif event.getValue('payMeth','CC') EQ 'CC'>
						Credit Card
						<cfset arguments.event.setValue('p_#variables.profile_1._profileID#_mppid',int(val(arguments.event.getValue('p_#variables.profile_1._profileID#_mppid',0)))) />
						<cfif arguments.event.getValue('p_#variables.profile_1._profileID#_mppid') gt 0>
							<cfset local.qrySavedInfoOnFile = application.objPayments.getSavedInfoOnFile(
									mppid     = arguments.event.getValue('p_#variables.profile_1._profileID#_mppid'),
									memberID  = val(event.getValue('memberID',0)),
									profileID = int(variables.profile_1._profileID)) />
							- #local.qrySavedInfoOnFile.detail#
						</cfif>						
					
					</cfif>	
				</td>
			</tr>
			</table>
			</cfoutput>
		</cfsavecontent>
		
		<!--- email submitter (no error shown to user) --->
		<cfsavecontent variable="local.mailContent">
			<cfoutput>
				#local.pageCSS#
				<p>Thank you! Please print this page - it is your confirmation.</p><hr/>
				#local.invoice#	
			</cfoutput>
		</cfsavecontent>
		
		<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
			emailfrom={ name="", email=variables.strEMailSettings_member.from },
			emailto=[
				{ name="", email=variables.strEMailSettings_member.to }
			],
			emailreplyto=variables.strEMailSettings_staff.to,
			emailsubject=variables.strEMailSettings_member.subject,
			emailtitle = variables.strPageFields.staffEmailTitle,
			emailhtmlcontent = local.mailContent,
			siteID = variables.siteID,
			memberID = event.getValue('memberID',0),
			messageTypeID = application.objCustomPageUtils.getMessageTypeID(),
			sendingSiteResourceID = this.siteResourceID
		)/>
		
		<cfset local.emailSentToUser = local.responseStruct.success>
		
		<!--- email staff (no error shown to user) --->

		<cfsavecontent variable="local.mailContent">
			<cfoutput>
				<cfif NOT local.emailSentToUser>
					<p><b>The member was NOT sent an e-mail confirmation of this submission.</b></p>
				</cfif>
				<cfif NOT local.recordUpdated>
					<p><b>The member's record was NOT updated in Control Panel with any changes made on this application.</b></p>
				</cfif>
				#local.pageCSS#
				
				#local.invoice#	
			</cfoutput>
		</cfsavecontent>
		<cfscript>
			local.arrEmailTo = [];
			variables.strEMailSettings_staff.to = replace(variables.strEMailSettings_staff.to,",",";","all");
			local.toEmailArr = listToArray(variables.strEMailSettings_staff.to,';');
			for (local.i=1; local.i lte arrayLen(local.toEmailArr); local.i++) {
				local.arrEmailTo.append({ name:'', email:local.toEmailArr[local.i] });
			}
		</cfscript>
		<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
			emailfrom={ name="", email=variables.strEMailSettings_staff.from},
			emailto=local.arrEmailTo,
			emailreplyto=variables.strEMailSettings_staff.from,
			emailsubject=variables.strEMailSettings_staff.subject,
			emailtitle=arguments.event.getTrimValue('mc_siteinfo.sitename') & " - " & variables.formNameDisplay,
			emailhtmlcontent=local.mailContent,
			siteID=variables.siteID,
			memberID=arguments.event.getTrimValue('mc_siteinfo.sysMemberID'),
			messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
			sendingSiteResourceID=this.siteResourceID
		)>

		<!--- relocate to message page --->
		<cfset session.invoice = replaceNoCase(replaceNoCase(replaceNoCase(local.invoice,"html>","div>","ALL"),"body>","div>","ALL"),"head>","div>","ALL")>
		<cflocation url="#variables.baselink#&fa=complete" addtoken="false">
	</cffunction>

</cfcomponent>